'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Get the admin user ID to use as createdById
    const adminUserId = await queryInterface.rawSelect('core_users', {
      where: {
        email: '<EMAIL>'
      }
    }, ['id']);

    if (!adminUserId) {
      throw new Error('Admin user not found. Please run user seeder first.');
    }

    // Department data to insert (cleaned and lowercase)
    const departmentNames = [
      'padding section',
      'leather section',
      'maintenance section',
      'conveyor store',
      'store',
      'purchase'
    ];

    // Remove duplicates and create department objects
    const uniqueDepartmentNames = [...new Set(departmentNames)];
    const departmentData = uniqueDepartmentNames.map(name => ({
      name: name.trim().toLowerCase(),
      status: 'active',
      created_by_id: adminUserId,
      created_at: new Date(),
      updated_at: new Date()
    }));

    await queryInterface.bulkInsert('departments', departmentData, {});
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.bulkDelete('departments', null, {});
  }
};
