'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const adminRoleId = await queryInterface.rawSelect('user_roles', {
      where: {
        role: 'admin'
      }
    }, ['id']);

    if (!adminRoleId) {
      throw new Error('Admin role not found. Please run role seeder first.');
    }

    await queryInterface.bulkInsert('core_users', [
      {
        firstName: 'tech',
        lastName: 'inject',
        email: '<EMAIL>',
        mobile: '1234567890',
        firebaseUID: '8lmqEFRx6LWOUOmChGlvECpbbjU2',
        status: 'active',
        roleId: adminRoleId,
        createdById: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
     
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('core_users', null, {});
  }
};