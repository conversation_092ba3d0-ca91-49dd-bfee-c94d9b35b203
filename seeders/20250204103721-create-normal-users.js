'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First create addresses for the normal users
    const addresses = await queryInterface.bulkInsert('address', [
      {
        street: '123 Main St',
        postalCode: '12345',
        city: 'City',
        state: 'State',
        country: 'Country',
        status: 'active',
        createdById: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
    ], { returning: true });

    // Get the single core user with the specified email
    const [coreUser] = await queryInterface.sequelize.query(
      'SELECT id FROM core_users WHERE email = \'<EMAIL>\'',
      {
        type: Sequelize.QueryTypes.SELECT
      }
    );

    // Create a normal user linking it to the core user and address
    await queryInterface.bulkInsert('normal_users', [{
      coreUserId: coreUser.id,
      addressId: addresses[0].id
    }], {});
  },

  async down(queryInterface, Sequelize) {
    // Delete in reverse order to maintain referential integrity
    await queryInterface.bulkDelete('normal_users', null, {});
    await queryInterface.bulkDelete('address', null, {});
  }
};
