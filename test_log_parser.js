const fs = require('fs');
const path = require('path');
const { createReadStream } = require('fs');
const { createInterface } = require('readline');

// Simple log parser to test locally
class TestLogParser {
    static parseLogLine(line) {
        try {
            // Skip empty lines
            if (!line.trim()) return null;
            
            // Try to parse as JSON
            const logEntry = JSON.parse(line);
            
            // Validate required fields
            if (!logEntry.timestamp || !logEntry.request || !logEntry.response) {
                return null;
            }
            
            return logEntry;
        } catch (error) {
            console.log(`Failed to parse line: ${line.substring(0, 100)}...`);
            return null;
        }
    }
    
    static async parseLogStreamWithPagination(filePath, page = 1, pageSize = 50) {
        return new Promise((resolve, reject) => {
            const logs = [];
            let lineCount = 0;
            let validLogCount = 0;
            
            const fileStream = createReadStream(filePath);
            const rl = createInterface({
                input: fileStream,
                crlfDelay: Infinity
            });
            
            rl.on('line', (line) => {
                lineCount++;
                const logEntry = this.parseLogLine(line);
                
                if (logEntry) {
                    validLogCount++;
                    logs.push(logEntry);
                }
                
                // Stop early if we have enough logs for testing
                if (logs.length >= 100) {
                    rl.close();
                }
            });
            
            rl.on('close', () => {
                console.log(`Processed ${lineCount} lines, found ${validLogCount} valid log entries`);
                
                // Apply pagination
                const startIndex = (page - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                const paginatedLogs = logs.slice(startIndex, endIndex);
                
                const totalPages = Math.ceil(logs.length / pageSize);
                
                const result = {
                    logs: paginatedLogs,
                    pagination: {
                        currentPage: page,
                        totalPages: totalPages,
                        totalData: logs.length,
                        pageSize: pageSize,
                        hasNextPage: page < totalPages,
                        hasPreviousPage: page > 1
                    }
                };
                
                resolve(result);
            });
            
            rl.on('error', (error) => {
                reject(error);
            });
        });
    }
}

async function testLogParsing() {
    try {
        // Find the most recent log file with new naming convention
        const logsDir = path.join(__dirname, 'logs');
        const files = fs.readdirSync(logsDir);
        const logFiles = files.filter(file => file.endsWith('-api.log')).sort().reverse();
        
        if (logFiles.length === 0) {
            console.log('No log files found with pattern *-api.log');
            return;
        }
        
        const logFilePath = path.join(logsDir, logFiles[0]);
        console.log(`Using log file: ${logFiles[0]}`);
        
        // Check if file exists
        if (!fs.existsSync(logFilePath)) {
            console.log('Log file not found:', logFilePath);
            return;
        }
        
        // Get file size
        const stats = fs.statSync(logFilePath);
        const fileSizeInMB = stats.size / (1024 * 1024);
        console.log(`Log file size: ${fileSizeInMB.toFixed(2)} MB`);
        
        // Test parsing with small page size
        console.log('Testing log parsing with page size 5...');
        const result = await TestLogParser.parseLogStreamWithPagination(logFilePath, 1, 5);
        
        console.log('Pagination info:', result.pagination);
        console.log('Number of logs returned:', result.logs.length);
        
        // Test JSON stringification
        console.log('Testing JSON.stringify...');
        const jsonString = JSON.stringify(result);
        console.log(`JSON string length: ${jsonString.length} characters`);
        
        // Test with larger page size
        console.log('\nTesting with page size 50...');
        const largerResult = await TestLogParser.parseLogStreamWithPagination(logFilePath, 1, 50);
        
        console.log('Pagination info:', largerResult.pagination);
        console.log('Number of logs returned:', largerResult.logs.length);
        
        const largerJsonString = JSON.stringify(largerResult);
        console.log(`Larger JSON string length: ${largerJsonString.length} characters`);
        
        // Check individual log entry sizes
        if (result.logs.length > 0) {
            const sampleLog = result.logs[0];
            const sampleLogJson = JSON.stringify(sampleLog);
            console.log(`Sample log entry size: ${sampleLogJson.length} characters`);
            
            // Check if any log entries are extremely large
            let maxLogSize = 0;
            let maxLogIndex = 0;
            
            for (let i = 0; i < Math.min(result.logs.length, 10); i++) {
                const logSize = JSON.stringify(result.logs[i]).length;
                if (logSize > maxLogSize) {
                    maxLogSize = logSize;
                    maxLogIndex = i;
                }
            }
            
            console.log(`Largest log entry in sample: ${maxLogSize} characters (index ${maxLogIndex})`);
        }
        
    } catch (error) {
        console.error('Error testing log parsing:', error);
    }
}

// Run the test
testLogParsing(); 