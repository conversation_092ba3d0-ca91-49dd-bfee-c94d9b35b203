# Problematic Stocks Daily Email Report

This system automatically checks for problematic stock entries daily and sends email reports using Resend.com.

## 🚨 What Are Problematic Stocks?

The system identifies three types of problematic stock conditions:

1. **Negative Total Stock**: When total stock quantity is below zero
2. **Negative Usable Stock**: When usable stock quantity is below zero  
3. **Invalid Stock Ratio**: When usable stock exceeds total stock (impossible scenario)

## ⚙️ Configuration

### Environment Variables Required

Add these variables to your `.env` file:

```bash
# Resend Email Configuration
RESEND_API_KEY=re_your_resend_api_key_here
EMAIL_FROM=<EMAIL>

# Email Recipients (comma-separated for multiple recipients)
STOCK_ALERT_RECIPIENTS=<EMAIL>,<EMAIL>
```

### Getting Your Resend API Key

1. Go to [Resend.com](https://resend.com) and create an account
2. Navigate to API Keys section
3. Create a new API key
4. Copy the key (starts with `re_`)

### Email Configuration

- **RESEND_API_KEY**: Your Resend API key for authentication
- **EMAIL_FROM**: The sender email address (must be a verified domain in Resend)

## 📧 Email Recipients

The system sends reports to recipients configured via the `STOCK_ALERT_RECIPIENTS` environment variable:
- **Default Recipients**: `<EMAIL>,<EMAIL>`
- **Configurable**: Add comma-separated email addresses in `.env`
- **Multiple Recipients**: Supports sending to unlimited recipients

**Important**: 
- Emails are only sent when problematic stocks are found. No email is sent if all stocks are healthy.
- In Resend testing mode, only verified email addresses will receive emails
- Each recipient gets an individual email (not CC/BCC) for better delivery tracking

## 🕘 Schedule

The cron job runs **daily at 9:00 AM IST** by default.

### Changing the Schedule

To modify the schedule, edit the cron pattern in `src/jobs/ProblematicStocksCronJob.ts`:

```typescript
// Current: Daily at 9:00 AM
cron.schedule('0 9 * * *', async () => {

// Examples of other schedules:
// '0 8 * * 1-5'    = 8:00 AM on weekdays only
// '0 */12 * * *'   = Every 12 hours
// '0 0 * * 0'      = Weekly on Sunday at midnight
// '0 9 1 * *'      = Monthly on the 1st at 9:00 AM
```

## 🎯 Features

### Email Report Contents

- **Professional HTML Email**: Clean, responsive design with USI branding
- **Detailed Stock Information**: Material name, SKU, category, stock levels
- **Issue Identification**: Clear badges showing specific problems
- **Action Recommendations**: Step-by-step guidance for resolving issues
- **Summary Statistics**: Quick overview of total problematic stocks

### Smart Email Logic

- ✅ **No Spam**: Only sends emails when problems are found
- 🔄 **Duplicate Prevention**: Won't run multiple instances simultaneously
- 📊 **Detailed Logging**: Console output for monitoring and debugging
- 🧪 **Connection Testing**: Validates email service on startup

## 🚀 Usage

### Automatic Operation

The cron job starts automatically when the server starts. You'll see these messages:

```
Starting cron jobs...
🧪 Testing email connection...
Resend API connection verified successfully
🕘 Starting Problematic Stocks Cron Job...
✅ Problematic Stocks Cron Job scheduled successfully
📅 Next execution: Daily at 9:00 AM IST
```

### Manual Testing

To test the functionality immediately, you can add this to your code:

```typescript
import { ProblematicStocksCronJob } from './src/jobs/ProblematicStocksCronJob';

// Create and run immediately
const cronJob = new ProblematicStocksCronJob();
await cronJob.runNow();
```

### Testing Email Connection

```typescript
const cronJob = new ProblematicStocksCronJob();
const isConnected = await cronJob.testEmailConnection();
console.log('Email service status:', isConnected ? 'Connected' : 'Failed');
```

## 📝 Console Output Examples

### When No Issues Found
```
🔄 Running problematic stocks check at 12/6/2024, 9:00:00 AM
🔍 Fetching problematic stocks from database...
📊 Found 0 problematic stock entries
✅ No problematic stocks found. No email will be sent.
✅ Problematic stocks check completed
```

### When Issues Found
```
🔄 Running problematic stocks check at 12/6/2024, 9:00:00 AM
🔍 Fetching problematic stocks from database...
📊 Found 3 problematic stock entries
📋 Problematic stocks details:
  1. Steel Rods (SKU-001) - Issues: Negative Total Stock
  2. Aluminum Sheets (SKU-002) - Issues: Negative Usable Stock
  3. Copper Wire (SKU-003) - Issues: Invalid Stock Ratio
📧 Sending email report...
✅ Problematic stocks report sent successfully! Email ID: abc123
✅ Problematic stocks check completed
```

## 🛠️ Troubleshooting

### Email Not Sending

1. **Check API Key**: Ensure `RESEND_API_KEY` is correct
2. **Verify Domain**: Make sure `EMAIL_FROM` uses a verified domain in Resend
3. **Check Logs**: Look for error messages in console output
4. **Test Connection**: Run the email connection test

### Common Issues

| Issue | Solution |
|-------|----------|
| "Resend API connection failed" | Check your RESEND_API_KEY |
| "Invalid from address" | Verify domain in Resend dashboard |
| "No problematic stocks found" | This is normal - means all stocks are healthy |
| Emails not received | Check spam folder, verify recipient emails |

### Debugging

Enable detailed logging by checking the console output when the cron job runs. All operations are logged with descriptive emojis and messages.

## 🔧 Customization

### Changing Recipients

Edit the email addresses in `src/jobs/ProblematicStocksCronJob.ts`:

```typescript
const emailSent = await this.emailService.sendProblematicStocksReport(
    problematicStocks,
    '<EMAIL>',      // Primary recipient
    '<EMAIL>'            // CC recipient (optional)
);
```

### Modifying Email Template

The HTML email template is in `src/services/EmailService.ts` in the `generateStockReportHTML()` method. You can customize:

- Company branding and colors
- Email structure and layout
- Additional information to include
- Styling and formatting

### Adding More Stock Checks

To add additional problematic stock conditions, modify the query in:
`src/features/raw_material_stock/repositories/PostgresRawMaterialStockRepo.ts`

In the `getProblematicStocks()` method, add new conditions to the `where` clause.

## 📦 Dependencies

- **resend**: Email sending service
- **node-cron**: Cron job scheduling
- **@types/node-cron**: TypeScript definitions

## 🔐 Security

- API keys are stored as environment variables
- No sensitive information is logged
- Email content is sanitized before sending
- Rate limiting prevents email abuse

---

## 📞 Support

For issues or questions about the problematic stocks email system, check:

1. Console logs for detailed error messages
2. Resend dashboard for email delivery status
3. Database for actual problematic stock entries
4. Environment variables configuration

The system is designed to be robust and informative, providing clear feedback about its operation status. 