const { url } = require('inspector');

require('dotenv').config();


const stagingDB = process.env.STAG_DB_URL || "";
const productionDB = process.env.PROD_DB_URL || "";



const development = {
    dialect: 'postgres',
    username: process.env.DEV_DB_USERNAME || "",
    password: process.env.DEV_DB_PASSWORD || "",
    database: process.env.DEV_DB_NAME || "",
    host: process.env.DEV_DB_HOST || "",
};


const testing = {
    url: stagingDB,
    dialectOptions: {
        ssl: {
            require: true,
        }
    },
    dialect: 'postgres',
}

const production = {
    url: productionDB,
    dialectOptions: {
        ssl: {
            require: true,
        }
    },
    dialect: 'postgres',
}


module.exports = {
    development: {
        ...development,
    },
    testing: {
        ...testing,
    },
    production: {
        ...production,
    },
};