import express from 'express';
import { routes } from './src/routes/index_routes';
import { sequelizeInit, WorkingENV } from './src/sequelize_init';
import cors from "cors";
import rateLimit from "express-rate-limit";
import { SequelizeAssociations } from './src/sequelize_associations';
import { Middlewares } from './src/middlewares/middlewares';
import * as admin from 'firebase-admin';
import adminJson from "./FirebaseAdmin.json";

import { DefaultSystemSetup } from './src/scripts/systemSetup';

import {
	consoleLoggingMiddleware,
	errorLoggingMiddleware,
	fileLoggingMiddleware,
	requestResponseLoggingMiddleware
} from './src/middleware/enhancedLoggingMiddleware';
import { initModel } from './src/initModel';
import { ProblematicStocksCronJob } from "./src/jobs/ProblematicStocksCronJob";
import { RepoProvider } from './src/core/RepoProvider';

// //@ts-ignore
// Number.prototype.toJSON = function () {
//   //@ts-ignore
//   return Number(this.toString());
// };

const app = express()
const port = 3004;

app.use(cors({
	origin: '*',
}));

app.use(express.json());

const rateLimiter = rateLimit({
	windowMs: 1 * 60 * 1000,
	max: 200,
	message: "Too many requests, please try after some time.",
});

app.use(rateLimiter);

// Apply logging middleware
// app.use(consoleLoggingMiddleware);
// app.use(fileLoggingMiddleware);
// app.use(requestResponseLoggingMiddleware);
// app.use(errorLoggingMiddleware);

const initApp = () => {

	
	admin.initializeApp({
		credential: admin.credential.cert(adminJson as admin.ServiceAccount)
	});
	sequelizeInit.authenticate()
		.then(async () => {
			console.log(`DB connected successfully in ${WorkingENV} mode`);
			console.log("Preparing server...");
			console.log("Starting redis...");

			/* connect redis */
			await RepoProvider.redisServerRepository.connect();

			initModel();
			await SequelizeAssociations.associate();
			// await sequelizeInit.sync();

			// Setup system: sequences, roles, and default users
			// console.log("Setting up system...");
			// try {
			// 	const setupResult = await DefaultSystemSetup.setupSystem();
			// 	if (setupResult.success) {
			// 		console.log(`✅ ${setupResult.message}`);
			// 	} else {
			// 		console.warn(`⚠️  ${setupResult.message}`);
			// 	}
			// }
			// catch (error) {
			// 	console.error("❌ Failed to setup system:", error);
			// }

			console.log("Configuring api routes...");

			// Apply middleware once for all /api routes
			app.use('/api',
				Middlewares.useAuthMiddleware,
				Middlewares.sessionAuthMiddleware
			);

			// Register all routes without middleware (middleware already applied above)
			for (const route of routes) {
				app.use('/api', route);
			}
			console.log("Server is ready");

			// Initialize and start the problematic stocks cron job
			// console.log("Starting cron jobs...");

			// Configure email recipients from environment variables or defaults
			// const emailRecipients = process.env.STOCK_ALERT_RECIPIENTS
			//   ? process.env.STOCK_ALERT_RECIPIENTS.split(',').map(email => email.trim())
			//   : ['<EMAIL>']; // Default to verified email for Resend testing mode

			// const stocksCronJob = new ProblematicStocksCronJob({
			//   emailRecipients: emailRecipients,
			//   cronPattern: '0 9 * * *', // Daily at 9 AM
			//   timezone: 'Asia/Kolkata',
			//   enabled: true
			// });

			// console.log(`📧 Email recipients configured: ${emailRecipients.join(', ')}`);
			// console.log('📧 Please check your RESEND_API_KEY and EMAIL_FROM environment variables.');
			// // Start the cron job
			// stocksCronJob.startCronJob();

			console.clear();
			app.listen(port, () => {
				console.log(`Server is running on port ${port}`);
				// console.log(`🕘 Problematic Stocks Cron Job is scheduled to run daily at 9:00 AM IST`);
			});
		});
}
export { initApp }
