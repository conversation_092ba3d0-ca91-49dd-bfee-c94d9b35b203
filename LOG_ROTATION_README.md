# 📋 Daily Log Rotation System

## Overview

This project implements **automatic daily log file rotation** for HTTP request logs using Morgan middleware. Each day's logs are stored in separate files with the naming convention `DD-MMM-YY-api.log`.

## 🗂️ Log File Naming Convention

- **Format**: `DD-MMM-YY-api.log`
- **Examples**: 
  - `05-jun-25-api.log` (June 5, 2025)
  - `23-dec-24-api.log` (December 23, 2024)
  - `01-jan-26-api.log` (January 1, 2026)

## 🔧 Implementation Details

### Middleware Configuration

The logging middleware (`src/middleware/loggingMiddleware.ts`) automatically:
- Creates a new log file each day
- Rotates logs at midnight
- Writes JSON-formatted log entries
- Handles both successful requests and errors

### Key Features

1. **Dynamic File Creation**: Log files are created dynamically based on the current date
2. **JSON Format**: Each log entry is a complete JSON object with request/response details
3. **Error Handling**: Comprehensive error logging with stack traces
4. **Stream Management**: Proper stream handling to prevent memory leaks

## 📊 Log Entry Structure

Each log entry contains:

```json
{
  "timestamp": "2024-06-05T10:30:45.123Z",
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "ip": "*************",
  "request": {
    "timestamp": "2024-06-05T10:30:45.120Z",
    "method": "GET",
    "url": "/api/users",
    "headers": { "content-type": "application/json" },
    "body": null
  },
  "response": {
    "status": 200,
    "headers": { "content-type": "application/json" },
    "body": { "success": true, "data": [...] },
    "responseTime": "45ms"
  },
  "error": false
}
```

## 🌐 API Endpoints (Updated)

All existing API endpoints continue to work exactly as before, but now automatically discover and use the correct log file based on the date parameter.

### Get Logs by Date (Updated)
```http
GET /api/logs/by-date?date=2025-06-05&page=1&pageSize=50
```
- Now automatically finds `05-jun-25-api.log` based on the date parameter
- No changes to request/response format

### Get Available Dates (Updated)
```http
GET /api/logs/dates
```
- Now scans for DD-MMM-YY-api.log files and extracts dates
- Returns same format as before

### Get All Logs by Date (Updated)
```http
GET /api/logs/by-date/all?date=2025-06-05
```
- Now automatically finds the correct log file for the date
- No changes to request/response format

### Get Log Statistics (Updated)
```http
GET /api/logs/stats-by-date?date=2025-06-05
```
- Now automatically finds the correct log file for the date
- No changes to request/response format

## 🔒 Security Considerations

1. **Authentication Required**: All log endpoints require `LOG.READ` permission
2. **Environment Restriction**: Only available in production environment
3. **File Path Validation**: Prevents directory traversal attacks
4. **Filename Validation**: Only allows valid log file formats

## 📁 File Structure

```
logs/
├── 05-jun-25-api.log
├── 04-jun-25-api.log
├── 03-jun-25-api.log
└── ...
```

## 🛠️ Configuration

### Environment Variables

No additional environment variables required. The system works out of the box.

### Permissions

Ensure your application has:
- Write permissions to the `logs/` directory
- Read permissions for serving log files

## 📈 Performance Considerations

1. **Stream Management**: Each write operation creates and closes a stream to ensure proper daily rotation
2. **File Size**: Large log files (>100MB) are handled with streaming parsers
3. **Memory Usage**: Optimized to prevent memory leaks with proper stream handling

## 🧪 Testing

### Test Files

- `test_json_parser.js` - Tests log parsing functionality
- `test_log_parser.js` - Tests pagination and filtering

### Running Tests

```bash
# Test JSON log parsing
node test_json_parser.js

# Test log parsing with pagination
node test_log_parser.js
```

## 🔄 Migration from Old System

### Old Format vs New Format

| Old Format | New Format |
|------------|------------|
| `2024-06-05_api.log` | `05-jun-25-api.log` |
| `api.log` (single file) | Daily rotation |

### Migration Notes

- The system automatically handles the new format
- Old log files are not automatically migrated
- Test files have been updated to use the new format

## 📝 Log Analysis

### Parsing Utilities

The system includes comprehensive log parsing utilities:

- `MorganLogParser` - Main parser class
- Stream-based parsing for large files
- Filtering and pagination support
- Statistics generation

### Common Queries

```javascript
// Get all logs for a specific date
const logs = await MorganLogParser.parseLogStream('05-jun-25-api.log');

// Filter by HTTP method
const getLogs = logs.filter(log => log.request.method === 'GET');

// Filter by status code
const errorLogs = logs.filter(log => log.response.status >= 400);

// Filter by response time
const slowLogs = logs.filter(log => 
  parseInt(log.response.responseTime) > 1000
);
```

## 🚨 Monitoring and Alerts

### Log File Monitoring

Consider implementing:
- Daily log file size monitoring
- Disk space alerts
- Log parsing error notifications
- Performance degradation alerts

### Cleanup Recommendations

To prevent disk space issues, implement:
- Automatic cleanup of logs older than 30 days
- Log compression for archived files
- Backup strategies for important logs

## 🐛 Troubleshooting

### Common Issues

1. **No log files created**
   - Check write permissions on `logs/` directory
   - Verify middleware is properly configured

2. **Old format files still being created**
   - Ensure you've restarted the application
   - Check for cached modules

3. **API endpoints not working**
   - Verify production environment setting
   - Check user permissions
   - Confirm authentication

### Debug Mode

Enable debug logging by setting appropriate log levels in your application configuration.

## 📚 Additional Resources

- [Morgan Documentation](https://github.com/expressjs/morgan)
- [Stream API Documentation](https://nodejs.org/api/stream.html)
- [Log Analysis Best Practices](https://github.com/your-repo/log-analysis-guide) 