import { IRawMaterialStockDetails } from '../features/raw_material_stock/models/IRawMaterialStock';

/**
 * Configuration interface for email service
 */
export interface IEmailServiceConfig {
    apiKey: string;
    fromEmail: string;
    maxRetries?: number;
    timeout?: number;
}

/**
 * Email recipient interface
 */
export interface IEmailRecipient {
    email: string;
    name?: string;
    type: 'to' | 'cc' | 'bcc';
}

/**
 * Email attachment interface
 */
export interface IEmailAttachment {
    filename: string;
    content: string | Buffer;
    contentType: string;
    size?: number;
}

/**
 * Base email data interface
 */
export interface IEmailData {
    from: string;
    to: string[];
    cc?: string[];
    bcc?: string[];
    subject: string;
    html: string;
    text?: string;
    attachments?: IEmailAttachment[];
    tags?: Record<string, string>;
    headers?: Record<string, string>;
}

/**
 * Email send result interface
 */
export interface IEmailSendResult {
    success: boolean;
    messageId?: string;
    error?: {
        code: string;
        message: string;
        statusCode?: number;
    };
    timestamp: Date;
}

/**
 * Problematic stock issue types
 */
export type ProblematicStockIssueType = 
    | 'NEGATIVE_TOTAL_STOCK' 
    | 'NEGATIVE_USABLE_STOCK' 
    | 'INVALID_STOCK_RATIO' 
    | 'ZERO_STOCK_WITH_ASSIGNMENTS'
    | 'EXCESSIVE_ASSIGNED_STOCK';

/**
 * Individual stock issue interface
 */
export interface IStockIssue {
    type: ProblematicStockIssueType;
    description: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    recommendedAction: string;
}

/**
 * Enhanced problematic stock details
 */
export interface IProblematicStockDetails extends IRawMaterialStockDetails {
    issues: IStockIssue[];
    lastUpdated: Date;
    affectedTransactions?: number;
    estimatedImpact?: {
        financialImpact: number;
        operationalImpact: 'LOW' | 'MEDIUM' | 'HIGH';
    };
}

/**
 * Stock report summary interface
 */
export interface IStockReportSummary {
    totalProblematicStocks: number;
    issueBreakdown: Record<ProblematicStockIssueType, number>;
    severityBreakdown: Record<'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL', number>;
    reportGeneratedAt: Date;
    reportPeriod: {
        startDate: Date;
        endDate: Date;
    };
    totalFinancialImpact?: number;
}

/**
 * Email template context interface
 */
export interface IEmailTemplateContext {
    reportSummary: IStockReportSummary;
    problematicStocks: IProblematicStockDetails[];
    recipients: {
        primary: string[];
        cc: string[];
    };
    systemInfo: {
        systemName: string;
        environment: 'development' | 'staging' | 'production';
        timestamp: Date;
    };
    customizations?: {
        companyLogo?: string;
        companyName?: string;
        brandColors?: {
            primary: string;
            secondary: string;
            accent: string;
        };
    };
}

/**
 * Email template result interface
 */
export interface IEmailTemplateResult {
    html: string;
    subject: string;
    previewText?: string;
    estimatedReadTime?: number;
}

/**
 * Email service interface
 */
export interface IEmailService {
    sendProblematicStocksReport(
        stocks: IProblematicStockDetails[],
        recipients: IEmailRecipient[],
        options?: {
            priority?: 'low' | 'normal' | 'high';
            sendAt?: Date;
            trackOpens?: boolean;
            trackClicks?: boolean;
        }
    ): Promise<IEmailSendResult>;

    generateEmailTemplate(context: IEmailTemplateContext): Promise<IEmailTemplateResult>;
    
    testConnection(): Promise<boolean>;
    
    validateEmailAddress(email: string): boolean;
    
    getDeliveryStatus(messageId: string): Promise<{
        status: 'pending' | 'sent' | 'delivered' | 'bounced' | 'complained';
        timestamp: Date;
        details?: string;
    }>;
}

/**
 * Email configuration validation result
 */
export interface IEmailConfigValidation {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: string[];
}

/**
 * Email metrics interface
 */
export interface IEmailMetrics {
    totalSent: number;
    totalDelivered: number;
    totalBounced: number;
    deliveryRate: number;
    bounceRate: number;
    lastSentAt?: Date;
    averageDeliveryTime?: number;
}

/**
 * Email template configuration
 */
export interface IEmailTemplateConfig {
    templateId: string;
    templateName: string;
    version: string;
    author: string;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    supportedLanguages: string[];
    requiredVariables: string[];
    optionalVariables: string[];
} 