/**
 * Email-related interfaces
 */
export * from './EmailInterfaces';

/**
 * Cron job-related interfaces
 */
export * from './CronJobInterfaces';

/**
 * API-related interfaces
 */
export * from './APIInterfaces';

/**
 * Type utilities and helpers
 */

/**
 * Make all properties of T optional except for the keys specified in K
 */
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;

/**
 * Make all properties of T required except for the keys specified in K
 */
export type RequiredExcept<T, K extends keyof T> = Required<T> & Partial<Pick<T, K>>;

/**
 * Extract the type of an array element
 */
export type ArrayElement<ArrayType extends readonly unknown[]> = 
    ArrayType extends readonly (infer ElementType)[] ? ElementType : never;

/**
 * Create a union type from the values of an object
 */
export type ValueOf<T> = T[keyof T];

/**
 * Create a type with only the keys of T that have values assignable to U
 */
export type KeysOfType<T, U> = {
    [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

/**
 * Create a deep partial type (makes all nested properties optional)
 */
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends Record<string, unknown>
        ? DeepPartial<T[P]>
        : T[P];
};

/**
 * Create a deep required type (makes all nested properties required)
 */
export type DeepRequired<T> = {
    [P in keyof T]-?: T[P] extends Record<string, unknown>
        ? DeepRequired<T[P]>
        : T[P];
};

/**
 * Utility type for timestamp fields
 */
export interface ITimestamps {
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
}

/**
 * Utility type for user tracking fields
 */
export interface IUserTracking {
    createdById: string;
    updatedById?: string;
    deletedById?: string;
}

/**
 * Combined audit fields
 */
export interface IAuditFields extends ITimestamps, IUserTracking {}

/**
 * Generic entity with ID
 */
export interface IEntity {
    id: string;
}

/**
 * Entity with audit fields
 */
export interface IAuditableEntity extends IEntity, IAuditFields {}

/**
 * Soft delete capability
 */
export interface ISoftDeletable {
    deletedAt?: Date;
    deletedById?: string;
    isDeleted: boolean;
}

/**
 * Versioning capability
 */
export interface IVersionable {
    version: number;
    lastModified: Date;
    modifiedBy: string;
}

/**
 * Status tracking
 */
export interface IStatusTracking {
    status: string;
    statusChangedAt: Date;
    statusChangedBy: string;
    statusHistory?: Array<{
        status: string;
        changedAt: Date;
        changedBy: string;
        reason?: string;
    }>;
}

/**
 * Taggable entity
 */
export interface ITaggable {
    tags: string[];
    taggedAt?: Date;
    taggedBy?: string;
}

/**
 * Searchable entity
 */
export interface ISearchable {
    searchTerms: string[];
    searchableFields: string[];
    lastIndexed?: Date;
}

/**
 * Geographic location
 */
export interface IGeoLocation {
    latitude: number;
    longitude: number;
    accuracy?: number;
    address?: {
        street?: string;
        city?: string;
        state?: string;
        country?: string;
        postalCode?: string;
    };
}

/**
 * File reference
 */
export interface IFileReference {
    fileId: string;
    filename: string;
    originalName: string;
    url: string;
    size: number;
    mimeType: string;
    uploadedAt: Date;
    uploadedBy: string;
}

/**
 * Configuration setting
 */
export interface IConfigSetting {
    key: string;
    value: string | number | boolean | Record<string, unknown>;
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    description: string;
    category: string;
    isSecret: boolean;
    environment: string[];
    lastUpdated: Date;
    updatedBy: string;
}

/**
 * Log level type
 */
export type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';

/**
 * Log entry
 */
export interface ILogEntry {
    level: LogLevel;
    message: string;
    timestamp: Date;
    source: string;
    userId?: string;
    requestId?: string;
    correlationId?: string;
    metadata?: Record<string, unknown>;
    error?: {
        name: string;
        message: string;
        stack?: string;
    };
}

/**
 * Environment type
 */
export type Environment = 'development' | 'testing' | 'staging' | 'production';

/**
 * HTTP methods
 */
export type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

/**
 * Content types
 */
export type ContentType = 
    | 'application/json'
    | 'application/xml'
    | 'text/plain'
    | 'text/html'
    | 'text/csv'
    | 'application/pdf'
    | 'application/octet-stream'
    | 'multipart/form-data';

/**
 * Sort order
 */
export type SortOrder = 'asc' | 'desc';

/**
 * Comparison operators
 */
export type ComparisonOperator = 
    | 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' 
    | 'in' | 'nin' | 'like' | 'regex' | 'between';

/**
 * Currency codes (ISO 4217)
 */
export type CurrencyCode = 'USD' | 'EUR' | 'GBP' | 'JPY' | 'INR' | 'CAD' | 'AUD';

/**
 * Language codes (ISO 639-1)
 */
export type LanguageCode = 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'zh' | 'ja' | 'ko' | 'hi';

/**
 * Timezone identifiers
 */
export type Timezone = 
    | 'UTC' 
    | 'America/New_York' 
    | 'America/Los_Angeles' 
    | 'Europe/London' 
    | 'Europe/Paris' 
    | 'Asia/Tokyo' 
    | 'Asia/Kolkata' 
    | 'Australia/Sydney'; 