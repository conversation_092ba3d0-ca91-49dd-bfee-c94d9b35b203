/**
 * Cron job schedule configuration
 */
export interface ICronSchedule {
    pattern: string;
    timezone: string;
    description: string;
    enabled: boolean;
}

/**
 * Cron job execution context
 */
export interface ICronJobContext {
    jobId: string;
    jobName: string;
    executionId: string;
    startTime: Date;
    scheduledTime: Date;
    previousExecution?: Date;
    nextExecution?: Date;
    attempt: number;
    maxAttempts: number;
}

/**
 * Cron job execution result
 */
export interface ICronJobResult {
    success: boolean;
    executionTime: number; // milliseconds
    message: string;
    data?: Record<string, unknown>;
    error?: {
        code: string;
        message: string;
        stack?: string;
    };
    warnings?: string[];
    metricsCollected?: ICronJobMetrics;
}

/**
 * Cron job configuration
 */
export interface ICronJobConfig {
    jobId: string;
    jobName: string;
    description: string;
    schedule: ICronSchedule;
    maxExecutionTime: number; // milliseconds
    maxRetries: number;
    retryDelay: number; // milliseconds
    concurrent: boolean;
    priority: 'low' | 'normal' | 'high' | 'critical';
    notifications: {
        onSuccess?: boolean;
        onFailure?: boolean;
        onRetry?: boolean;
        recipients?: string[];
    };
    environment: ('development' | 'staging' | 'production')[];
}

/**
 * Cron job metrics
 */
export interface ICronJobMetrics {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    lastExecution?: Date;
    lastSuccessfulExecution?: Date;
    lastFailedExecution?: Date;
    consecutiveFailures: number;
    uptimePercentage: number;
}

/**
 * Cron job status
 */
export type CronJobStatus = 
    | 'SCHEDULED' 
    | 'RUNNING' 
    | 'COMPLETED' 
    | 'FAILED' 
    | 'CANCELLED' 
    | 'DISABLED' 
    | 'PAUSED';

/**
 * Cron job execution log entry
 */
export interface ICronJobLogEntry {
    executionId: string;
    jobId: string;
    status: CronJobStatus;
    startTime: Date;
    endTime?: Date;
    executionTime?: number;
    result?: ICronJobResult;
    context: ICronJobContext;
    resourceUsage?: {
        memoryUsed: number;
        cpuUsed: number;
        diskIO?: number;
    };
}

/**
 * Problematic stocks cron job specific configuration
 */
export interface IProblematicStocksCronConfig extends ICronJobConfig {
    emailConfig: {
        recipients: {
            primary: string[];
            cc?: string[];
            bcc?: string[];
        };
        template: {
            subject: string;
            includeCharts: boolean;
            includeRecommendations: boolean;
            language: 'en' | 'es' | 'fr' | 'de';
        };
        conditions: {
            minimumIssues: number;
            severityThreshold: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
            skipIfNoIssues: boolean;
        };
    };
    stockAnalysis: {
        lookbackDays: number;
        includeHistoricalTrends: boolean;
        categorizeByImpact: boolean;
        includeFinancialImpact: boolean;
    };
}

/**
 * Cron job manager interface
 */
export interface ICronJobManager {
    registerJob(config: ICronJobConfig, handler: ICronJobHandler): Promise<boolean>;
    unregisterJob(jobId: string): Promise<boolean>;
    startJob(jobId: string): Promise<boolean>;
    stopJob(jobId: string): Promise<boolean>;
    pauseJob(jobId: string): Promise<boolean>;
    resumeJob(jobId: string): Promise<boolean>;
    executeJobNow(jobId: string): Promise<ICronJobResult>;
    getJobStatus(jobId: string): Promise<CronJobStatus>;
    getJobMetrics(jobId: string): Promise<ICronJobMetrics>;
    getJobLogs(jobId: string, limit?: number): Promise<ICronJobLogEntry[]>;
    getAllJobs(): Promise<ICronJobConfig[]>;
    getActiveJobs(): Promise<ICronJobConfig[]>;
}

/**
 * Cron job handler interface
 */
export interface ICronJobHandler {
    execute(context: ICronJobContext): Promise<ICronJobResult>;
    validate?(context: ICronJobContext): Promise<boolean>;
    cleanup?(context: ICronJobContext): Promise<void>;
    onError?(error: Error, context: ICronJobContext): Promise<void>;
}

/**
 * Cron job notification interface
 */
export interface ICronJobNotification {
    jobId: string;
    jobName: string;
    type: 'success' | 'failure' | 'retry' | 'timeout' | 'warning';
    message: string;
    timestamp: Date;
    executionId: string;
    context: ICronJobContext;
    result?: ICronJobResult;
    severity: 'info' | 'warning' | 'error' | 'critical';
}

/**
 * Cron job health check interface
 */
export interface ICronJobHealthCheck {
    jobId: string;
    isHealthy: boolean;
    lastHealthCheck: Date;
    issues?: string[];
    recommendations?: string[];
    performance: {
        averageExecutionTime: number;
        memoryUsage: number;
        cpuUsage: number;
    };
}

/**
 * Batch job configuration
 */
export interface IBatchJobConfig {
    batchSize: number;
    parallelProcessing: boolean;
    maxConcurrentBatches: number;
    progressReporting: boolean;
    checkpointInterval: number; // milliseconds
    resumeOnFailure: boolean;
}

/**
 * Scheduled maintenance window
 */
export interface IMaintenanceWindow {
    id: string;
    name: string;
    description: string;
    startTime: Date;
    endTime: Date;
    recurring: boolean;
    affectedJobs: string[];
    pauseJobs: boolean;
    notifyUsers: boolean;
    createdBy: string;
    approvedBy?: string;
}

/**
 * Cron job dependency
 */
export interface ICronJobDependency {
    jobId: string;
    dependsOn: string[];
    waitForCompletion: boolean;
    failIfDependencyFails: boolean;
    maxWaitTime: number; // milliseconds
}

/**
 * Cron job alerting configuration
 */
export interface ICronJobAlertConfig {
    enabled: boolean;
    channels: ('email' | 'slack' | 'webhook' | 'sms')[];
    conditions: {
        onFailure: boolean;
        onSuccess: boolean;
        onTimeout: boolean;
        onConsecutiveFailures: number;
        onLongRunning: number; // milliseconds
    };
    cooldownPeriod: number; // milliseconds
    escalation?: {
        after: number; // failures
        recipients: string[];
        channels: ('email' | 'slack' | 'webhook' | 'sms')[];
    };
} 