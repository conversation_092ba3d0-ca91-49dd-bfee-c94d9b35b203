/**
 * Standard API response structure
 */
export interface IAPIResponse<T = unknown> {
    success: boolean;
    message: string;
    data: T;
    timestamp: Date;
    requestId?: string;
    version?: string;
}

/**
 * Error details interface
 */
export interface IAPIError {
    code: string;
    message: string;
    field?: string;
    details?: Record<string, unknown>;
    suggestions?: string[];
}

/**
 * API error response
 */
export interface IAPIErrorResponse {
    success: false;
    message: string;
    errors: IAPIError[];
    timestamp: Date;
    requestId?: string;
    statusCode: number;
    path?: string;
    method?: string;
}

/**
 * Pagination parameters
 */
export interface IPaginationParams {
    page: number;
    pageSize: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

/**
 * Pagination metadata
 */
export interface IPaginationMeta {
    currentPage: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    nextPage?: number;
    previousPage?: number;
}

/**
 * Paginated response
 */
export interface IPaginatedResponse<T> {
    items: T[];
    pagination: IPaginationMeta;
    filters?: Record<string, unknown>;
    sorting?: {
        field: string;
        order: 'asc' | 'desc';
    };
}

/**
 * Filter criteria
 */
export interface IFilterCriteria {
    field: string;
    operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'like' | 'regex';
    value: string | number | boolean | (string | number | boolean)[];
    caseSensitive?: boolean;
}

/**
 * Search parameters
 */
export interface ISearchParams {
    query?: string;
    filters?: IFilterCriteria[];
    pagination?: IPaginationParams;
    fields?: string[];
    includeDeleted?: boolean;
}

/**
 * Bulk operation request
 */
export interface IBulkOperationRequest<T> {
    operation: 'create' | 'update' | 'delete';
    items: T[];
    options?: {
        continueOnError?: boolean;
        validateFirst?: boolean;
        batchSize?: number;
    };
}

/**
 * Bulk operation result
 */
export interface IBulkOperationResult<T> {
    totalItems: number;
    successfulItems: number;
    failedItems: number;
    results: Array<{
        index: number;
        success: boolean;
        item?: T;
        error?: IAPIError;
    }>;
    summary: {
        processingTime: number;
        errors: IAPIError[];
        warnings: string[];
    };
}

/**
 * User context for API requests
 */
export interface IUserContext {
    userId: string;
    username: string;
    email: string;
    roles: string[];
    permissions: string[];
    sessionId: string;
    lastActivity: Date;
    ipAddress?: string;
    userAgent?: string;
}

/**
 * Request context
 */
export interface IRequestContext {
    requestId: string;
    method: string;
    path: string;
    headers: Record<string, string>;
    query: Record<string, string>;
    user?: IUserContext;
    startTime: Date;
    correlationId?: string;
}

/**
 * Audit log entry
 */
export interface IAuditLogEntry {
    id: string;
    userId: string;
    action: string;
    resource: string;
    resourceId?: string;
    timestamp: Date;
    ipAddress: string;
    userAgent: string;
    details: Record<string, unknown>;
    result: 'success' | 'failure' | 'partial';
    duration?: number;
}

/**
 * File upload information
 */
export interface IFileUpload {
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    path: string;
    uploadedAt: Date;
    uploadedBy: string;
    checksum?: string;
}

/**
 * Export request configuration
 */
export interface IExportConfig {
    format: 'csv' | 'xlsx' | 'pdf' | 'json';
    fields?: string[];
    filters?: IFilterCriteria[];
    includeHeaders?: boolean;
    filename?: string;
    compression?: 'none' | 'gzip' | 'zip';
}

/**
 * Export result
 */
export interface IExportResult {
    fileId: string;
    filename: string;
    format: string;
    size: number;
    recordCount: number;
    createdAt: Date;
    expiresAt: Date;
    downloadUrl: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
}

/**
 * Import configuration
 */
export interface IImportConfig {
    format: 'csv' | 'xlsx' | 'json';
    mapping: Record<string, string>;
    validation: {
        required: string[];
        unique: string[];
        formats: Record<string, string>;
    };
    options: {
        skipHeader?: boolean;
        skipErrors?: boolean;
        batchSize?: number;
        updateExisting?: boolean;
    };
}

/**
 * Import result
 */
export interface IImportResult {
    jobId: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    totalRecords: number;
    processedRecords: number;
    successfulRecords: number;
    failedRecords: number;
    errors: Array<{
        row: number;
        field?: string;
        message: string;
    }>;
    startedAt: Date;
    completedAt?: Date;
    duration?: number;
}

/**
 * Cache configuration
 */
export interface ICacheConfig {
    key: string;
    ttl: number; // seconds
    tags?: string[];
    namespace?: string;
    compress?: boolean;
}

/**
 * Rate limiting configuration
 */
export interface IRateLimitConfig {
    windowMs: number;
    maxRequests: number;
    skipFailedRequests?: boolean;
    skipSuccessfulRequests?: boolean;
    keyGenerator?: (context: IRequestContext) => string;
}

/**
 * Health check result
 */
export interface IHealthCheckResult {
    status: 'healthy' | 'degraded' | 'unhealthy';
    checks: Record<string, {
        status: 'pass' | 'warn' | 'fail';
        message?: string;
        duration?: number;
        timestamp: Date;
    }>;
    version: string;
    uptime: number;
    timestamp: Date;
}

/**
 * System metrics
 */
export interface ISystemMetrics {
    cpu: {
        usage: number;
        loadAverage: number[];
    };
    memory: {
        used: number;
        free: number;
        total: number;
        percentage: number;
    };
    disk: {
        used: number;
        free: number;
        total: number;
        percentage: number;
    };
    network: {
        bytesIn: number;
        bytesOut: number;
        packetsIn: number;
        packetsOut: number;
    };
    processes: {
        active: number;
        total: number;
    };
    timestamp: Date;
}

/**
 * Database connection info
 */
export interface IDatabaseInfo {
    type: string;
    host: string;
    port: number;
    database: string;
    connected: boolean;
    connectionPool: {
        active: number;
        idle: number;
        total: number;
    };
    lastQuery?: Date;
    responseTime?: number;
}

/**
 * Notification configuration
 */
export interface INotificationConfig {
    type: 'email' | 'sms' | 'push' | 'slack' | 'webhook';
    recipients: string[];
    template?: string;
    priority: 'low' | 'normal' | 'high' | 'urgent';
    schedule?: {
        immediate: boolean;
        delay?: number;
        retry?: {
            attempts: number;
            interval: number;
        };
    };
}

/**
 * Feature flag
 */
export interface IFeatureFlag {
    key: string;
    name: string;
    description: string;
    enabled: boolean;
    environment: string[];
    users?: string[];
    groups?: string[];
    percentage?: number;
    conditions?: Array<{
        attribute: string;
        operator: string;
        value: string | number | boolean;
    }>;
    createdAt: Date;
    updatedAt: Date;
    createdBy: string;
} 