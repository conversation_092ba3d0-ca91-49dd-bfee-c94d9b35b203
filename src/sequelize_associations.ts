import { AddressTable } from './features/address/database/AddressTable'
import { DebitNoteTable } from './features/debit_note/database/DebitNoteTable'
import { FactoryGateTable } from './features/factory_gates/database/FactoryGateTable'
import { ItemCategoryTable } from './features/item_category/database/ItemCategoryTable'
import { ItemUnitTable } from './features/item_unit/database/ItemUnitTable'
import { LogsTable } from './features/logs/database/LogsTable'
import { PurchaseInvoiceEntryMappingTable } from './features/purchase_invoice/database/PurchaseInvoiceEntryMappingTable'
import { PurchaseInvoiceTable } from './features/purchase_invoice/database/PurchaseInvoiceTable'
import { RawMaterialPriceTable } from './features/raw_material/database/RawMaterialPriceTable'
import { RawMaterialVariationTable } from './features/raw_material/database/RawMaterialVariationTable'
import { RawMaterialHoldTable } from './features/raw_material_stock/database/RawMaterialHoldTable'
import { RawMaterialRejectionTable } from './features/raw_material_stock/database/RawMaterialRejectionTable'
import { RawMaterialStockInTable } from './features/raw_material_stock/database/RawMaterialStockInTable'
import { RawMaterialStockIssuanceTable } from './features/raw_material_stock/database/RawMaterialStockIssuanceTable'
import { RawMaterialStockTable } from './features/raw_material_stock/database/RawMaterialStockTable'
import { StorageLocationTable } from './features/storage_locations/database/StorageLocationTable'
import { SupplierTable } from './features/supplier/database/SupplierTable'
import { CoreUserTable } from './features/users/core/database/CoreUserTable'
import { NormalUserTable } from './features/users/sub_feaures/normal_user/database/NormalUserTable'
import { RolePermissionsTable } from './features/users/sub_feaures/user_permissions/database/RolePermissionsTable'
import { UserRoleTable } from './features/users/sub_feaures/user_roles/database/UserRoleTable'
import { OpeningStockTable } from './features/opening_stock/database/OpeningStockTable'
import { PurchaseOrderTable } from './features/purchase_order/database/PurchaseOrderTable'
import { PurchaseOrderItemTable } from './features/purchase_order/database/PurchaseOrderItemTable'
import { PurchaseInvoicePurchaseOrderMapping } from './features/purchase_invoice/database/PurchaseInvoicePurchaseOrderMapping'
import { RawMaterialExcessEntryTable } from './features/raw_material/database/RawMaterialExcessEntry'
import { RawMaterialReplacementEntryTable } from './features/raw_material/database/RawMaterialReplacementEntry'
import { RawMaterialMainTable } from './features/raw_material/database/RawMaterialMainTable'
import { RawMaterialVariationsAndAttributesRelationTable } from './features/raw_material/database/RawMaterialsAndAttributesRelationTable'
import { ItemAttributeValueTable } from './features/item_attribute_value/database/ItemAttributeValueTable'
import { ItemAttributeTable } from './features/item_attribute/database/ItemAttributeTable'
import { StockTable } from './features/final_goods/model/stockTable'
import { FinalGoodsVariationTable } from './features/final_goods/model/finalGoodsVariationTable'
import { FinalGoodsTable } from './features/final_goods/model/finalGoodsTable'
import { BillOfMaterialTable } from './features/bill_of_materials/database/BillOfMaterialTable'
import { BillOfMaterialRawMaterialTable } from './features/bill_of_materials/database/BillOfMaterialRawMaterialTable'
import { TaxRateModel } from './features/tax-rate/models/TaxRateTable'
import { DepartmentTable } from './features/department/database/DepartmentTable'
import { FinalGoodsAndCategoriesLinkingModel } from './features/final_goods/model/finalGoodsAndCategoriesLinking'
import { ThirdPartySystemUserTable } from './features/third_party_system_users/database/ThirdPartySystemUserTable'
import { SystemUserTable } from './features/system_users/database/SystemUserTable'

export class SequelizeAssociations {
  static async associate() {
    this._rawMaterialAssociations()
    this._rawMaterialVariationAssociations()
    this._rawMaterialStockInAssociations()
    this._rawMaterialStockAssociations()
    this._supplierAssociations()
    this._purchaseInvoiceAssociations()
    this._userTableAssociations()
    this._debitNoteTableAssociations()
    this._logsTableAssociations()
    this._rawMaterialStockIssuanceAssociations()
    this._addressTableAssociations()
    this._userRoleTableAssociations()
    this._openingStockAssociations()
    this._purchaseOrderAssociations()
    this._purchaseOrderItemAssociations()
    this._purchaseOrderAndPurchaseInvoiceAssociations()
    this._rawMaterialExcessEntriesAssociations()
    this._RawMaterialReplacementEntriesAssociations();
    this._RawMaterialVariationsAndAttributesRelationTableAssociations();
    this._ItemAttributesAndValuesAssociations();
    this._taxRateAssociate()
    // this._hashesAssociate()
    // this._finalGoodsAssociate()
    // this._finalGoodsVariationAssociate()
    this._stockAssociate()
    this._departmentAssociations()
    this._billOfMaterialAssociate()
    this._billOfRawMaterialAssociate()
    this._thirdPartySystemUserAssociations();
    this._systemUserAssociations();
  }

  static _rawMaterialAssociations() {
    /* association with user */
    CoreUserTable.hasMany(RawMaterialMainTable, {
      foreignKey: 'createdById',
    });
    RawMaterialMainTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: 'createdBy',
    });
    /* association with category */
    ItemCategoryTable.hasMany(RawMaterialMainTable, {
      foreignKey: 'categoryId',
    });
    RawMaterialMainTable.belongsTo(ItemCategoryTable, {
      foreignKey: 'categoryId',
      as: 'category',
    });

    /* association with unit */
    ItemUnitTable.hasMany(RawMaterialMainTable, {
      foreignKey: 'unitId',
    });
    RawMaterialMainTable.belongsTo(ItemUnitTable, {
      foreignKey: 'unitId',
      as: 'unit',
    });
    ItemUnitTable.belongsTo(CoreUserTable, { foreignKey: 'createdById', as: 'creator' });
    ItemUnitTable.belongsTo(CoreUserTable, { foreignKey: 'updatedById', as: 'updater' });
    ItemUnitTable.belongsTo(CoreUserTable, { foreignKey: 'deletedById', as: 'deleter' });


    ItemCategoryTable.hasMany(FinalGoodsAndCategoriesLinkingModel, {
      foreignKey: 'category_id',
      as: "final_goods"
    })
  }

  static _rawMaterialVariationAssociations() {
    RawMaterialMainTable.hasMany(RawMaterialVariationTable, {
      foreignKey: 'parentRawMaterialId',
      as: 'variations',
    })
    RawMaterialVariationTable.belongsTo(RawMaterialMainTable, {
      foreignKey: 'parentRawMaterialId',
      as: 'rawMaterial',
    })

    /* association with Price */
    RawMaterialVariationTable.hasMany(RawMaterialPriceTable, {
      foreignKey: 'rawMaterialId',
      as: 'prices',
    })
    RawMaterialPriceTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
      as: 'prices',
    })

    SupplierTable.hasMany(RawMaterialPriceTable, {
      foreignKey: 'supplierId',
    })
    RawMaterialPriceTable.belongsTo(SupplierTable, {
      foreignKey: 'supplierId',
      as: 'supplier',
    })

    /* association with raw material rejection */
    RawMaterialVariationTable.hasMany(RawMaterialRejectionTable, {
      foreignKey: 'rawMaterialId',
    })
    RawMaterialRejectionTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
    })

    /* association with raw material holds */

    RawMaterialVariationTable.hasMany(RawMaterialHoldTable, {
      foreignKey: 'rawMaterialId',
    })
    RawMaterialHoldTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
    })

    /* association with raw material stock issuance */
    RawMaterialVariationTable.hasMany(RawMaterialStockIssuanceTable, {
      foreignKey: 'rawMaterialId',
      as: 'stockIssuances',
    })
    RawMaterialStockIssuanceTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
    })
  }

  static _rawMaterialStockInAssociations() {
    SupplierTable.hasMany(RawMaterialStockInTable, {
      foreignKey: 'supplierId',
    })
    RawMaterialStockInTable.belongsTo(SupplierTable, {
      foreignKey: 'supplierId',
      as: 'supplier',
    })

    StorageLocationTable.hasMany(RawMaterialStockInTable, {
      foreignKey: 'storageLocationId',
      as: 'storageLocation',
    })
    RawMaterialStockInTable.belongsTo(StorageLocationTable, {
      foreignKey: 'storageLocationId',
      as: 'storageLocation',
    })

    FactoryGateTable.hasMany(RawMaterialStockInTable, {
      foreignKey: 'factoryGateId',
    })
    RawMaterialStockInTable.belongsTo(FactoryGateTable, {
      foreignKey: 'factoryGateId',
      as: 'factoryGate',
    })

    RawMaterialVariationTable.hasMany(RawMaterialStockInTable, {
      foreignKey: 'rawMaterialId',
    })
    RawMaterialStockInTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterialVariation',
    })
  }

  static _rawMaterialStockAssociations() {
    RawMaterialVariationTable.hasMany(RawMaterialStockTable, {
      foreignKey: 'rawMaterialId',
    })

    RawMaterialStockTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterialVariation',
    })
  }

  static _supplierAssociations() {
    /* association with Address */
    AddressTable.hasMany(SupplierTable, {
      foreignKey: 'addressId',
    })
    SupplierTable.belongsTo(AddressTable, {
      foreignKey: 'addressId',
      as: 'address',
    })

  }

  static _purchaseInvoiceAssociations() {
    /* association with supplier */

    SupplierTable.hasMany(PurchaseInvoiceTable, {
      foreignKey: 'supplierId',
    })
    PurchaseInvoiceTable.belongsTo(SupplierTable, {
      foreignKey: 'supplierId',
      as: 'supplier',
    })

    /* association with raw material stock in */
    PurchaseInvoiceTable.hasMany(RawMaterialStockInTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'rawMaterialStockInEntries',
    })
    RawMaterialStockInTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice',
    })

    /* association with raw material rejection */
    PurchaseInvoiceTable.hasMany(RawMaterialRejectionTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'rawMaterialRejections',
    })
    RawMaterialRejectionTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
    })

    /* association with raw material hold */
    PurchaseInvoiceTable.hasMany(RawMaterialHoldTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'rawMaterialHolds',
    })
    RawMaterialHoldTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
    })

    /* association with users */

    CoreUserTable.hasMany(PurchaseInvoiceTable, {
      foreignKey: 'purchasedById',
    })
    PurchaseInvoiceTable.belongsTo(CoreUserTable, {
      foreignKey: 'purchasedById',
      as: 'purchasedBy',
    })

    /* with entry mapping */
    PurchaseInvoiceTable.hasOne(PurchaseInvoiceEntryMappingTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'entryMapping',
    })
    PurchaseInvoiceEntryMappingTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice',
    })

    /* with factory Gate */
  }

  static _userTableAssociations() {
    CoreUserTable.hasOne(NormalUserTable, {
      foreignKey: 'coreUserId',
      as: 'normalUser',
    })

    NormalUserTable.belongsTo(CoreUserTable, {
      foreignKey: 'coreUserId',
      as: 'coreUser',
    })

    UserRoleTable.hasMany(CoreUserTable, {
      foreignKey: 'roleId',
    })
    CoreUserTable.belongsTo(UserRoleTable, {
      foreignKey: 'roleId',
      as: 'role',
    })

    // /* with supplier */
    // UserTable.hasMany(SupplierTable, {
    //     foreignKey: 'createdById',
    // });
    // SupplierTable.belongsTo(UserTable, {
    //     foreignKey: 'createdById',
    //     as: "actionUser"
    // });

    // /* with category */
    // UserTable.hasMany(ItemCategoryTable, {
    //     foreignKey: 'createdById',
    // });
    // ItemCategoryTable.belongsTo(UserTable, {
    //     foreignKey: 'createdById',
    //     as: "actionUser"
    // });

    /* with  address*/
    // AddressTable.hasMany(CoreUserTable, {
    //     foreignKey: 'addressId',
    // });
    // CoreUserTable.belongsTo(AddressTable, {
    //     foreignKey: 'addressId',
    //     as: "address",
    // });
  }

  static _debitNoteTableAssociations() {
    /* with purchase invoice */
    PurchaseInvoiceTable.hasMany(DebitNoteTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'debitNotes',
    })
    DebitNoteTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice',
    })

    /* with raw material */
    RawMaterialVariationTable.hasMany(DebitNoteTable, {
      foreignKey: 'rawMaterialId',
    })
    DebitNoteTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
    })
  }

  static _logsTableAssociations() {
    CoreUserTable.hasMany(LogsTable, {
      foreignKey: 'userId',
    })

    LogsTable.belongsTo(CoreUserTable, {
      foreignKey: 'userId',
      as: 'user',
    })
  }

  static _rawMaterialStockIssuanceAssociations() {
    /* association with users */

    CoreUserTable.hasMany(RawMaterialStockIssuanceTable, {
      foreignKey: 'createdById',
    })
    RawMaterialStockIssuanceTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: "issuedBy",
    });

    RawMaterialStockIssuanceTable.belongsTo(CoreUserTable, {
      foreignKey: 'issuedTo',
      as: "issuedToUser",
    });

    CoreUserTable.hasMany(RawMaterialStockIssuanceTable, {
      foreignKey: 'issuedTo',
      as: "rawMaterialIssuance",
    })
  }

  static _addressTableAssociations() {
    AddressTable.hasMany(NormalUserTable, {
      foreignKey: 'addressId',
    })
    NormalUserTable.belongsTo(AddressTable, {
      foreignKey: 'addressId',
      as: 'address',
    })
  }

  static _userRoleTableAssociations() {
    UserRoleTable.hasOne(RolePermissionsTable, {
      foreignKey: 'roleId',
      as: 'permissions',
    })

    RolePermissionsTable.belongsTo(UserRoleTable, {
      foreignKey: 'roleId',
      as: 'role',
    })
  }

  static _openingStockAssociations() {
    RawMaterialVariationTable.hasMany(OpeningStockTable, {
      foreignKey: 'rawMaterialId',
      as: 'openingStocks',
    })
    OpeningStockTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
    })

    CoreUserTable.hasMany(OpeningStockTable, {
      foreignKey: 'createdById',
    })
    OpeningStockTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: 'createdBy',
    })
  }

  static _purchaseOrderAssociations() {
    PurchaseOrderTable.belongsTo(SupplierTable, {
      foreignKey: 'supplierId',
      as: 'supplier',
    })
    SupplierTable.hasMany(PurchaseOrderTable, {
      foreignKey: 'supplierId',
      as: 'purchaseOrder',
    });
    PurchaseOrderTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: 'createdBy',
    })
    CoreUserTable.hasMany(PurchaseOrderTable, {
      foreignKey: 'createdById',
      as: 'purchaseOrder',
    });

    // Department associations
    PurchaseOrderTable.belongsTo(DepartmentTable, {
      foreignKey: 'fromDepartmentId',
      as: 'fromDepartment',
    })
    DepartmentTable.hasMany(PurchaseOrderTable, {
      foreignKey: 'fromDepartmentId',
      as: 'purchaseOrdersFrom',
    });
    PurchaseOrderTable.belongsTo(DepartmentTable, {
      foreignKey: 'toDepartmentId',
      as: 'toDepartment',
    })
    DepartmentTable.hasMany(PurchaseOrderTable, {
      foreignKey: 'toDepartmentId',
      as: 'purchaseOrdersTo',
    });
  }

  static _purchaseOrderItemAssociations() {

    PurchaseOrderTable.hasMany(PurchaseOrderItemTable, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrderItems',
    });

    PurchaseOrderItemTable.belongsTo(PurchaseOrderTable, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrder',
    });
    PurchaseOrderItemTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterialVariation',
    })
    RawMaterialVariationTable.hasMany(PurchaseOrderItemTable, {
      foreignKey: 'rawMaterialId',
      as: 'purchaseOrderItem',
    })
  }
  static _purchaseOrderAndPurchaseInvoiceAssociations() {
    // Mapping table belongs to PurchaseOrder
    PurchaseInvoicePurchaseOrderMapping.belongsTo(PurchaseOrderTable, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrder',
    });

    // PurchaseOrder has many mappings
    PurchaseOrderTable.hasMany(PurchaseInvoicePurchaseOrderMapping, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseInvoiceMappings',
    });

    // Mapping table belongs to PurchaseInvoice
    PurchaseInvoicePurchaseOrderMapping.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice',
    });

    // PurchaseInvoice has many mappings
    PurchaseInvoiceTable.hasMany(PurchaseInvoicePurchaseOrderMapping, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseOrderMappings',
    });
  }

  static _rawMaterialExcessEntriesAssociations() {
    RawMaterialExcessEntryTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
    });
    RawMaterialVariationTable.hasMany(RawMaterialExcessEntryTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterialExcessEntry',
    })
    RawMaterialExcessEntryTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice'
    })
    PurchaseInvoiceTable.hasMany(RawMaterialExcessEntryTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'rawMaterialExcessEntry',
    })
  }

  static _RawMaterialReplacementEntriesAssociations() {
    RawMaterialReplacementEntryTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterial',
    });
    RawMaterialVariationTable.hasMany(RawMaterialReplacementEntryTable, {
      foreignKey: 'rawMaterialId',
      as: 'rawMaterialReplacementEntry',
    })
    RawMaterialReplacementEntryTable.belongsTo(PurchaseInvoiceTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'purchaseInvoice'
    })
    PurchaseInvoiceTable.hasMany(RawMaterialReplacementEntryTable, {
      foreignKey: 'purchaseInvoiceId',
      as: 'rawMaterialReplacementEntry',
    })
  }
  static _RawMaterialVariationsAndAttributesRelationTableAssociations() {
    RawMaterialVariationTable.hasMany(RawMaterialVariationsAndAttributesRelationTable, {
      foreignKey: 'raw_material_variation_id',
      as: 'attributesRelation',
    });
    RawMaterialVariationsAndAttributesRelationTable.belongsTo(RawMaterialVariationTable, {
      foreignKey: 'raw_material_variation_id',
      as: 'rawMaterialVariation',
    });
    RawMaterialVariationsAndAttributesRelationTable.belongsTo(ItemAttributeValueTable, {
      foreignKey: 'item_attribute_value_id',
      as: 'attributeValue',
    });
    ItemAttributeValueTable.hasMany(RawMaterialVariationsAndAttributesRelationTable, {
      foreignKey: 'item_attribute_value_id',
      as: 'rawMaterialVariationsAndAttributesRelation',
    });
    CoreUserTable.hasMany(RawMaterialVariationsAndAttributesRelationTable, {
      foreignKey: 'createdById',
      as: 'createdBy',
    });
    RawMaterialVariationsAndAttributesRelationTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: 'createdBy',
    });
  }
  static _ItemAttributesAndValuesAssociations() {
    ItemAttributeTable.hasMany(ItemAttributeValueTable, {
      foreignKey: 'itemAttributeId',
      as: 'attributeValues',
    });
    ItemAttributeValueTable.belongsTo(ItemAttributeTable, {
      foreignKey: 'itemAttributeId',
      as: 'attribute',
    });
  }

  static _departmentAssociations() {
    DepartmentTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: 'createdBy',
    });

    CoreUserTable.hasMany(DepartmentTable, {
      foreignKey: 'createdById',
      as: 'departments',
    });
  }



  static _taxRateAssociate() {
    TaxRateModel.belongsTo(CoreUserTable, {
      foreignKey: 'createdBy',
      as: 'createdByUser',
      onDelete: 'SET NULL'
    });
    TaxRateModel.belongsTo(CoreUserTable, {
      foreignKey: 'updatedBy',
      as: 'updatedByUser',
      onDelete: 'SET NULL'
    });
    TaxRateModel.belongsTo(CoreUserTable, {
      foreignKey: 'deletedBy',
      as: 'deletedByUser',
    });
  }


  static _finalGoodsAssociate() {
    FinalGoodsTable.belongsTo(CoreUserTable, {
      as: "createdByUser",
      foreignKey: "createdBy",
    });
    FinalGoodsTable.belongsTo(CoreUserTable, {
      as: "updatedByUser",
      foreignKey: "updatedBy",
    });
    FinalGoodsTable.belongsTo(CoreUserTable, {
      as: "deletedByUser",
      foreignKey: "deletedBy",
    });
    FinalGoodsTable.hasMany(FinalGoodsVariationTable, {
      foreignKey: 'finalGoodsId',
      as: 'finalGoodsVariations',
      onUpdate: "CASCADE",
      onDelete: "SET NULL"
    })
    // FinalGoodsTable.hasMany(StockTable, {
    //   foreignKey: "finalGoodsId",
    //   as: "stock", // Alias for the association
    // });
    // FinalGoodsTable.belongsTo(TaxRateModel, {
    //   foreignKey: "taxRate", as: "tax"
    // })

    FinalGoodsTable.belongsTo(ItemCategoryTable, {
      foreignKey: 'category',
      as: 'finalGoodsCategories',
    })
  }

  static _finalGoodsVariationAssociate() {
    FinalGoodsVariationTable.belongsTo(CoreUserTable,
      { as: 'createdByUser', foreignKey: 'createdBy' })
    FinalGoodsVariationTable.belongsTo(CoreUserTable,
      { as: 'updatedByUser', foreignKey: 'updatedBy' })
    FinalGoodsVariationTable.belongsTo(CoreUserTable,
      { as: 'deletedByUser', foreignKey: 'deletedBy' })
    FinalGoodsVariationTable.belongsTo(FinalGoodsTable,
      { as: 'finalGoodsData', foreignKey: 'finalGoodsId', })

    FinalGoodsVariationTable.belongsTo(StockTable, {
      as: "stock",
      foreignKey: "stockId",
    });
    FinalGoodsVariationTable.hasMany(StockTable, {
      as: "stockVariations",
      foreignKey: "finalGoodsVariationId"
    })
  }


  static _stockAssociate() {
    StockTable.belongsTo(CoreUserTable,
      { as: 'createdByUser', foreignKey: 'createdBy' })
    StockTable.belongsTo(CoreUserTable,
      { as: 'updatedByUser', foreignKey: 'updatedBy' })
    StockTable.belongsTo(CoreUserTable,
      { as: 'deletedByUser', foreignKey: 'deletedBy' })

    FinalGoodsTable.hasMany(StockTable, {
      as: 'stocks',
      foreignKey: 'final_goods_id'
    });

    StockTable.belongsTo(FinalGoodsTable,
      { as: "finalGoods", foreignKey: "final_goods_id" });

    StockTable.belongsTo(FinalGoodsVariationTable,
      { as: "finalGoodsVariation", foreignKey: "final_goods_variation_id" })
  }


  static _billOfMaterialAssociate() {
    BillOfMaterialTable.belongsTo(CoreUserTable, {
      as: 'createdByUser',
      foreignKey: 'createdBy'
    })
    BillOfMaterialTable.belongsTo(CoreUserTable, {
      as: 'updatedByUser',
      foreignKey: 'updatedBy'
    })
    BillOfMaterialTable.belongsTo(CoreUserTable, {
      as: 'deletedByUser',
      foreignKey: 'deletedBy'
    })
    BillOfMaterialTable.belongsTo(FinalGoodsVariationTable, {
      as: 'finalGoodsVariation',
      foreignKey: 'finalGoodsVariationId'
    })
    BillOfMaterialTable.hasMany(BillOfMaterialRawMaterialTable, {
      as: 'rawMaterialVariation',
      foreignKey: 'bomId'
    });
  }

  static _billOfRawMaterialAssociate() {
    BillOfMaterialRawMaterialTable.belongsTo(CoreUserTable, {
      as: 'createdByUser',
      foreignKey: 'createdBy'
    })
    BillOfMaterialRawMaterialTable.belongsTo(CoreUserTable, {
      as: 'updatedByUser',
      foreignKey: 'updatedBy'
    })
    BillOfMaterialRawMaterialTable.belongsTo(CoreUserTable, {
      as: 'deletedByUser',
      foreignKey: 'deletedBy'
    })
    RawMaterialVariationTable.hasMany(BillOfMaterialRawMaterialTable, {
      as: 'billofmaterial_rawmaterial',
      foreignKey: 'rawMaterialVariationId'
    })
    BillOfMaterialRawMaterialTable.belongsTo(RawMaterialVariationTable, {
      as: 'rawMaterialVariation',
      foreignKey: 'rawMaterialVariationId'
    })
    BillOfMaterialRawMaterialTable.belongsTo(BillOfMaterialTable, {
      as: 'billOfMaterial',
      foreignKey: 'bomId'
    });
  }

  static _thirdPartySystemUserAssociations() {
    // Association for createdById
    ThirdPartySystemUserTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: 'createdBy',
    });

    CoreUserTable.hasMany(ThirdPartySystemUserTable, {
      foreignKey: 'createdById',
      as: 'thirdPartySystemUsers',
    });
    ThirdPartySystemUserTable.belongsTo(CoreUserTable, {
      foreignKey: 'updatedById',
      as: 'updatedBy',
    });

    CoreUserTable.hasMany(ThirdPartySystemUserTable, {
      foreignKey: 'updatedById',
      as: 'thirdPartySystemUsersUpdatedBy',
    });

    ThirdPartySystemUserTable.belongsTo(CoreUserTable, {
      foreignKey: 'deletedById',
      as: 'deletedBy',
    });

    CoreUserTable.hasMany(ThirdPartySystemUserTable, {
      foreignKey: 'deletedById',
      as: 'thirdPartySystemUsersDeletedBy',
    });

    // Association for userId (the new foreign key)
    ThirdPartySystemUserTable.belongsTo(CoreUserTable, {
      foreignKey: 'userId',
      as: 'user',
    });

    CoreUserTable.hasMany(ThirdPartySystemUserTable, {
      foreignKey: 'userId',
      as: 'thirdPartySystemUsersByUser',
    });
  }

  static _systemUserAssociations() {
    // Association for createdById
    SystemUserTable.belongsTo(CoreUserTable, {
      foreignKey: 'createdById',
      as: 'createdBy',
    });

    CoreUserTable.hasMany(SystemUserTable, {
      foreignKey: 'createdById',
      as: 'systemUsers',
    });

    SystemUserTable.belongsTo(CoreUserTable, {
      foreignKey: 'updatedById',
      as: 'updatedBy',
    });

    CoreUserTable.hasMany(SystemUserTable, {
      foreignKey: 'updatedById',
      as: 'systemUsersUpdatedBy',
    });

    SystemUserTable.belongsTo(CoreUserTable, {
      foreignKey: 'deletedById',
      as: 'deletedBy',
    });

    CoreUserTable.hasMany(SystemUserTable, {
      foreignKey: 'deletedById',
      as: 'systemUsersDeletedBy',
    });

    // Association for userId (the new foreign key)
    SystemUserTable.belongsTo(CoreUserTable, {
      foreignKey: 'userId',
      as: 'user',
    });

    CoreUserTable.hasMany(SystemUserTable, {
      foreignKey: 'userId',
      as: 'systemUsersByUser',
    });
  }
}
