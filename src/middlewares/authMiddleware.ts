import * as admin from 'firebase-admin';

import { NextFunction, Request, Response } from "express";
import { HelperMethods } from "../core/HelperMethods";
import { CoreUserTable } from '../features/users/core/database/CoreUserTable';
import { USER_STAUS } from '../features/users/core/models/UserMisc';
import { RolePermissionsTable } from '../features/users/sub_feaures/user_permissions/database/RolePermissionsTable';
import { UserRoleTable } from '../features/users/sub_feaures/user_roles/database/UserRoleTable';
import { USER_ROLE_STATUS } from '../features/users/sub_feaures/user_roles/models/UserRolesMisc';
import { AppPermissions } from '../features/users/sub_feaures/user_permissions/AppPermissions';
import { RepoProvider } from '../core/RepoProvider';
import { THIRD_PARTY_SYSTEM_USER_TYPE } from '../features/third_party_system_users/models/ThirdPartySystemUserMisc';

export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
    console.log("🔒 authMiddleware:" + req.url);

    let is3rdPartyUser = false;
    const idToken = req.headers.authorization?.split('Bearer ')[1];
    if (!idToken) {
        res.status(401).send(
            HelperMethods.getUnAuthorisedResponse()
        );
        return;
    }
    try {

        /* check if the token exists in the 3rd party data */
        const thirdPartyUser = await RepoProvider.thirdPartySystemUserRepo.get3rdPartySystemUser(
            THIRD_PARTY_SYSTEM_USER_TYPE.CRM_USER,
        );
        if (thirdPartyUser.success && thirdPartyUser.data !== null && thirdPartyUser.data.token === idToken) {
            is3rdPartyUser = true;
        }

        if (is3rdPartyUser) {
            /* get user and set to the request */
            const user = await RepoProvider.normalUserRepo.getById(thirdPartyUser.data!.userId);
            if (user.success && user.data !== null) {
                (req as any).is_3rd_party_user = true;
                (req as any).user_id = user.data!.coreUserId;

            }
            else {
                res.status(401).send(
                    HelperMethods.getUnAuthorisedResponse("User not found")
                );
                return;
            }

            const permissionsData: string[] = [];
            for (const feature in AppPermissions) {
                const appFeature = Object.values(AppPermissions[feature as keyof typeof AppPermissions]);
                for (const permission of appFeature) {
                    permissionsData.push(permission);
                }

                (req as any).user_permissions = permissionsData;
                
                next();
                return;
            }
        }
        const decodedToken = await verifyFirebaseToken(idToken);


        if (decodedToken && decodedToken!.uid) {
            const user = await getUserByFirebaseUid(decodedToken!.uid);
            if (user) {

                (req as any).firebase_uid = decodedToken?.uid;
                (req as any).user_id = user.dataValues.id;

                /* get role permissions */
                const userRole = await UserRoleTable.findOne({
                    where: {
                        id: user.dataValues.roleId,
                        status: USER_ROLE_STATUS.ACTIVE,
                    },
                    include:
                    {
                        model: RolePermissionsTable,
                        as: 'permissions',

                    }
                });

                if (!userRole) {
                    res.status(401).send(
                        HelperMethods.getUnAuthorisedResponse()
                    );
                    return;
                }

                const permissionsData: string[] = [];

                if (userRole.dataValues.role === "admin") {
                    for (const feature in AppPermissions) {
                        const appFeature = Object.values(AppPermissions[feature as keyof typeof AppPermissions]);
                        for (const permission of appFeature) {
                            permissionsData.push(permission);
                        }
                    }
                }
                else {
                    permissionsData.push(...userRole.permissions.dataValues.permissions);
                }
                (req as any).user_permissions = permissionsData;
                next();
            } else {
                res.status(401).send(
                    HelperMethods.getUnAuthorisedResponse("User not found")
                );
                return;
            }
        } else {
            res.status(401).send(
                HelperMethods.getUnAuthorisedResponse()
            );
            return;
        }
    } catch (err) {

        res.status(401).send(HelperMethods.getUnAuthorisedResponse());
        return;
    }
};



export const verifyFirebaseToken = async (idToken: string) => {
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    return decodedToken;
};


export const getUserByFirebaseUid = async (firebaseUID: string) => {
    return await CoreUserTable.findOne({
        where: {
            firebaseUID: firebaseUID,
            status: USER_STAUS.ACTIVE,
        },
    });
}