import { BrandTableModel } from "./features/brand/model/brandTable";
import { FinalGoodsAndCategoriesLinkingModel } from "./features/final_goods/model/finalGoodsAndCategoriesLinking";
import { FinalGoodsTable } from "./features/final_goods/model/finalGoodsTable";
import { FinalGoodsVariationAndAttributesRelationTable } from "./features/final_goods/model/FinalGoodsVariationAndAttributesRelationTable";
import { FinalGoodsVariationTable } from "./features/final_goods/model/finalGoodsVariationTable";
import { StockTable } from "./features/final_goods/model/stockTable";



const initModel = () => {
    const models = {
        FinalGoodsTable: FinalGoodsTable.initModel(),
        FinalGoodsVariationTable: FinalGoodsVariationTable.initModel(),
        FinalGoodsVariationAndAttributesRelationTable: FinalGoodsVariationAndAttributesRelationTable.initModel(),
        FinalGoodsAndCategoriesLinkingModel:FinalGoodsAndCategoriesLinkingModel.initModel(),
        StockTable: StockTable.initModel(),
        BrandTableModel:BrandTableModel.initModel()
    }

    try {
        Object.values(models).forEach((model: any) => {
            if (model.associate) {
              model.associate(models);
            }

            if(model.hooks){
                model.hooks(models);
            }
        });
        console.log("Association initialized successfully.");
        return models
    } catch (error) {
        console.log(error);
    }
}

export {initModel}