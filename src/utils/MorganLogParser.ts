import fs from 'fs';
import path from 'path';
import zlib from 'zlib';

export interface IMorganLog {
    timestamp: string;
    id: string;
    ip: string;
    request: {
        timestamp: string;
        method: string;
        url: string;
        headers: Record<string, any>;
        body: any;
    };
    response: {
        status: number;
        headers: Record<string, any>;
        body: any;
        responseTime: string;
    };
    error: false | {
        message: string;
        name?: string;
        stack?: string;
        statusCode?: number;
        timestamp?: string;
        details?: any;
    };
}

export interface ILogStatistics {
    totalRequests: number;
    requestsByMethod: Record<string, number>;
    requestsByStatus: Record<string, number>;
    errorCount: number;
    averageResponseTime: number;
    slowestRequests: Array<{
        url: string;
        method: string;
        responseTime: number;
        timestamp: string;
    }>;
    topIPs: Array<{
        ip: string;
        count: number;
    }>;
    errorDistribution: Record<string, number>;
    timeRange: {
        start: string;
        end: string;
    };
}

export class MorganLogParser {
    
    /**
     * Parse a single log file and return array of log entries
     */
    static parseLog(filePath: string): IMorganLog[] {
        try {
            let content: string;
            
            // Check if file is gzipped
            if (filePath.endsWith('.gz')) {
                const compressed = fs.readFileSync(filePath);
                content = zlib.gunzipSync(compressed).toString('utf8');
            } else {
                content = fs.readFileSync(filePath, 'utf8');
            }

            return this.parseLogContent(content);
        } catch (error) {
            console.error(`Error reading log file ${filePath}:`, error);
            return [];
        }
    }

    /**
     * Parse multiple log files from a directory
     */
    static parseLogsFromDirectory(directoryPath: string, filePattern: string = '-api.log'): IMorganLog[] {
        try {
            const files = fs.readdirSync(directoryPath);
            const logFiles = files.filter(file => file.endsWith(filePattern));
            
            let allLogs: IMorganLog[] = [];
            
            for (const file of logFiles) {
                const filePath = path.join(directoryPath, file);
                const logs = this.parseLog(filePath);
                allLogs.push(...logs);
            }

            // Sort by timestamp
            allLogs.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
            
            return allLogs;
        } catch (error) {
            console.error(`Error reading directory ${directoryPath}:`, error);
            return [];
        }
    }

    /**
     * Parse log content string into log entries
     */
    private static parseLogContent(content: string): IMorganLog[] {
        const logs: IMorganLog[] = [];
        
        // Split by separator
        const entries = content.split('----------------------------------------');
        
        for (const entry of entries) {
            const trimmedEntry = entry.trim();
            if (!trimmedEntry) continue;
            
            try {
                const logEntry = this.parseLogEntry(trimmedEntry);
                if (logEntry) {
                    logs.push(logEntry);
                }
            } catch (error) {
                console.warn('Failed to parse log entry:', error);
            }
        }
        
        return logs;
    }

    /**
     * Parse a single log entry
     */
    private static parseLogEntry(entry: string): IMorganLog | null {
        try {
            const lines = entry.split('\n').filter(line => line.trim());
            
            let timestamp = '';
            let id = '';
            let ip = '';
            let request: any = null;
            let response: any = null;
            let error: any = false;

            for (const line of lines) {
                const trimmedLine = line.trim();
                
                if (trimmedLine.startsWith('Timestamp: ')) {
                    timestamp = trimmedLine.substring('Timestamp: '.length);
                } else if (trimmedLine.startsWith('id: ')) {
                    id = trimmedLine.substring('id: '.length);
                } else if (trimmedLine.startsWith('IP: ')) {
                    ip = trimmedLine.substring('IP: '.length);
                } else if (trimmedLine.startsWith('Request: ')) {
                    const requestJson = trimmedLine.substring('Request: '.length);
                    request = this.safeJsonParse(requestJson);
                } else if (trimmedLine.startsWith('Response: ')) {
                    const responseJson = trimmedLine.substring('Response: '.length);
                    response = this.safeJsonParse(responseJson);
                } else if (trimmedLine.startsWith('Error: ')) {
                    const errorJson = trimmedLine.substring('Error: '.length);
                    if (errorJson === 'false') {
                        error = false;
                    } else {
                        error = this.safeJsonParse(errorJson);
                    }
                }
            }

            // Validate required fields
            if (!timestamp || !id || !request || !response) {
                return null;
            }

            return {
                timestamp,
                id,
                ip,
                request,
                response,
                error
            };
        } catch (error) {
            console.warn('Error parsing log entry:', error);
            return null;
        }
    }

    /**
     * Safely parse JSON with fallback
     */
    private static safeJsonParse(jsonString: string): any {
        try {
            return JSON.parse(jsonString);
        } catch (error) {
            console.warn('Failed to parse JSON:', jsonString);
            return jsonString; // Return as string if parsing fails
        }
    }

    /**
     * Filter logs based on criteria
     */
    static filterLogs(
        logs: IMorganLog[],
        filters: {
            method?: string;
            statusCode?: number;
            hasError?: boolean;
            url?: string;
            startDate?: Date;
            endDate?: Date;
        }
    ): IMorganLog[] {
        return logs.filter(log => {
            // Method filter
            if (filters.method && log.request.method !== filters.method) {
                return false;
            }

            // Status code filter
            if (filters.statusCode && log.response.status !== filters.statusCode) {
                return false;
            }

            // Error filter
            if (filters.hasError !== undefined) {
                const hasError = log.error !== false;
                if (filters.hasError !== hasError) {
                    return false;
                }
            }

            // URL filter (contains)
            if (filters.url && !log.request.url.includes(filters.url)) {
                return false;
            }

            // Date range filter
            const logDate = new Date(log.timestamp);
            if (filters.startDate && logDate < filters.startDate) {
                return false;
            }
            if (filters.endDate && logDate > filters.endDate) {
                return false;
            }

            return true;
        });
    }

    /**
     * Get comprehensive statistics from logs
     */
    static getLogStatistics(logs: IMorganLog[]): ILogStatistics {
        if (logs.length === 0) {
            return {
                totalRequests: 0,
                requestsByMethod: {},
                requestsByStatus: {},
                errorCount: 0,
                averageResponseTime: 0,
                slowestRequests: [],
                topIPs: [],
                errorDistribution: {},
                timeRange: { start: '', end: '' }
            };
        }

        const stats: ILogStatistics = {
            totalRequests: logs.length,
            requestsByMethod: {},
            requestsByStatus: {},
            errorCount: 0,
            averageResponseTime: 0,
            slowestRequests: [],
            topIPs: [],
            errorDistribution: {},
            timeRange: {
                start: logs[0].timestamp,
                end: logs[logs.length - 1].timestamp
            }
        };

        const ipCounts: Record<string, number> = {};
        let totalResponseTime = 0;
        const requestsWithTime: Array<{
            url: string;
            method: string;
            responseTime: number;
            timestamp: string;
        }> = [];

        for (const log of logs) {
            // Count by method
            const method = log.request.method;
            stats.requestsByMethod[method] = (stats.requestsByMethod[method] || 0) + 1;

            // Count by status
            const status = log.response.status.toString();
            stats.requestsByStatus[status] = (stats.requestsByStatus[status] || 0) + 1;

            // Count errors
            if (log.error !== false) {
                stats.errorCount++;
                
                // Error distribution
                const errorType = log.error.name || 'Unknown Error';
                stats.errorDistribution[errorType] = (stats.errorDistribution[errorType] || 0) + 1;
            }

            // IP counts
            if (log.ip) {
                ipCounts[log.ip] = (ipCounts[log.ip] || 0) + 1;
            }

            // Response time analysis
            const responseTimeStr = log.response.responseTime;
            if (responseTimeStr && responseTimeStr.includes('ms')) {
                const responseTime = parseInt(responseTimeStr.replace('ms', ''));
                if (!isNaN(responseTime)) {
                    totalResponseTime += responseTime;
                    requestsWithTime.push({
                        url: log.request.url,
                        method: log.request.method,
                        responseTime,
                        timestamp: log.timestamp
                    });
                }
            }
        }

        // Calculate average response time
        if (requestsWithTime.length > 0) {
            stats.averageResponseTime = Math.round(totalResponseTime / requestsWithTime.length);
        }

        // Get slowest requests (top 10)
        stats.slowestRequests = requestsWithTime
            .sort((a, b) => b.responseTime - a.responseTime)
            .slice(0, 10);

        // Get top IPs (top 10)
        stats.topIPs = Object.entries(ipCounts)
            .map(([ip, count]) => ({ ip, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);

        return stats;
    }
} 