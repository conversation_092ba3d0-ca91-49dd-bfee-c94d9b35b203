import express from 'express';
import morgan from 'morgan';
import json from 'morgan-json';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Configuration constants
const MAX_FILE_SIZE = 50 * 1024; // 500KB per file (for testing)
const MAX_FILES_PER_DAY = 10; // Maximum 10 files per day
const LOGS_RETENTION_DAYS = 30; // Keep logs for 30 days

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');

if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

/**
 * Generate log file path for the current date with rotation support
 * Format: DD-MMM-YY-api.log or DD-MMM-YY-api-1.log, DD-MMM-YY-api-2.log, etc.
 */
function getLogFilePath(): string {
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const month = now.toLocaleDateString('en', { month: 'short' }).toLowerCase();
  const year = String(now.getFullYear()).slice(-2);
  const baseFilename = `${day}-${month}-${year}-api`;
  
  // Find the current rotation number
  let rotationNumber = 0;
  let logFilePath = path.join(logsDir, `${baseFilename}.log`);
  
  // Check if current file exists and is too large
  while (fs.existsSync(logFilePath)) {
    const stats = fs.statSync(logFilePath);
    if (stats.size < MAX_FILE_SIZE) {
      // Current file is under size limit, use it
      break;
    }
    
    // File is too large, try next rotation
    rotationNumber++;
    if (rotationNumber >= MAX_FILES_PER_DAY) {
      // Use the last allowed file (will continue growing)
      rotationNumber = MAX_FILES_PER_DAY - 1;
      logFilePath = path.join(logsDir, `${baseFilename}-${rotationNumber}.log`);
      break;
    }
    
    logFilePath = path.join(logsDir, `${baseFilename}-${rotationNumber}.log`);
  }
  
  return logFilePath;
}

/**
 * Get or create write stream for current day's log file with size rotation
 */
function getLogFileStream(): fs.WriteStream {
  const logFilePath = getLogFilePath();
  return fs.createWriteStream(logFilePath, { flags: 'a' });
}

/**
 * Clean up old log files (older than LOGS_RETENTION_DAYS)
 */
function cleanupOldLogs(): void {
  try {
    const files = fs.readdirSync(logsDir);
    const now = new Date();
    const cutoffDate = new Date(now.getTime() - (LOGS_RETENTION_DAYS * 24 * 60 * 60 * 1000));
    
    for (const file of files) {
      if (file.endsWith('-api.log') || file.match(/-api-\d+\.log$/)) {
        const filePath = path.join(logsDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          console.log(`Cleaned up old log file: ${file}`);
        }
      }
    }
  } catch (error) {
    console.error('Error cleaning up old logs:', error);
  }
}

/**
 * Get current log file statistics
 */
function getLogFileStats() {
  try {
    const files = fs.readdirSync(logsDir);
    const logFiles = files.filter(file => file.endsWith('-api.log') || file.match(/-api-\d+\.log$/));
    
    let totalSize = 0;
    let totalFiles = 0;
    
    for (const file of logFiles) {
      const filePath = path.join(logsDir, file);
      const stats = fs.statSync(filePath);
      totalSize += stats.size;
      totalFiles++;
    }
    
    return {
      totalFiles,
      totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2),
      files: logFiles.map(file => {
        const filePath = path.join(logsDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          sizeMB: (stats.size / (1024 * 1024)).toFixed(2),
          modified: stats.mtime
        };
      })
    };
  } catch (error) {
    console.error('Error getting log file stats:', error);
    return null;
  }
}

// Run cleanup on startup and then every 24 hours
cleanupOldLogs();
setInterval(cleanupOldLogs, 24 * 60 * 60 * 1000);

// Console logging middleware
export const consoleLoggingMiddleware = morgan('dev');

// Simple JSON format for basic morgan logging
const jsonFormat = json(':method :url :status :response-time ms');

// File logging middleware with size-based rotation
export const fileLoggingMiddleware = morgan(jsonFormat, {
  stream: {
    write: (message: string) => {
      try {
        // Create a new stream for each write to ensure proper rotation
        const stream = getLogFileStream();
        stream.write(message);
        stream.end();
      } catch (error) {
        console.error('Error writing to log file:', error);
      }
    }
  }
});

// Enhanced request/response logging middleware with size management
export const requestResponseLoggingMiddleware = 
  (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const startTime = Date.now();
  const logId = uuidv4();
  
  // Capture request data
  const requestData = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    headers: req.headers,
    body: req.body || null
  };

  // Capture response data
  let responseBody: any = null;
  let errorInfo: any = null;

  // Override res.json to capture response body
  const originalJson = res.json;
  res.json = function(data: any) {
    responseBody = data;
    return originalJson.call(this, data);
  };

  // Capture errors
  const originalSend = res.send;
  res.send = function(data: any) {
    if (res.statusCode >= 400) {
      errorInfo = {
        statusCode: res.statusCode,
        data: data
      };
    }
    return originalSend.call(this, data);
  };

  // Log after response is sent
  res.on('finish', () => {
    try {
      const responseTime = Date.now() - startTime;

      // Create a comprehensive JSON log entry
      const logEntry = {
        timestamp: new Date().toISOString(),
        id: logId,
        ip: req.ip,
        request: {
          timestamp: requestData.timestamp,
          method: requestData.method,
          url: requestData.url,
          headers: requestData.headers,
          body: requestData.body
        },
        response: {
          status: res.statusCode,
          headers: res.getHeaders(),
          body: responseBody,
          responseTime: `${responseTime}ms`
        },
        error: errorInfo || false
      };

      // Write JSON log entry with size-based rotation
      const apiLogStream = getLogFileStream();
      apiLogStream.write(JSON.stringify(logEntry) + '\n');
      apiLogStream.end();
    } catch (error) {
      console.error('Error in request/response logging:', error);
    }
  });

  next();
};

// Global error handler middleware with size management
export const errorLoggingMiddleware = (
  err: Error, 
  req: express.Request, 
  res: express.Response, 
  next: express.NextFunction
) => {
  try {
    const errorDetails = {
      message: err.message,
      name: err.name,
      stack: err.stack,
      statusCode: res.statusCode || 500,
      timestamp: new Date().toISOString(),
      url: req.url,
      method: req.method
    };

    // Create error log entry
    const errorLogEntry = {
      timestamp: new Date().toISOString(),
      id: uuidv4(),
      ip: req.ip,
      request: {
        timestamp: new Date().toISOString(),
        method: req.method,
        url: req.url,
        headers: req.headers,
        body: req.body || null
      },
      response: {
        status: res.statusCode || 500,
        headers: res.getHeaders(),
        body: null,
        responseTime: "N/A"
      },
      error: errorDetails
    };

    // Write error log entry with size-based rotation
    const apiLogStream = getLogFileStream();
    apiLogStream.write(JSON.stringify(errorLogEntry) + '\n');
    apiLogStream.end();

    // Send error response if not already sent
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        message: 'Internal Server Error',
        data: null
      });
    }
  } catch (error) {
    console.error('Error in error logging middleware:', error);
  }
};

// Export utility functions
export { getLogFilePath, getLogFileStream, getLogFileStats, cleanupOldLogs }; 