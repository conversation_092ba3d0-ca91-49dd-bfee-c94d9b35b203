/**
 * Complete System Setup Module
 * 
 * This module replaces all database seeders and provides a comprehensive system setup that includes:
 * 1. Database sequences creation (purchase_invoice_entry_seq, raw_material_stock_issuance_custom_entry_id_seq)
 * 2. Admin role creation
 * 3. Default user creation in both Firebase Auth and PostgreSQL
 * 
 * Features:
 * - Firebase-first approach: Creates users in Firebase Auth first, then links to database
 * - Idempotent: Only creates resources if they don't already exist
 * - Consistent: Verifies data consistency between Firebase and database
 * - Comprehensive: Handles both core users and normal users with addresses
 * 
 * Replaced seeders:
 * - 20250204095304-create-admin-role.js
 * - 20250204103720-create-core-users.js  
 * - 20250204103721-create-normal-users.js
 * - 20250520081834-core-user-2.js
 * - 20250520081953-normal-user-2.js
 * - 20250329041919-create-purchase-invoice-entry-seq.js
 * - 20250329041920-create-raw_material_stock_issuance_custom_entry_id_seq.js
 */

import * as admin from 'firebase-admin';
import { sequelizeInit } from '../sequelize_init';
import { CoreUserTable } from '../features/users/core/database/CoreUserTable';
import { NormalUserTable } from '../features/users/sub_feaures/normal_user/database/NormalUserTable';
import { AddressTable } from '../features/address/database/AddressTable';
import { UserRoleTable } from '../features/users/sub_feaures/user_roles/database/UserRoleTable';
import { USER_STAUS } from '../features/users/core/models/UserMisc';
import { ADDRESS_STATUS } from '../features/address/models/AddressMisc';
import { USER_ROLE_STATUS } from '../features/users/sub_feaures/user_roles/models/UserRolesMisc';
import { ICreateCoreUser } from '../features/users/core/models/ICoreUser';
import { ICreateAddress } from '../features/address/models/IAddress';


interface IDefaultUser {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  mobile: string;
  firebaseUID?: string;
  isNormalUser?: boolean;
  address?: Omit<ICreateAddress, 'status' | 'createdById'>;
}

interface ISystemSetupResult {
  success: boolean;
  message: string;
  usersCreated: number;
  usersSkipped: number;
  errors: string[];
}

export class DefaultSystemSetup {
  
  private static readonly DEFAULT_USERS: IDefaultUser[] = [
    {
      firstName: 'tech',
      lastName: 'inject',
      email: '<EMAIL>',
      password: 'Admin@123',
      mobile: '1234567890',
      isNormalUser: false
    },
    {
      firstName: 'admin',
      lastName: 'admin',
      email: '<EMAIL>',
      password: 'Admin@123',
      mobile: '1234567891',
      isNormalUser: true,
      address: {
        street: '123 Main St',
        postalCode: '12345',
        city: 'City',
        state: 'State',
        country: 'Country'
      }
    }
  ];

  /**
   * Complete system setup: creates sequences, roles, and default users
   */
  public static async setupSystem(): Promise<ISystemSetupResult> {
    const result: ISystemSetupResult = {
      success: false,
      message: '',
      usersCreated: 0,
      usersSkipped: 0,
      errors: []
    };

    console.log('🚀 Starting complete system setup...');

    try {
      // Step 1: Create database sequences
      await this.createDatabaseSequences();
      
      // Step 2: Ensure admin role exists
      const adminRole = await this.ensureAdminRoleExists();
      if (!adminRole) {
        throw new Error('Failed to create or find admin role');
      }

      // Step 3: Create default users
      return await this.createDefaultUsers(adminRole);

    } catch (error) {
      const errorMessage = `Critical error in system setup: ${error instanceof Error ? error.message : 'Unknown error'}`;
      result.errors.push(errorMessage);
      result.message = errorMessage;
      console.error(`💥 ${errorMessage}`);
      return result;
    }
  }

  /**
   * Creates default users in both Firebase Auth and PostgreSQL database
   * Only creates users if they don't exist at both levels
   */
  private static async createDefaultUsers(adminRole: UserRoleTable): Promise<ISystemSetupResult> {
    const result: ISystemSetupResult = {
      success: false,
      message: '',
      usersCreated: 0,
      usersSkipped: 0,
      errors: []
    };


    console.log('👥 Starting default user creation process...');

    try {
      // Process each default user
      for (const userConfig of this.DEFAULT_USERS) {
        try {
          const userResult = await this.processUser(userConfig, adminRole.dataValues.id);
          if (userResult.created) {
            result.usersCreated++;
            console.log(`✅ Created user: ${userConfig.email}`);
          } else {
            result.usersSkipped++;
            console.log(`⏭️  Skipped user: ${userConfig.email} (already exists)`);
          }
        } catch (error) {
          const errorMessage = `Failed to process user ${userConfig.email}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMessage);
          console.error(`❌ ${errorMessage}`);
        }
      }

      result.success = result.errors.length === 0 || result.usersCreated > 0;
      result.message = result.success 
        ? `Successfully processed ${result.usersCreated + result.usersSkipped} users (${result.usersCreated} created, ${result.usersSkipped} skipped)`
        : `Failed to create users. Errors: ${result.errors.join(', ')}`;

      console.log(`🎉 Default user creation completed: ${result.message}`);
      return result;

    } catch (error) {
      const errorMessage = `Critical error in default user creation: ${error instanceof Error ? error.message : 'Unknown error'}`;
      result.errors.push(errorMessage);
      result.message = errorMessage;
      console.error(`💥 ${errorMessage}`);
      return result;
    }
  }

  /**
   * Processes a single user - checks existence and creates if needed
   */
  private static async processUser(userConfig: IDefaultUser, adminRoleId: number): Promise<{ created: boolean }> {
    try {
      // Step 1: Check/Create Firebase user first
      let firebaseUser = await this.checkFirebaseUserExists(userConfig.email);
      
      if (!firebaseUser) {
        console.log(`🔥 Creating Firebase user: ${userConfig.email}`);
        firebaseUser = await this.createFirebaseUser(userConfig);
      } else {
        console.log(`✅ Firebase user already exists: ${userConfig.email}`);
      }

      // Step 2: Check if user exists in database using the Firebase UID
      const dbUserExists = await this.checkDatabaseUserByFirebaseUID(firebaseUser.uid);

      if (dbUserExists) {
        console.log(`✅ Database user already exists: ${userConfig.email}`);
        await this.verifyUserConsistency(userConfig, dbUserExists, firebaseUser);
        return { created: false };
      }

      // Step 3: Create database user with the Firebase UID
      console.log(`💾 Creating database user: ${userConfig.email}`);
      await this.createDatabaseUserWithRepos(userConfig, adminRoleId, firebaseUser.uid);
      
      return { created: true };

    } catch (error) {
      console.error(`❌ Error processing user ${userConfig.email}:`, error);
      throw error;
    }
  }

  /**
   * Checks if user exists in Firebase Auth
   */
  private static async checkFirebaseUserExists(email: string): Promise<admin.auth.UserRecord | null> {
    try {
      const user = await admin.auth().getUserByEmail(email);
      return user;
    } catch (error) {
      if ((error as any)?.code === 'auth/user-not-found') {
        return null;
      }
      throw error;
    }
  }

  /**
   * Checks if user exists in database by Firebase UID
   */
  private static async checkDatabaseUserByFirebaseUID(firebaseUID: string): Promise<CoreUserTable | null> {
    return await CoreUserTable.findOne({
      where: {
        firebaseUID: firebaseUID,
        status: USER_STAUS.ACTIVE
      }
    });
  }

  /**
   * Creates user in Firebase Auth
   */
  private static async createFirebaseUser(userConfig: IDefaultUser): Promise<admin.auth.UserRecord> {
    try {
      const createUserData: admin.auth.CreateRequest = {
        email: userConfig.email,
        password: userConfig.password,
        emailVerified: true,
        displayName: `${userConfig.firstName} ${userConfig.lastName}`
      };

      return await admin.auth().createUser(createUserData);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Creates user in database directly (Firebase user already exists)
   */
  private static async createDatabaseUserWithRepos(
    userConfig: IDefaultUser, 
    adminRoleId: number, 
    firebaseUID: string
  ): Promise<void> {
    const transaction = await sequelizeInit.transaction();
    
    try {
      // Create core user
      const coreUserData: ICreateCoreUser = {
        firstName: userConfig.firstName,
        lastName: userConfig.lastName,
        email: userConfig.email,
        password: userConfig.password,
        mobile: userConfig.mobile,
        firebaseUID: firebaseUID,
        status: USER_STAUS.ACTIVE,
        roleId: adminRoleId,
        createdById: 1
      };

      const coreUser = await CoreUserTable.create(coreUserData, { 
        transaction,
        userId: 1 
      });

      // If this is a normal user, create address and normal user record
      if (userConfig.isNormalUser && userConfig.address) {
        // Create address
        const addressData: ICreateAddress = {
          street: userConfig.address.street,
          postalCode: userConfig.address.postalCode,
          city: userConfig.address.city,
          state: userConfig.address.state,
          country: userConfig.address.country,
          status: ADDRESS_STATUS.ACTIVE,
          createdById: 1
        };

        const address = await AddressTable.create(addressData, { 
          transaction,
          userId: 1 
        });

        // Create normal user record
        await NormalUserTable.create({
          coreUserId: coreUser.dataValues.id,
          addressId: address.dataValues.id
        }, { 
          transaction,
          userId: 1 
        });
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Verifies that user data is consistent between Firebase and database
   */
  private static async verifyUserConsistency(userConfig: IDefaultUser, dbUser: CoreUserTable, firebaseUser: admin.auth.UserRecord): Promise<void> {
    // Check if Firebase UID matches database
    if (firebaseUser.uid !== dbUser.dataValues.firebaseUID) {
      console.warn(`⚠️  Inconsistency detected for ${userConfig.email}:`);
      console.warn(`   Database firebaseUID: ${dbUser.dataValues.firebaseUID}`);
      console.warn(`   Firebase UID: ${firebaseUser.uid}`);
    }

    // Check if names match
    const dbFullName = `${dbUser.dataValues.firstName} ${dbUser.dataValues.lastName}`;
    if (firebaseUser.displayName && firebaseUser.displayName !== dbFullName) {
      console.warn(`⚠️  Name mismatch for ${userConfig.email}:`);
      console.warn(`   Database: ${dbFullName}`);
      console.warn(`   Firebase: ${firebaseUser.displayName}`);
    }
  }

  /**
   * Creates required database sequences
   */
  private static async createDatabaseSequences(): Promise<void> {
    console.log('🔢 Creating database sequences...');
    
    try {
      // Create purchase invoice entry sequence
      await sequelizeInit.query(`
        CREATE SEQUENCE IF NOT EXISTS purchase_invoice_entry_seq
        INCREMENT 1
        START 1;
      `);
      console.log('✅ Purchase invoice entry sequence created/verified');

      // Create raw material stock issuance sequence
      await sequelizeInit.query(`
        CREATE SEQUENCE IF NOT EXISTS raw_material_stock_issuance_custom_entry_id_seq
        INCREMENT 1
        START 1;
      `);
      console.log('✅ Raw material stock issuance sequence created/verified');

    } catch (error) {
      console.error('❌ Failed to create database sequences:', error);
      throw error;
    }
  }

  /**
   * Ensures admin role exists in database
   */
  private static async ensureAdminRoleExists(): Promise<UserRoleTable | null> {
    try {
      let adminRole = await UserRoleTable.findOne({
        where: {
          role: 'admin',
          status: USER_ROLE_STATUS.ACTIVE
        }
      });

      if (!adminRole) {
        console.log('📝 Creating admin role...');
        adminRole = await UserRoleTable.create({
          role: 'admin',
          status: USER_ROLE_STATUS.ACTIVE,
          createdById: 1
        }, { 
          userId: 1 
        });
        console.log('✅ Admin role created successfully');
      } else {
        console.log('✅ Admin role already exists');
      }

      return adminRole;
    } catch (error) {
      console.error('❌ Failed to ensure admin role exists:', error);
      return null;
    }
  }
} 