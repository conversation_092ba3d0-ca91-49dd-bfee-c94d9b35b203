import * as admin from 'firebase-admin';
import { sequelizeInit } from '../sequelize_init';
import { QueryTypes } from 'sequelize';
import { UserRoleTable } from '../features/users/sub_feaures/user_roles/database/UserRoleTable';
import { USER_STAUS } from '../features/users/core/models/UserMisc';
import { ADDRESS_STATUS } from '../features/address/models/AddressMisc';
import { USER_ROLE_STATUS } from '../features/users/sub_feaures/user_roles/models/UserRolesMisc';
import { ICreateNormalUser, IUpdateNormalUser } from '../features/users/sub_feaures/normal_user/models/INormalUser';
import { RepoProvider } from '../core/RepoProvider';

interface IDefaultUserCreationResult {
    success: boolean;
    message: string;
    usersCreated: number;
    usersSkipped: number;
    errors: string[];
}

type IDefaultUser = ICreateNormalUser


export class DefaultSystemSetup {

        private static readonly DEFAULT_USERS: IDefaultUser[] = [
        { 
                address: {
                    street: '123 Main St',
                    postalCode: '12345',
                    city: 'Admin-City',
                    state: 'Admin-State',
                    country: 'Admin-Country',
                    status: ADDRESS_STATUS.ACTIVE,
                    createdById: 1 // Use system user
                },
                firstName: 'tech-inject',
                lastName: 'admin',
                email: '<EMAIL>',
                password: 'admin@123',
                mobile: '**********',
                roleId: 1, // Will be set to actual admin role ID
                status: USER_STAUS.ACTIVE,
                firebaseUID: '',
                createdById: 1 // Use system user
            
        },
        {
                address: {
                    street: '123 Main St',
                    postalCode: '12345',
                    city: 'Admin-City',
                    state: 'Admin-State',
                    country: 'Admin-Country',
                    status: ADDRESS_STATUS.ACTIVE,
                    createdById: 1 // Use system user
                },
                firstName: 'tongue-tinglers',
                lastName: 'admin',
                email: '<EMAIL>',
                password: 'Admin@123',
                mobile: '0987654321',
                roleId: 1, // Will be set to actual admin role ID
                status: USER_STAUS.ACTIVE,
                firebaseUID: '',
                createdById: 1 // Use system user
            }
        
    ];

    /**
     * Complete system setup: creates sequences, roles, and default users
     */
    public static async setupSystem(): Promise<IDefaultUserCreationResult> {
        const result: IDefaultUserCreationResult = {
            success: false,
            message: '',
            usersCreated: 0,
            usersSkipped: 0,
            errors: []
        };

        console.log('🚀 Starting complete system setup...');

        try {
            // Step 1: Create database sequences
            await this.createDatabaseSequences();

            // Step 2: Create system user first (to enable logging for subsequent operations)
            console.log('🔧 Creating system user...');
            await this.createSystemUser();

            // Step 3: Ensure admin role exists
            const adminRole = await this.ensureAdminRoleExists();
            if (!adminRole) {
                throw new Error('Failed to create or find admin role');
            }

            // Step 4: Create default users
            return await this.createDefaultUsers();

        } catch (error) {
            const errorMessage = `Critical error in system setup: ${error instanceof Error ? error.message : 'Unknown error'}`;
            result.errors.push(errorMessage);
            result.message = errorMessage;
            console.error(`💥 ${errorMessage}`);
            return result;
        }
    }

    /**
     * Creates default users in both Firebase Auth and PostgreSQL database
     * Only creates users if they don't exist at both levels
     */
    private static async createDefaultUsers(): Promise<IDefaultUserCreationResult> {
        const result: IDefaultUserCreationResult = {
            success: false,
            message: '',
            usersCreated: 0,
            usersSkipped: 0,
            errors: []
        };

        console.log('👥 Starting default user creation process...');

        try {
            // Get admin role for users
            const adminRole = await UserRoleTable.findOne({
                where: {
                    role: 'admin',
                    status: USER_ROLE_STATUS.ACTIVE
                }
            });

            if (!adminRole) {
                throw new Error('Admin role not found');
            }

            // Process each default user
            for (const userConfig of this.DEFAULT_USERS) {
                try {
                    // Set the correct role ID
                    userConfig.roleId = adminRole.dataValues.id;
                    
                    const userResult = await this.processUser(userConfig);
                    if (userResult.created) {
                        result.usersCreated++;
                        console.log(`✅ Created user: ${userConfig.email}`);
                    } else {
                        result.usersSkipped++;
                        console.log(`⏭️  Skipped user: ${userConfig.email} (already exists)`);
                    }
                } catch (error) {
                    const errorMessage = `Failed to process user ${userConfig.email}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                    result.errors.push(errorMessage);
                    console.error(`❌ ${errorMessage}`);
                }
            }

            result.success = result.errors.length === 0 || result.usersCreated > 0;
            result.message = result.success
                ? `Successfully processed ${result.usersCreated + result.usersSkipped} users (${result.usersCreated} created, ${result.usersSkipped} skipped)`
                : `Failed to create users. Errors: ${result.errors.join(', ')}`;

            console.log(`🎉 Default user creation completed: ${result.message}`);
            return result;

        } catch (error) {
            const errorMessage = `Critical error in default user creation: ${error instanceof Error ? error.message : 'Unknown error'}`;
            result.errors.push(errorMessage);
            result.message = errorMessage;
            console.error(`💥 ${errorMessage}`);
            return result;
        }
    }

    /**
     * Processes a single user - checks existence and creates if needed
     */
    private static async processUser(userConfig: IDefaultUser): Promise<{ created: boolean }> {
        const T=await sequelizeInit.transaction()
        try {
            // Step 1: Check/Create Firebase user first
            let firebaseUser = await this.checkFirebaseUserExists(userConfig.email);

            if (!firebaseUser) {
                console.log(`🔥 Creating Firebase user: ${userConfig.email}`);
                firebaseUser = await this.createFirebaseUser(userConfig);
            } else {
                console.log(`✅ Firebase user already exists: ${userConfig.email} - ${firebaseUser.uid}`);
            }
            const repo = RepoProvider.normalUserRepo;
            userConfig.firebaseUID = firebaseUser.uid;

            // Step 2: Check if user exists in database using the Firebase UID
            const dbUserExists = await repo.getByFirebaseId(firebaseUser.uid,T);

            if (dbUserExists.success && dbUserExists.data) {
                //verfiy fb user is same as db user, otherwise update the db user
                if (dbUserExists.data.firebaseUID !== firebaseUser.uid) {
                    console.log(`🔥 Updating Firebase user: ${userConfig.email}`);
                    const updatePayload: IUpdateNormalUser = {
                        id: dbUserExists.data.id,
                        updatedById: 1,
                        firstName: userConfig.firstName,
                        lastName: userConfig.lastName,
                        email: userConfig.email,
                        mobile: userConfig.mobile,
                        roleId: userConfig.roleId,
                        status: userConfig.status,
                        address: {
                            id: dbUserExists.data.address.id,
                            updatedById: 1,
                            ...userConfig.address
                        }
                    };
                    await repo.updateWithFirebaseUID(updatePayload, firebaseUser.uid,T);
                }
                console.log(`✅ Database user already exists: ${dbUserExists.data.coreUserId} - ${userConfig.email} - ${dbUserExists.data.firebaseUID}`)
                return { created: false };
            }

            // Step 3: Create database user with the Firebase UID (system creation)
            console.log(`💾 Creating database user: ${userConfig.email}`);
            await this.createDefaultUser(userConfig);

            await T.commit()
            return { created: true };

        } catch (error) {
            await T.rollback()
            console.error(`❌ Error processing user ${userConfig.email}:`, error);
            throw error;
        }
    }

    /**
     * Checks if user exists in Firebase Auth
     */
    private static async checkFirebaseUserExists(email: string): Promise<admin.auth.UserRecord | null> {
        try {
            const user = await admin.auth().getUserByEmail(email);
            return user;
        } catch (error) {
            if ((error as any)?.code === 'auth/user-not-found') {
                return null;
            }
            throw error;
        }
    }

    /**
     * Creates user in Firebase Auth
     */
    private static async createFirebaseUser(userConfig: IDefaultUser): Promise<admin.auth.UserRecord> {
        try {
            const createUserData: admin.auth.CreateRequest = {
                email: userConfig.email,
                password: userConfig.password,
                emailVerified: true,
                displayName: `${userConfig.firstName} ${userConfig.lastName}`
            };

            return await admin.auth().createUser(createUserData);
        } catch (error) {
            throw error;
        }
    }

    /**
     * Creates the initial system user (ID 1) to enable logging for subsequent operations
     */
    private static async createSystemUser(): Promise<void> {
        try {
            console.log('🔧 Creating system user...');
            
            // Check if system user already exists
            const [existingUser] = await sequelizeInit.query(`
                SELECT id FROM core_users WHERE id = 1;
            `, {
                type: QueryTypes.SELECT
            });

            if (existingUser) {
                console.log('✅ System user already exists');
                return;
            }

            // Get admin role ID (create if doesn't exist)
            let adminRoleId = 1;
            const [existingRole] = await sequelizeInit.query(`
                SELECT id FROM user_roles WHERE role = 'admin';
            `, {
                type: QueryTypes.SELECT
            });

            if (!existingRole) {
                // Create admin role first
                const [roleResult] = await sequelizeInit.query(`
                    INSERT INTO user_roles ("role", "status", "createdById", "createdAt", "updatedAt")
                    VALUES ('sumeet', 'active', null, NOW(), NOW())
                    RETURNING id;
                `, {
                    type: QueryTypes.RAW
                });
                adminRoleId = (roleResult as any)[0].id;
            } else {
                adminRoleId = (existingRole as any).id;
            }

            // Create system user with ID 1
            await sequelizeInit.query(`
                INSERT INTO core_users (
                    "id", "firstName", "lastName", "email", "mobile", "firebaseUID", 
                    "status", "roleId", "createdById", "createdAt", "updatedAt"
                ) VALUES (
                    1, 'System', 'User', '<EMAIL>', '0000000000', 'SYSTEM_USER',
                    'active', :roleId, null, NOW(), NOW()
                );
            `, {
                replacements: { roleId: adminRoleId },
                type: QueryTypes.INSERT
            });

            console.log('✅ System user created successfully with ID: 1');
        } catch (error) {
            // If user already exists, that's fine
            if ((error as any)?.code === '23505') { // Unique constraint violation
                console.log('✅ System user already exists');
                return;
            }
            console.error('❌ Failed to create system user:', error);
            throw error;
        }
    }

    /**
     * Creates a default user directly in database using raw SQL
     */
    private static async createDefaultUser(userConfig: IDefaultUser): Promise<void> {
        const transaction = await sequelizeInit.transaction();
        
        try {
            // Use raw SQL to bypass all Sequelize hooks and logging
            
            // 1. Insert core user
            const [coreUserResult] = await sequelizeInit.query(`
                INSERT INTO core_users (
                    "firstName", "lastName", "email", "mobile", "firebaseUID", 
                    "status", "roleId", "createdById", "createdAt", "updatedAt"
                ) VALUES (
                    :firstName, :lastName, :email, :mobile, :firebaseUID,
                    :status, :roleId, :createdById, NOW(), NOW()
                ) RETURNING id;
            `, {
                replacements: {
                    firstName: userConfig.firstName,
                    lastName: userConfig.lastName,
                    email: userConfig.email,
                    mobile: userConfig.mobile,
                    firebaseUID: userConfig.firebaseUID,
                    status: userConfig.status,
                    roleId: userConfig.roleId,
                    createdById: 1 // Use system user
                },
                transaction,
                type: QueryTypes.INSERT
            });

            const coreUserId = (coreUserResult as any)[0].id;

            // 2. Insert address
            const [addressResult] = await sequelizeInit.query(`
                INSERT INTO addresses (
                    "street", "postalCode", "city", "state", "country", 
                    "status", "createdById", "createdAt", "updatedAt"
                ) VALUES (
                    :street, :postalCode, :city, :state, :country,
                    :status, :createdById, NOW(), NOW()
                ) RETURNING id;
            `, {
                replacements: {
                    street: userConfig.address.street,
                    postalCode: userConfig.address.postalCode,
                    city: userConfig.address.city,
                    state: userConfig.address.state,
                    country: userConfig.address.country,
                    status: ADDRESS_STATUS.ACTIVE,
                    createdById: 1 // Use system user
                },
                transaction,
                type: QueryTypes.INSERT
            });

            const addressId = (addressResult as any)[0].id;

            // 3. Insert normal user
            await sequelizeInit.query(`
                INSERT INTO normal_users ("coreUserId", "addressId") 
                VALUES (:coreUserId, :addressId);
            `, {
                replacements: {
                    coreUserId: coreUserId,
                    addressId: addressId
                },
                transaction,
                type: QueryTypes.INSERT
            });

            await transaction.commit();
            console.log(`✅ System user created successfully with core ID: ${coreUserId}`);
        } catch (error) {
            await transaction.rollback();
            console.error('❌ Failed to create system user:', error);
            throw error;
        }
    }

    /**
     * Creates required database sequences
     */
    private static async createDatabaseSequences(): Promise<void> {
        console.log('🔢 Creating database sequences...');

        try {
            // Create purchase invoice entry sequence
            await sequelizeInit.query(`
        CREATE SEQUENCE IF NOT EXISTS purchase_invoice_entry_seq
        INCREMENT 1
        START 1;
      `);
            console.log('✅ Purchase invoice entry sequence created/verified');

            // Create raw material stock issuance sequence
            await sequelizeInit.query(`
        CREATE SEQUENCE IF NOT EXISTS raw_material_stock_issuance_custom_entry_id_seq
        INCREMENT 1
        START 1;
      `);
            console.log('✅ Raw material stock issuance sequence created/verified');

        } catch (error) {
            console.error('❌ Failed to create database sequences:', error);
            throw error;
        }
    }

    /**
     * Ensures admin role exists in database
     */
    private static async ensureAdminRoleExists(): Promise<UserRoleTable | null> {
        try {
            let adminRole = await UserRoleTable.findOne({
                where: {
                    role: 'admin',
                    status: USER_ROLE_STATUS.ACTIVE
                }
            });

            if (!adminRole) {
                console.log('📝 Creating admin role...');
                // Now we can use normal Sequelize operations since system user (ID 1) exists
                adminRole = await UserRoleTable.create({
                    role: 'admin',
                    status: USER_ROLE_STATUS.ACTIVE,
                    createdById: 1 // Use system user ID
                }, {
                    userId: 1 // Use system user for logging
                });
                console.log('✅ Admin role created successfully');
            } else {
                console.log('✅ Admin role already exists');
            }

            return adminRole;
        } catch (error) {
            console.error('❌ Failed to ensure admin role exists:', error);
            return null;
        }
    }
} 