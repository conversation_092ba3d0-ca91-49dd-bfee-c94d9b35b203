import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { THIRD_PARTY_SYSTEM_USER_TYPE } from "../models/ThirdPartySystemUserMisc";
import { IThirdPartySystemUser, ICreateThirdPartySystemUser, IUpdateThirdPartySystemUser, IThirdPartySystemUserResponse } from "../models/IThirdPartySystemUser";

export interface IThirdPartySystemUserRepo {
    get3rdPartySystemUser(type: THIRD_PARTY_SYSTEM_USER_TYPE, transaction?: Transaction): Promise<APIBaseResponse<IThirdPartySystemUserResponse | null>>;
    create(payload: ICreateThirdPartySystemUser, transaction: Transaction): Promise<APIBaseResponse<IThirdPartySystemUser | null>>;
    update(payload: IUpdateThirdPartySystemUser, transaction: Transaction): Promise<APIBaseResponse<null>>;
    getById(id: number, transaction: Transaction): Promise<APIBaseResponse<IThirdPartySystemUserResponse | null>>;
    getAll(page: number, limit: number, transaction: Transaction): Promise<PaginatedBaseResponse<IThirdPartySystemUserResponse>>;
    getByUserId(userId: number, transaction: Transaction): Promise<APIBaseResponse<IThirdPartySystemUserResponse[]>>;
    delete(id: number, deletedById: number, transaction: Transaction): Promise<APIBaseResponse<null>>;
}
