import { DataTypes, Model } from 'sequelize';
import { IThirdPartySystemUser, ICreateThirdPartySystemUser } from '../models/IThirdPartySystemUser';
import { THIRD_PARTY_SYSTEM_USER_TYPE } from '../models/ThirdPartySystemUserMisc';
import { sequelizeInit } from '../../../sequelize_init';
import { RepoProvider } from '../../../core/RepoProvider';
import { CoreUserTable } from '../../users/core/database/CoreUserTable';

class ThirdPartySystemUserTable extends Model<IThirdPartySystemUser, ICreateThirdPartySystemUser> {
    declare user: CoreUserTable;
    declare createdBy: CoreUserTable;
}

ThirdPartySystemUserTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        type: {
            type: DataTypes.ENUM(...Object.values(THIRD_PARTY_SYSTEM_USER_TYPE)),
            allowNull: false,
        },
        token: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.userId;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        },
    },
    {
        sequelize: sequelizeInit,
        modelName: 'ThirdPartySystemUserTable',
        tableName: 'third_party_system_users',
        timestamps: true,
        underscored: true,
        paranoid: true,
    }
);

ThirdPartySystemUserTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "third_party_system_users",
        instance,
        options
    );
});

ThirdPartySystemUserTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "third_party_system_users",
        instance,
        options
    );
});

ThirdPartySystemUserTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "third_party_system_users",
        instance,
        options
    );
});

export { ThirdPartySystemUserTable };
