import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { THIRD_PARTY_SYSTEM_USER_TYPE } from "./ThirdPartySystemUserMisc";

interface IThirdPartySystemUser extends InterfaceMetaData {
    id: number;
    type: THIRD_PARTY_SYSTEM_USER_TYPE;
    token: string;
    userId: number;
}

interface ICreateThirdPartySystemUser {
    type: THIRD_PARTY_SYSTEM_USER_TYPE;
    token: string;
    userId: number;
    createdById: number;
}

interface IUpdateThirdPartySystemUser extends Omit<ICreateThirdPartySystemUser, "createdById"> {
    id: number;
    updatedById: number;
}

interface IThirdPartySystemUserResponse {
    id: number;
    type: THIRD_PARTY_SYSTEM_USER_TYPE;
    token: string;
    userId: number;
    
}

export { 
    IThirdPartySystemUser, 
    ICreateThirdPartySystemUser, 
    IUpdateThirdPartySystemUser, 
    IThirdPartySystemUserResponse 
};
