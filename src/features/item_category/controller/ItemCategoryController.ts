import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { ITEM_CATEGORY_STAUS } from "../models/ItemCategoryMisc";
import { ICreateItemCategory } from "../models/IItemCategory";
import { sequelizeInit } from "../../../sequelize_init";
import { HelperMethods } from "../../../core/HelperMethods";

export class ItemCategoryController {

    static async create(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id");

            const payload = pick(req.body, ["name", "status",]) as ICreateItemCategory;
            payload.status = ITEM_CATEGORY_STAUS.ACTIVE;
            payload.createdById = Number(userId!);
            payload.name = payload.name.toLowerCase().trim();

            const result = await RepoProvider.itemCategoryRepo.create(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async update(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = Number(get(req.params, "id"));

            const userId = get(req, "user_id",);

            const payload = {
                ...req.body,
                updatedById: Number(userId!),
            };

            const result = await RepoProvider.itemCategoryRepo.update(id, payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async delete(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const ids: any = pick(req.body, "ids");
            const userId = get(req, "user_id",);


            const result = await RepoProvider.itemCategoryRepo.delete(ids, Number(userId), T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not deleted"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
            const result = await RepoProvider.itemCategoryRepo.getAll(page, pageSize, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async searchByText(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            await T.commit()
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
        const text = get(req.query, "text") as string;
        const result = await RepoProvider.itemCategoryRepo.searchByText(text, T);
        if (!result.success) {
            res.status(500).send(result);
            return;
        }
        res.status(200).send(result);
    }



    static async getById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.itemCategoryRepo.getById(Number(id), T);
            if (!result.success) {
                throw new Error('Data not exists')
            }
            T.commit()
            res.status(200).send(result);
        } catch (error) {
            T.rollback()
            res.status(404).send(error instanceof Error ? error.message : 'Something went wrong :Data not exists.');
        }
    }

    static async search(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const text = get(req.query, "text") as string;
            const result = await RepoProvider.itemCategoryRepo.search(text, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
}