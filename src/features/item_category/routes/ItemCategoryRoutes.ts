import * as express from "express";
import { ItemCategoryValidations } from "../validations/ItemCategoryValidations";
import { ItemCategoryController } from "../controller/ItemCategoryController";
import { permissionsMiddleware } from "../../../middlewares/permissionsMiddleware";
import { AppPermissions } from "../../users/sub_feaures/user_permissions/AppPermissions";

const apiInitialPath = "/item-categories";
const itemCategoryRouter = express.Router();

itemCategoryRouter.post(apiInitialPath + "/create",
    permissionsMiddleware(AppPermissions.ITEM_CATEGORY.CREATE),
    ItemCategoryValidations.validateCreate, ItemCategoryController.create);

itemCategoryRouter.put(apiInitialPath + "/update/:id",
    permissionsMiddleware(AppPermissions.ITEM_CATEGORY.UPDATE),
    ItemCategoryValidations.validateUpdate, ItemCategoryController.update);

itemCategoryRouter.delete(apiInitialPath + "/delete", ItemCategoryValidations.validateDelete, ItemCategoryController.delete);

itemCategoryRouter.get(apiInitialPath + "/search",
    permissionsMiddleware(AppPermissions.ITEM_CATEGORY.READ),

    ItemCategoryValidations.validateSearch, ItemCategoryController.search);

itemCategoryRouter.get(apiInitialPath + "/searchByText",
    permissionsMiddleware(AppPermissions.ITEM_CATEGORY.READ),
    ItemCategoryValidations.validateSearchByText, ItemCategoryController.searchByText);

itemCategoryRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.ITEM_CATEGORY.READ),

    ItemCategoryValidations.validateGetAll, ItemCategoryController.getAll);

itemCategoryRouter.get(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.ITEM_CATEGORY.READ),
    ItemCategoryValidations.validateGetById, ItemCategoryController.getById);

export { itemCategoryRouter };