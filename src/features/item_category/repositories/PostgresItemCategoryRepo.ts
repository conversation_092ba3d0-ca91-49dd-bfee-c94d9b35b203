import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { ItemCategoryTable } from "../database/ItemCategoryTable";
import { ICreateItemCategory } from "../models/IItemCategory";
import { ITEM_CATEGORY_STAUS } from "../models/ItemCategoryMisc";
import { IItemCategoryRepo } from "./IItemCategoryRepo";
import { Op, Transaction, UniqueConstraintError } from "sequelize";

export class PostgresItemCategoryRepo implements IItemCategoryRepo {
    delete(ids: number[], deletedById: number,transaction:Transaction): Promise<APIBaseResponse<null>> {
        throw new Error("Method not implemented.");
    }

    async create(payload: ICreateItemCategory,transaction:Transaction): Promise<APIBaseResponse<ItemCategoryTable | null>> {
        try {
            const result = await ItemCategoryTable.create(payload, {
                userId: payload.createdById,
                transaction
            });
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Category already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(id: number, payload: ICreateItemCategory,transaction:Transaction): Promise<APIBaseResponse<null>> {
        try {
            const deletionUpdates = {
                deletedAt: new Date(),
                deletedById: payload.updatedById,
            };
            if (payload.status === ITEM_CATEGORY_STAUS.DELETED) {
                Object.assign(payload, deletionUpdates);
            }
            await ItemCategoryTable.update(payload, {
                where: {
                    id: id
                },
                userId: payload.updatedById!,
                individualHooks: true,
                transaction

            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Category already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<ItemCategoryTable> | null>> {
        try {
            const offset = (page - 1) * pageSize;
            const { count, rows } = await ItemCategoryTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: {
                    status: ITEM_CATEGORY_STAUS.ACTIVE,
                    deletedAt: null,
                },
                transaction
            });

            const totalPages = Math.ceil(count / pageSize);

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchByText(text: string,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<ItemCategoryTable> | null>> {
        try {
            const { count, rows } = await ItemCategoryTable.findAndCountAll({
                limit: 10,
                order: [['createdAt', 'DESC']],
                where: {

                    name: {
                        [Op.iLike]: `%${text}%`
                    },

                    status: ITEM_CATEGORY_STAUS.ACTIVE
                },
                transaction
            });

            const totalPages = 1;

            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number,transaction:Transaction): Promise<APIBaseResponse<ItemCategoryTable | null>> {
        try {
            const result = await ItemCategoryTable.findByPk(id,{
                transaction:transaction
            });
            if(!result){
                return HelperMethods.getErrorResponse('Data not exists')
            }
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }



    async search(text: string,transaction:Transaction): Promise<APIBaseResponse<ItemCategoryTable[] | null>> {
        try {
            const result = await ItemCategoryTable.findAll({
                where: {
                    name: {
                        [Op.iLike]: `%${text}%`
                    },
                    status: ITEM_CATEGORY_STAUS.ACTIVE,
                },
                limit: 5,
                transaction
            });
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
}