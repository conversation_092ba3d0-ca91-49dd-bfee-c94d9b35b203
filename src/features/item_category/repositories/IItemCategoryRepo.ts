import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { ItemCategoryTable } from "../database/ItemCategoryTable";
import { ICreateItemCategory, } from "../models/IItemCategory";

export interface IItemCategoryRepo {
    create(payload: ICreateItemCategory,transaction:Transaction): Promise<APIBaseResponse<ItemCategoryTable | null>>;

    update(id: Number, payload: ICreateItemCategory,transaction:Transaction): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<ItemCategoryTable> | null>>;

    searchByText(text: string,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<ItemCategoryTable> | null>>;

    getById(id: number,transaction:Transaction): Promise<APIBaseResponse<ItemCategoryTable | null>>;

    delete(ids: number[], deletedById: number,transaction:Transaction): Promise<APIBaseResponse<null>>;

    search(text: string,transaction:Transaction): Promise<APIBaseResponse<ItemCategoryTable[] | null>>
}