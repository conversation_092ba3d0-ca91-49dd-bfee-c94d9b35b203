import { parseUserToMetaUser } from "../../users/core/parser/core_user_parser"
import { ParsedItemCategory } from "../models/IItemCategory"

export class ItemCategoryUtils {
    static parseItemCategory = (category: any): ParsedItemCategory => {
        console.log(category)
        const data: ParsedItemCategory = {
            id: category.id,
            name: category.name,
            status: category.status,
            createdBy: parseUserToMetaUser(category.creator),
            updatedBy: category?.updater ? parseUserToMetaUser(category.updater) : category.updater,
            deletedBy: category?.deleter ? parseUserToMetaUser(category.deleter) : category.deleter,
            createdAt: category.created_at,
            updatedAt: category.updated_at,
            deletedAt: category.deleted_at,
        }
        return data
    }
}