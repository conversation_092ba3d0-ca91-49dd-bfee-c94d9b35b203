import { InterfaceMetaData, ParsedMeta } from "../../../core/CoreInterfaces";
import { ITEM_CATEGORY_STAUS } from "./ItemCategoryMisc";

interface IItemCategory extends InterfaceMetaData {
    id: number;
    name: string;
    status: ITEM_CATEGORY_STAUS;
}

interface ICreateItemCategory extends InterfaceMetaData {
    name: string;
    status: ITEM_CATEGORY_STAUS;
}


interface ParsedItemCategory extends ParsedMeta {
    id: number;
    name: string;
    status: ITEM_CATEGORY_STAUS;
}

export { IItemCategory, ICreateItemCategory,ParsedItemCategory };