import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { FACTORY_GATE_STAUS } from "./FactoryGateMisc";

interface IFactoryGate extends InterfaceMetaData {
    id: number;
    name: string;
    description: string;
    status: FACTORY_GATE_STAUS;
}

interface ICreateFactoryGate extends InterfaceMetaData {
    name: string;
    description: string;
    status: FACTORY_GATE_STAUS
}


export { IFactoryGate, ICreateFactoryGate };