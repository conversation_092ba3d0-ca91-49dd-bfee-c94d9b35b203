import * as express from "express";
import { FactoryGateValidations } from "../validations/FactoryGateValidations";
import { FactoryGateController } from "../controller/FactoryGateController";
import { AppPermissions } from "../../users/sub_feaures/user_permissions/AppPermissions";
import { permissionsMiddleware } from "../../../middlewares/permissionsMiddleware";

const apiInitialPath = "/factory-gates";
const factoryGatesRouter = express.Router();

factoryGatesRouter.post(apiInitialPath + "/create",
    permissionsMiddleware(AppPermissions.FACTORY_GATE.CREATE),
    FactoryGateValidations.validateCreate, FactoryGateController.create);

factoryGatesRouter.put(apiInitialPath + "/update/:id",
    permissionsMiddleware(AppPermissions.FACTORY_GATE.UPDATE),
    FactoryGateValidations.validateUpdate, FactoryGateController.update);

factoryGatesRouter.delete(apiInitialPath + "/delete", FactoryGateValidations.validateDelete, FactoryGateController.delete);

factoryGatesRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.FACTORY_GATE.READ),
    FactoryGateValidations.validateGetAll, FactoryGateController.getAll);

factoryGatesRouter.get(apiInitialPath + "/searchByText",
    permissionsMiddleware(AppPermissions.FACTORY_GATE.READ),
    FactoryGateValidations.validateSearchByText, FactoryGateController.searchByText);

factoryGatesRouter.get(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.FACTORY_GATE.READ),
    FactoryGateValidations.validateGetById, FactoryGateController.getById);

export { factoryGatesRouter };