import { z } from "zod";

export class FactoryGateSchema {
    static createSchema =
        z.object({
            name: z.string().min(3, "Name must be at least 3 characters long").max(50, "Name must be less than 50 characters long"),
            description: z.string().optional().nullable(),

        });
    static searchByText =
        z.object({
            text: z.string().min(3, "Invalid request"),
        });

}