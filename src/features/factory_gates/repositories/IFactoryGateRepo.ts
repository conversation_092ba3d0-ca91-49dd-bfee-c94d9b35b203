import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { FactoryGateTable } from "../database/FactoryGateTable";
import { ICreateFactoryGate } from "../models/IFactoryGate";

export interface IFactoryGateRepo {
    create(vendor: ICreateFactoryGate, transaction: Transaction): Promise<APIBaseResponse<FactoryGateTable | null>>;

    update(id: number, Vendor: ICreateFactoryGate, transaction: Transaction): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<FactoryGateTable> | null>>;

    searchByText(text: string, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<FactoryGateTable> | null>>;

    getById(id: number, transaction: Transaction): Promise<APIBaseResponse<FactoryGateTable | null>>;

    delete(ids: Number[], deletedById: Number, transaction: Transaction): Promise<APIBaseResponse<null>>;
}