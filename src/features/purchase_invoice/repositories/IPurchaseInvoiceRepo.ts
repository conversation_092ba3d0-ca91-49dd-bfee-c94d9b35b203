import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { IPurchaseInvoiceAddNewItemRequest, IPurchaseInvoiceDetailedResponse, IPurchaseInvoiceRequest, IPurchaseInvoiceResponse, IPurchaseInvoiceUpdateRequest } from "../models/IPurchaseInvoice";

export interface IPurchaseInvoiceRepo {

    create(purchaseInvoice: IPurchaseInvoiceRequest, transaction: Transaction): Promise<APIBaseResponse<null>>;

    update(purchaseInvoice: IPurchaseInvoiceUpdateRequest, transaction: Transaction): Promise<APIBaseResponse<null>>;

    edit(purchaseInvoice: IPurchaseInvoiceUpdateRequest, transaction: Transaction): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IPurchaseInvoiceResponse> | null>>;

    getByDateRange(startDate: Date, endDate: Date, page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IPurchaseInvoiceResponse> | null>>;

    getById(id: number, transaction: Transaction): Promise<APIBaseResponse<IPurchaseInvoiceDetailedResponse | null>>;

    addNewItem(request: IPurchaseInvoiceAddNewItemRequest, transaction: Transaction): Promise<APIBaseResponse<null>>;

    searchByAny(text: string, page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IPurchaseInvoiceResponse> | null>>;
}                       