import * as express from "express";
import { PurchaseInvoiceValidations } from "../validations/PurchaseInvoiceValidations";
import { PurchaseInvoiceController } from "../controller/PurchaseInvoiceController";
import { permissionsMiddleware } from "../../../middlewares/permissionsMiddleware";
import { AppPermissions } from "../../users/sub_feaures/user_permissions/AppPermissions";

const apiInitialPath = "/purchase-invoices";
const purchaseInvoicesRouter = express.Router();

purchaseInvoicesRouter.post(apiInitialPath + "/create",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.RECEIVE),
    PurchaseInvoiceValidations.validateCreate, PurchaseInvoiceController.create);

// PurchaseInvoicesRouter.put(apiInitialPath + "/update/:id", PurchaseInvoiceValidations.validateUpdate, PurchaseInvoiceController.update);

// PurchaseInvoicesRouter.delete(apiInitialPath + "/delete", PurchaseInvoiceValidations.validateDelete, PurchaseInvoiceController.delete);

// purchaseInvoicesRouter.put(apiInitialPath + "/add-new/:id", PurchaseInvoiceValidations.validateAddNewItem, PurchaseInvoiceController.addNewItem);

purchaseInvoicesRouter.put(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.UPDATE_PURCHASE),
    PurchaseInvoiceValidations.validateUpdate, PurchaseInvoiceController.update);

purchaseInvoicesRouter.put(apiInitialPath + "/edit/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.UPDATE_PURCHASE_FULL),
    PurchaseInvoiceValidations.validateUpdateFull, PurchaseInvoiceController.edit);

purchaseInvoicesRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_PURCHASE),
    PurchaseInvoiceValidations.validateGetAll, PurchaseInvoiceController.getAll);


purchaseInvoicesRouter.get(apiInitialPath + "/by-date-range",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_PURCHASE),
    PurchaseInvoiceValidations.validateGetByDateRange, PurchaseInvoiceController.getByDateRange);

purchaseInvoicesRouter.get(apiInitialPath + "/search",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_PURCHASE),
    PurchaseInvoiceValidations.validateSearchByAny, PurchaseInvoiceController.searchByAny);

purchaseInvoicesRouter.get(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_PURCHASE),
    PurchaseInvoiceValidations.validateGetById, PurchaseInvoiceController.getById);

export { purchaseInvoicesRouter };