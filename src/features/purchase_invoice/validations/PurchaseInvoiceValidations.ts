import { Request, Response, NextFunction } from "express";
import { PurchaseInvoiceSchema } from "./PurchaseInvoiceSchema";
import { HelperMethods } from "../../../core/HelperMethods";
import { get, isArray, pick } from "lodash";
import { CoreSchemas } from "../../../core/CoreSchemas";

export class PurchaseInvoiceValidations {
    static validateCreate = (req: Request, res: Response, next: NextFunction) => {

        const result = PurchaseInvoiceSchema.createSchema.safeParse(req.body);
        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {


        const id = get(req.params, "id");
        if (!id) {
            res.status(400).send(HelperMethods.getErrorResponse("Id is required"));
            return;
        }
        else {
            try {
                parseInt(id);
            }
            catch (error) {
                res.status(400).send(HelperMethods.getErrorResponse("Id is not a number"));
                return;
            }
        }

        const result = PurchaseInvoiceSchema.updateSchema.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }
    static validateUpdateFull = (req: Request, res: Response, next: NextFunction) => {


        const id = get(req.params, "id");
        if (!id) {
            res.status(400).send(HelperMethods.getErrorResponse("Id is required"));
            return;
        }
        else {
            try {
                parseInt(id);
            }
            catch (error) {
                res.status(400).send(HelperMethods.getErrorResponse("Id is not a number"));
                return;
            }
        }

        const result = PurchaseInvoiceSchema.updateSchema.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }

    static validateDelete = (req: Request, res: Response, next: NextFunction) => {

        const ids = pick(req.body, "ids");
        if (!ids || !isArray(ids)) {
            res.status(400).send(HelperMethods.getErrorResponse("Ids are required"));
            return;
        }
        for (const id of ids) {
            if (typeof id !== "number") {
                res.status(400).send(HelperMethods.getErrorResponse("Ids are required"));
                return;
            }
        }
        return next();
    }

    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {

        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }

    static validateGetById = (req: Request, res: Response, next: NextFunction) => {

        const id = get(req.params, "id");
        if (!id || typeof Number(id) !== "number") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid id"));
            return;
        }

        return next();
    }

    static validateAddNewItem = (req: Request, res: Response, next: NextFunction) => {

        const id = get(req.params, "id");
        if (!id || typeof Number(id) !== "number") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid id"));
            return;
        }
        const result = PurchaseInvoiceSchema.addNewItemSchema.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }


    static validateSearchByAny = (req: Request, res: Response, next: NextFunction) => {

        const result = PurchaseInvoiceSchema.searchByAny.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }


        return next();
    }

    static validateGetByDateRange = (req: Request, res: Response, next: NextFunction) => {

        const startDate = get(req.query, "startDate");
        const endDate = get(req.query, "endDate");

        if (!startDate || !endDate) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        /* vaidate date type */
        if (typeof startDate !== "string" || typeof endDate !== "string") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
        }

        /* check if dates strings are in yyyy-mm-dd format */
        if (!(startDate as string).match(/^\d{4}-\d{2}-\d{2}$/) || !(endDate as string).match(/^\d{4}-\d{2}-\d{2}$/)) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid date format, must be yyyy-mm-dd"));
            return;
        }

        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }


        return next();
    }

}