import { z } from "zod";

export class PurchaseInvoiceSchema {

    static receivedItemSchema = z.object({
        rawMaterialId: z.number().int().positive("Invalid raw material"),
        totalQty: z.number().positive("Total quantity must be a positive number"),
        price: z.number().positive("Price must be a positive number"),
        rejectedQty: z.number().nonnegative("Rejected quantity must be a positive number"),
        rejectionReason: z.string().min(8, "Rejection reason must be at least 8 characters long").max(500, "Rejection reason must be up to 500 characters long").nullable(),
        rejectedById: z.number().int().positive("Invalid rejected by").nullable(),
        holdQty: z.number().nonnegative("Hold quantity must be a positive number"),
        holdReason: z.string().min(8, "Hold reason must be at least 8 characters long").max(500, "Hold reason must be up to 500 characters long").nullable(),
        holdById: z.number().int().positive("Invalid hold by").nullable(),
        excessQty: z.number().nonnegative("Excess quantity must be a positive number").optional(),
        replaceableQty: z.number().nonnegative("Replaceable quantity must be a positive number").optional(),
    });

    static createSchema =
        z.object({
            invoiceNumber: z.string().min(3, "Invoice number must be at least 3 characters long").max(50, "Invoice number must be up to 50 characters long"),
            invoiceDate: z.string(),
            poNumber: z.string().min(3, "PO number must be at least 3 characters long").max(80, "PO number must be up to 80 characters long"),
            poDate: z.string(),
            supplierId: z.number().int().positive("Invalid supplier"),
            factoryGateId: z.number().int().positive("Invalid factory gate"),
            purchasedById: z.number().int().positive("Invalid purchased by id"),
            purchaseOrderId: z.number().int().positive("Invalid purchase order").nullable().optional(),
            rawMaterials: z.array(
                this.receivedItemSchema
            ),
        });

    static updateSchema =
        z.object({
            invoiceNumber: z.string().min(3, "Invoice number must be at least 3 characters long").max(50, "Invoice number must be up to 50 characters long"),
            invoiceDate: z.string(),
            poNumber: z.string().min(3, "PO number must be at least 3 characters long").max(80, "PO number must be up to 80 characters long"),
            poDate: z.string(),
            supplierId: z.number().int().positive("Invalid supplier"),
            factoryGateId: z.number().int().positive("Invalid factory gate"),
            purchasedById: z.number().int().positive("Invalid purchased by id"),
            rawMaterials: z.array(
                this.receivedItemSchema
            ),
        });

    static addNewItemSchema =
        z.object({
            rawMaterials: z.array(
                this.receivedItemSchema,
            ),
        });


    static searchByAny =
        z.object({
            text: z.string().min(3, "Invlaid request"),
        });
}