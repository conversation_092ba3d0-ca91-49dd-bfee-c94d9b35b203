import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get } from "lodash";
import { IPurchaseInvoiceAddNewItemRequest, IPurchaseInvoiceRequest, IPurchaseInvoiceUpdateRequest } from "../models/IPurchaseInvoice";
import { PURCHASE_INVOICE_STATUS } from "../models/PurchaseInvoiceMisc";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";

export class PurchaseInvoiceController {

    static async create(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id",);
    
            const payload: IPurchaseInvoiceRequest = {
                invoiceNumber: req.body.invoiceNumber,
                invoiceDate: req.body.invoiceDate,
                supplierId: req.body.supplierId,
                status: PURCHASE_INVOICE_STATUS.ACTIVE,
                createdById: Number(userId!),
                factoryGateId: req.body.factoryGateId,
                rawMaterials: req.body.rawMaterials,
                poNumber: req.body.poNumber,
                poDate: req.body.poDate,
                purchasedById: req.body.purchasedById,
                purchaseOrderId: req.body.purchaseOrderId,
            };
    
            const result = await RepoProvider.purchaseInvoiceRepo.create(payload,T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
    static async update(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = Number(get(req.params, "id"));
            const userId = get(req, "user_id",);
    
            const payload: IPurchaseInvoiceUpdateRequest = {
                id: id,
                invoiceNumber: req.body.invoiceNumber,
                invoiceDate: req.body.invoiceDate,
                supplierId: req.body.supplierId,
                status: PURCHASE_INVOICE_STATUS.ACTIVE,
                updatedById: Number(userId!),
                factoryGateId: req.body.factoryGateId,
                rawMaterials: req.body.rawMaterials,
                poNumber: req.body.poNumber,
                poDate: req.body.poDate,
                purchasedById: req.body.purchasedById,
            };
    
            const result = await RepoProvider.purchaseInvoiceRepo.update(payload,T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
    static async edit(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = Number(get(req.params, "id"));
            const userId = get(req, "user_id",);
    
            const payload: IPurchaseInvoiceUpdateRequest = {
                id: id,
                invoiceNumber: req.body.invoiceNumber,
                invoiceDate: req.body.invoiceDate,
                supplierId: req.body.supplierId,
                status: PURCHASE_INVOICE_STATUS.ACTIVE,
                updatedById: Number(userId!),
                factoryGateId: req.body.factoryGateId,
                rawMaterials: req.body.rawMaterials,
                poNumber: req.body.poNumber,
                poDate: req.body.poDate,
                purchasedById: req.body.purchasedById,
            };
    
            const result = await RepoProvider.purchaseInvoiceRepo.edit(payload,T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
    
            const result = await RepoProvider.purchaseInvoiceRepo.getAll(page, pageSize,T);
    
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.purchaseInvoiceRepo.getById(Number(id),T);
            if (!result.success) {
             throw new Error(result.message)
            }
            res.status(200).send(result);
            await T.commit()
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async addNewItem(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const userId = get(req, "user_id");
    
            const payload: IPurchaseInvoiceAddNewItemRequest = {
                id: Number(id),
                updatedById: Number(userId!),
                rawMaterials: req.body.rawMaterials,
            };
            const result = await RepoProvider.purchaseInvoiceRepo.addNewItem(payload,T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not added"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async searchByAny(req: Request, res: Response) {
        const T = await sequelizeInit.transaction()
        try {
            const text = req.query.text;
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
            const result = await RepoProvider.purchaseInvoiceRepo.searchByAny(text as string, page, pageSize,T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }


    static async getByDateRange(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const startDateString = get(req.query, "startDate") as string;
            const endDateString = get(req.query, "endDate") as string;
    
            const startDateData = startDateString.split("-");
            const endDateData = endDateString.split("-");
    
            const startDate = new Date(Number(startDateData[0]), Number(startDateData[1]) - 1, Number(startDateData[2]));
            const endDate = new Date(Number(endDateData[0]), Number(endDateData[1]) - 1, Number(endDateData[2]));
    
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
    
    
            const result = await RepoProvider.purchaseInvoiceRepo.getByDateRange(
                startDate,
                endDate,
                page, pageSize,T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

}