import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { DebitNoteTable } from "../../debit_note/database/DebitNoteTable";
import { PurchaseOrderTable } from "../../purchase_order/database/PurchaseOrderTable";
import { RawMaterialRejectionTable } from "../../raw_material_stock/database/RawMaterialRejectionTable";
import { IRawMaterialRejection } from "../../raw_material_stock/models/IRawMaterialRejection";
import { ISupplier } from "../../supplier/models/ISupplier";
import { PURCHASE_INVOICE_STATUS } from "./PurchaseInvoiceMisc";

interface ICreatePurchaseInvoice {
    invoiceNumber: string;
    invoiceDate: Date;
    poNumber: string;
    poDate: Date;
    supplierId: number;
    status: PURCHASE_INVOICE_STATUS;
    purchasedById: number;
    factoryGateId: number;
    createdById: number;
}

interface IRawMaterialReceivedItem {
    rawMaterialId: number;
    storageLocationId: number | null;
    totalQty: number;
    price: number;
    rejectedQty: number;
    rejectionReason: string | null;
    rejectedById: number | null;
    holdQty: number;
    holdReason: string | null;
    holdById: number | null;
    excessQty:number,
    replaceableQty: number;
}

interface IRawMaterialReceivedItemResponse extends IRawMaterialReceivedItem {
    rawMaterial: string;
    unit: string;

}

interface IPurchaseInvoiceRequest extends ICreatePurchaseInvoice {
    factoryGateId: number;
    rawMaterials: IRawMaterialReceivedItem[];
    purchaseOrderId?: number;

}

interface IPurchaseInvoiceUpdateRequest extends Omit<IPurchaseInvoiceRequest, "createdById"> {
    id: number;
    factoryGateId: number;
    rawMaterials: IRawMaterialReceivedItem[];
    updatedById: number;
}


interface IPurchaseInvoice extends ICreatePurchaseInvoice, InterfaceMetaData {

}

interface IPurchaseInvoiceResponse extends IPurchaseInvoice {
    supplier: string;
    entryId: string;
}


interface IPurchaseInvoiceAddNewItemRequest {
    id: number;
    updatedById: number;
    rawMaterials: IRawMaterialReceivedItem[];
}

interface IPurchaseInvoiceDetailedResponse extends IPurchaseInvoice {
    supplier: ISupplier;
    rawMaterials: IRawMaterialReceivedItemResponse[];
    debitNotes: DebitNoteTable[];
    rawMaterialRejections: IRawMaterialRejection[];
    purchaseOrder:PurchaseOrderTable | null;
}


interface ICreatePurchaseInvoiceEntryMapping {
    purchaseInvoiceId: number;
    entryNumber: string;
}

interface IPurchaseInvoiceEntryMapping extends ICreatePurchaseInvoiceEntryMapping {
    id: number;
}

export { IPurchaseInvoice, IPurchaseInvoiceResponse, ICreatePurchaseInvoice, IPurchaseInvoiceRequest, IPurchaseInvoiceDetailedResponse, IPurchaseInvoiceAddNewItemRequest, IRawMaterialReceivedItem, IRawMaterialReceivedItemResponse, IPurchaseInvoiceEntryMapping, ICreatePurchaseInvoiceEntryMapping, IPurchaseInvoiceUpdateRequest };