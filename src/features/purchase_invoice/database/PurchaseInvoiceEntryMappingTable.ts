import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreatePurchaseInvoiceEntryMapping, IPurchaseInvoiceEntryMapping } from '../models/IPurchaseInvoice';


class PurchaseInvoiceEntryMappingTable extends Model<IPurchaseInvoiceEntryMapping, ICreatePurchaseInvoiceEntryMapping> {

}

PurchaseInvoiceEntryMappingTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        purchaseInvoiceId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.purchaseInvoiceId;
                if (value) {
                    return Number(value);
                }
            },
        },
        entryNumber: {
            type: DataTypes.STRING,
            allowNull: false,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'purchase_invoices_entry_mapping',
    },
);
export { PurchaseInvoiceEntryMappingTable };