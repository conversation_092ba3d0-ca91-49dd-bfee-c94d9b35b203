import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreatePurchaseInvoice, IPurchaseInvoice } from '../models/IPurchaseInvoice';
import { PURCHASE_INVOICE_STATUS } from '../models/PurchaseInvoiceMisc';
import { SupplierTable } from '../../supplier/database/SupplierTable';
import { RawMaterialRejectionTable } from '../../raw_material_stock/database/RawMaterialRejectionTable';
import { RawMaterialStockInTable } from '../../raw_material_stock/database/RawMaterialStockInTable';
import { RepoProvider } from '../../../core/RepoProvider';
import { PurchaseInvoiceEntryMappingTable } from './PurchaseInvoiceEntryMappingTable';
import { RawMaterialHoldTable } from '../../raw_material_stock/database/RawMaterialHoldTable';
import { CoreUserTable } from '../../users/core/database/CoreUserTable';
import { DebitNoteTable } from '../../debit_note/database/DebitNoteTable';
import { RawMaterialExcessEntryTable } from '../../raw_material/database/RawMaterialExcessEntry';
import { RawMaterialReplacementEntryTable } from '../../raw_material/database/RawMaterialReplacementEntry';


class PurchaseInvoiceTable extends Model<IPurchaseInvoice, ICreatePurchaseInvoice> {

    declare supplier: SupplierTable;
    declare purchasedBy: CoreUserTable;
    declare rawMaterialRejections: RawMaterialRejectionTable[];
    declare rawMaterialHolds: RawMaterialHoldTable[];
    declare rawMaterialStockInEntries: RawMaterialStockInTable[];
    declare entryMapping?: PurchaseInvoiceEntryMappingTable;
    declare debitNotes: DebitNoteTable[];
    declare rawMaterialExcessEntry: RawMaterialExcessEntryTable [];
    declare rawMaterialReplacementEntry: RawMaterialReplacementEntryTable[];
}

PurchaseInvoiceTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        invoiceNumber: {
            type: DataTypes.STRING,
            allowNull: false,
        },

        invoiceDate: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        poNumber: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        poDate: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        purchasedById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.purchasedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        factoryGateId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.factoryGateId;
                if (value) {
                    return Number(value);
                }
            },
        },
        supplierId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.supplierId;
                if (value) {
                    return Number(value);
                }
            },
        },

        status: {
            type: DataTypes.ENUM(...Object.values(PURCHASE_INVOICE_STATUS)),
            allowNull: false,
        },

        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'purchase_invoices',
        timestamps: true,
        paranoid: true,
    },
);

PurchaseInvoiceTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "PurchaseInvoice",
        instance,
        options
    );
});

PurchaseInvoiceTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "PurchaseInvoice",
        instance,
        options
    );
});

PurchaseInvoiceTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "PurchaseInvoice",
        instance,
        options
    );
});

export { PurchaseInvoiceTable };