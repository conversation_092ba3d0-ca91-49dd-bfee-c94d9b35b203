import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { AddressTable } from "../database/AddressTable";
import { ICreateAddress, } from "../models/IAddress";
import { ADDRESS_STATUS } from "../models/AddressMisc";
import { IAddressRepo } from "./IAddressRepo";
import { Transaction, UniqueConstraintError } from "sequelize";

export class PostgresAddressRepo implements IAddressRepo {
    delete(ids: number[], deletedById: number, transaction: Transaction): Promise<APIBaseResponse<null>> {
        throw new Error("Method not implemented.");
    }

    async create(address: ICreateAddress, transaction: Transaction): Promise<APIBaseResponse<AddressTable | null>> {
        try {
            const result = await AddressTable.create(address, {
                userId: address.createdById,
                transaction
            });
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'phone') {
                    return HelperMethods.getErrorResponse('Contact number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(id: number, address: ICreateAddress, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            await AddressTable.update(address, {
                where: {
                    id: id
                },
                userId: address.createdById,
                individualHooks: true,
                transaction
            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'phone') {
                    return HelperMethods.getErrorResponse('Contact number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<AddressTable> | null>> {
        try {
            const offset = (page - 1) * pageSize;
            const { count, rows } = await AddressTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: {
                    status: ADDRESS_STATUS.ACTIVE
                },
                transaction
            });

            const totalPages = Math.ceil(count / pageSize);

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number, transaction: Transaction): Promise<APIBaseResponse<AddressTable | null>> {
        try {
            const result = await AddressTable.findByPk(id,{transaction});
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }


}