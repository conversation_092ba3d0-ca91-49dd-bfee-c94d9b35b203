import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { AddressTable } from "../database/AddressTable";
import { ICreateAddress, } from "../models/IAddress";

export interface IAddressRepo {
    create(Address: ICreateAddress, transaction: Transaction): Promise<APIBaseResponse<AddressTable | null>>;

    update(id: number, Address: ICreateAddress, transaction: Transaction): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<AddressTable> | null>>;

    getById(id: number, transaction: Transaction): Promise<APIBaseResponse<AddressTable | null>>;

    delete(ids: number[], deletedById: number, transaction: Transaction): Promise<APIBaseResponse<null>>;
}