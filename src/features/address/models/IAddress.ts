import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { ADDRESS_STATUS } from "./AddressMisc";

interface ICreateAddress {
    street: string;
    postalCode: string;
    city: string;
    state: string;
    country: string;
    status: ADDRESS_STATUS;
    createdById: number;
}


interface IUpdateAddress extends Omit<ICreateAddress, "createdById"> {
    id: number;
    updatedById: number;
}


interface IAddress extends ICreateAddress, InterfaceMetaData {

}

export { IAddress, ICreateAddress, IUpdateAddress };