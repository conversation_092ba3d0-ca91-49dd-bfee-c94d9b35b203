import { z } from "zod";

export class AddressSchema {
    static createSchema =
        z.object({
            street: z.string().min(3, "Street must be at least 3 characters long").max(100, "Street must be up to 100 characters long"),
            city: z.string().min(3, "City must be at least 3 characters long").max(50, "City must be up to 50 characters long"),
            state: z.string().min(3, "State must be at least 3 characters long").max(50, "State must be up to 50 characters long"),
            country: z.string().min(3, "Country must be at least 3 characters long").max(50, "Country must be up to 50 characters long"),
            postalCode: z.string().min(3, "Postal code must be at least 3 characters long").max(50, "Postal code must be up to 50 characters long"),
        });

    static updateSchema =
        AddressSchema.createSchema.extend({
            id: z.number({
                errorMap: () => ({
                    message: "Address id is required"
                })
            }).int().positive("Address id must be a positive number"),
        });

}