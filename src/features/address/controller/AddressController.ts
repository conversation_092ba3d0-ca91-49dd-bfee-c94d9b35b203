import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { ADDRESS_STATUS } from "../models/AddressMisc";
import { IAddress } from "../models/IAddress";
import { sequelizeInit } from "../../../sequelize_init";
import { HelperMethods } from "../../../core/HelperMethods";

export class AddressController {

    static async create(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const user_id = get(req, "user_id",);

            const payload = pick(req.body, ["street", "city", "state", "country", "postalCode",]) as IAddress;
            payload.status = ADDRESS_STATUS.ACTIVE;
            payload.createdById = Number(user_id!);

            const result = await RepoProvider.addressRepo.create(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async update(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const user_id = get(req, "user_id",);
            const id = Number(get(req.params, "id"));
            const payload = {
                ...req.body,
                updatedById: Number(user_id),
            };

            const result = await RepoProvider.addressRepo.update(id, payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async delete(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const ids: any = pick(req.body, "ids");

            const result = await RepoProvider.addressRepo.delete(ids, 1, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not deleted"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const result = await RepoProvider.addressRepo.getAll(1, 100, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.addressRepo.getById(Number(id), T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }
}