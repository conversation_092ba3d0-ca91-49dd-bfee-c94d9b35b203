import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { PurchaseOrderTable } from "../database/PurchaseOrderTable";
import { IPurchaseOrderPayload, IPurchaseOrderResponse, IUpdatePurchaseOrderPayload } from "../models/PurchaseOrder";

export interface IPurchaseOrderRepo {
    create(payload: IPurchaseOrderPayload, transaction: Transaction): Promise<APIBaseResponse<PurchaseOrderTable | null>>;

    update(payload: IUpdatePurchaseOrderPayload, transaction: Transaction): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number, transaction: Transaction, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<IPurchaseOrderResponse> | null>>;

    getById(id: Number, transaction: Transaction): Promise<APIBaseResponse<IPurchaseOrderResponse | null>>;

    delete(ids: Number[], deletedById: Number, transaction: Transaction): Promise<APIBaseResponse<null>>;

    searchByPoNumber(text: string, transaction: Transaction): Promise<APIBaseResponse<IPurchaseOrderResponse[] | null>>
}