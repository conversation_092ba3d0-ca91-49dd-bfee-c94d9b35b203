import { NextFunction, Request, Response } from 'express'
import { RepoProvider } from '../../../core/RepoProvider'
import { get } from 'lodash'
import {
  IPurchaseOrderPayload,
  IUpdatePurchaseOrderPayload,
} from '../models/PurchaseOrder'
import { HelperMethods } from '../../../core/HelperMethods'
import { sequelizeInit } from '../../../sequelize_init'

export class PurchaseOrderController {

  static async create(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const user_id = get(req, "user_id");

      const payload: IPurchaseOrderPayload = {
        ...req.body,
        createdById: Number(user_id!),
      };

      const result = await RepoProvider.purchaseOrderRepo.create(payload, T)
      if (!result.success) {
        res.status(500).send(result)
        return
      }
      await T.commit()
      res.status(200).send(result)
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }

  }

  static async update(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const id = Number(get(req.params, "id"));
      const user_id = get(req, "user_id",);

      const payload: IUpdatePurchaseOrderPayload = {
        ...req.body,
        id,
        updatedById: Number(user_id!),
      };

      const result = await RepoProvider.purchaseOrderRepo.update(payload, T)
      if (!result.success) {
        res.status(500).send(result)
        return
      }
      await T.commit()
      res.status(200).send(result)
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }

  }



  static async getAll(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const page = Number(get(req.query, 'page'))
      const pageSize = Number(get(req.query, 'pageSize'))
      const text = get(req.query, 'text') as string
      const result = await RepoProvider.purchaseOrderRepo.getAll(
        page,
        pageSize,
        T,
        text
      )
      if (!result.success) {
        res.status(500).send(result)
        return
      }
      await T.commit()
      res.status(200).send(result)
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }

  static async getById(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const id = get(req.params, "id");
      const result = await RepoProvider.purchaseOrderRepo.getById(Number(id), T);
      if (!result.success) {
        throw new Error(result.message)
      }
      await T.commit()
      res.status(200).send(result);
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }

  static async delete(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const ids: number[] = get(req.body, "ids");
      const user_id = get(req, "user_id");

      const convertedIds = ids.map((id) => Number(id));
      const result = await RepoProvider.purchaseOrderRepo.delete(convertedIds, Number(user_id!), T);
      if (!result.success) {
        throw new Error(result.message)
      }
      await T.commit()
      res.status(200).send(result);
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not deleted"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }

  static async searchByPoNumber(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const text = get(req.query, "text") as string;
      const result = await RepoProvider.purchaseOrderRepo.searchByPoNumber(text, T);
      if (!result.success) {
        throw new Error(result.message)
      }
      await T.commit()
      res.status(200).send(result);
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }

}
