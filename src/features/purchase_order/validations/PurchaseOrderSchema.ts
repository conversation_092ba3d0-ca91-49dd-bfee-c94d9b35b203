import { z } from "zod";

export class PurchaseOrderSchema {

    static purchaseOrderItemsSchema = z.object({
        rawMaterialId: z.number().positive("Item ID must be a positive number"),
        qty: z.number().positive("Quantity must be a positive number"),
    });

    static create = z.object({
        poNumber: z.string().min(1, "PO Number is required"),
        supplierId: z.number().positive("Supplier ID must be a positive number"),
        expectedDate: z.coerce.date().refine((date) => date instanceof Date, {
            message: "Expected date is required and must be a valid date",

        }),
        fromDepartmentId: z.number().positive("From Department ID must be a positive number"),
        toDepartmentId: z.number().positive("To Department ID must be a positive number"),
        supplierContactPerson: z.string().optional(),
        items: z.array(PurchaseOrderSchema.purchaseOrderItemsSchema).nonempty("Order items cannot be empty"),
    });

    
    static update = PurchaseOrderSchema.create.extend({
        receivedDate: z.coerce.date().optional(),
    }).partial();
}
