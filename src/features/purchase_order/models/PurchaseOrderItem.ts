import { InterfaceMetaData } from "../../../core/CoreInterfaces";


interface IPurchaseOrderItem extends InterfaceMetaData {
    purchaseOrderId:number;
    rawMaterialId:number;
    qty:number;
}

type CreatePurchaseOrderItem = Pick<IPurchaseOrderItem, "purchaseOrderId" | "rawMaterialId" |"qty" | "createdById">;

type UpdatePurchaseOrderItem = Pick<IPurchaseOrderItem, "purchaseOrderId" | "rawMaterialId" |"qty" | "updatedById">;

export { IPurchaseOrderItem, CreatePurchaseOrderItem, UpdatePurchaseOrderItem };