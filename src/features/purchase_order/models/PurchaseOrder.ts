import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { ISingleRawMaterialDetails } from "../../raw_material/models/IRawMaterial";
import { IRawMaterialVariationResponse } from "../../raw_material/models/IRawMaterialAndVariations";
import { ISupplier } from "../../supplier/models/ISupplier";
import { PurchaseOrderTable } from "../database/PurchaseOrderTable";


interface IPurchaseOrder extends InterfaceMetaData {
    poNumber: string;
    supplierId: number;
    expectedDate: Date;
    receivedDate: Date | null;
    fromDepartmentId: bigint;
    toDepartmentId: bigint;
    supplierContactPerson?: string;
}

interface IPurchaseOrderItems {
    rawMaterialId: number;
    qty: number;
}


interface IPurchaseOrderPayload extends IPurchaseOrder {
    items: IPurchaseOrderItems[];
}

interface IUpdatePurchaseOrderPayload extends IPurchaseOrderPayload {
    id: number;
}

interface IPurchaseOrderResponse {
    id: number,
    poNumber: string,
    supplier: ISupplier,
    expectedDate: Date,
    items: IPurchaseOrderResponseItems[];
    createdByName: string;
    createdAt: Date;
    fromDepartment: {
        id: number;
        name: string;
    };
    toDepartment: {
        id: number;
        name: string;
    };
    supplierContactPerson?: string;
}

interface IPurchaseOrderResponseItems {
    item: IRawMaterialVariationResponse,
    qty: number;
}


const purchaseOrderParser = (data: PurchaseOrderTable): IPurchaseOrderResponse => {
    return {
        id: Number(data.dataValues.id),
        poNumber: data.dataValues.poNumber,
        createdByName: data.createdBy.dataValues.firstName + ' ' + data.createdBy.dataValues.lastName,
        createdAt: data.dataValues.createdAt,
        supplier: {
            id: Number(data.dataValues.supplierId),
            name: data.supplier.dataValues.name,
            email: data.supplier.dataValues.email,
            phone: data.supplier.dataValues.phone,
            gst: data.supplier.dataValues.gst,
            pan: data.supplier.dataValues.pan,
            addressId: data.supplier.dataValues.addressId,
            status: data.supplier.dataValues.status,
            createdAt: data.supplier.dataValues.createdAt,
            updatedAt: data.supplier.dataValues.updatedAt,
            createdById: data.supplier.dataValues.createdById,
            updatedById: data.supplier.dataValues.updatedById,
            deletedAt: data.supplier.dataValues.deletedAt,
            deletedById: data.supplier.dataValues.deletedById
        },
        expectedDate: data.dataValues.expectedDate,
        fromDepartment: {
            id: Number(data.fromDepartment.dataValues.id),
            name: data.fromDepartment.dataValues.name,
        },
        toDepartment: {
            id: Number(data.toDepartment.dataValues.id),
            name: data.toDepartment.dataValues.name,
        },
        supplierContactPerson: data.dataValues.supplierContactPerson,
        items: data.purchaseOrderItems.map((item) => {
            return {
                item: {
                    id: Number(item.rawMaterialVariation.dataValues.id),
                    name: item.rawMaterialVariation.dataValues.name,
                    unit: item.rawMaterialVariation.rawMaterial.unit.dataValues,
                    category: item.rawMaterialVariation.rawMaterial.category.dataValues,
                    sku: item.rawMaterialVariation.dataValues.sku,
                    msq: item.rawMaterialVariation.dataValues.msq,
                    hsn: item.rawMaterialVariation.rawMaterial.dataValues.hsn,
                    gstPercentage: item.rawMaterialVariation.rawMaterial.dataValues.gstPercentage,
                    priceData: item.rawMaterialVariation.prices?.map(data => ({
                        supplier: {
                            ...data.supplier.dataValues,
                            id: Number(data.supplier.dataValues.id),
                        },
                        price: Number(data.dataValues.price),
                    })) || [],
                },
                qty: Number(item.dataValues.qty),
            }

        })
    }
}

type CreatePurchaseOrder = Pick<IPurchaseOrder, "poNumber" | "supplierId" | "expectedDate" | "receivedDate" | "fromDepartmentId" | "toDepartmentId" | "supplierContactPerson" | "createdById">;

type UpdatePurchaseOrder = Pick<IPurchaseOrder, "poNumber" | "supplierId" | "expectedDate" | "receivedDate" | "fromDepartmentId" | "toDepartmentId" | "supplierContactPerson" | "updatedById">;

export {
    IPurchaseOrder, CreatePurchaseOrder, UpdatePurchaseOrder, IPurchaseOrderPayload, IUpdatePurchaseOrderPayload, IPurchaseOrderResponse, purchaseOrderParser,
    IPurchaseOrderResponseItems,
};