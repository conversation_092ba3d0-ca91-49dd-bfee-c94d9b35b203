import * as express from 'express'
import { PurchaseOrderValidations } from '../validations/PurchaseOrderValidations'
import { PurchaseOrderController } from '../controller/PurchaseOrderController'

const apiInitialPath = '/purchase-orders'
const purchaseOrderRouter = express.Router()

purchaseOrderRouter.post(
  apiInitialPath + '/create',
  PurchaseOrderValidations.validateCreate,
  PurchaseOrderController.create
)

purchaseOrderRouter.put(
  apiInitialPath + '/update/:id',
  PurchaseOrderValidations.validateUpdate,
  PurchaseOrderValidations.validateId,
  PurchaseOrderController.update
)

purchaseOrderRouter.get(
  apiInitialPath + '/',
  PurchaseOrderValidations.validateGetAll,
  PurchaseOrderController.getAll
)

purchaseOrderRouter.get(
  apiInitialPath + '/search',
  PurchaseOrderValidations.validateSearch,
  PurchaseOrderController.searchByPoNumber
)

purchaseOrderRouter.get(
  apiInitialPath + '/:id',
  PurchaseOrderValidations.validateId,
  PurchaseOrderController.getById
)

purchaseOrderRouter.delete(
  apiInitialPath + "/delete" ,
   PurchaseOrderValidations.validateIdArray,
    PurchaseOrderController.delete
  );

export { purchaseOrderRouter }
