import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from '../../../core/RepoProvider';
import { CreatePurchaseOrderItem, IPurchaseOrderItem } from '../models/PurchaseOrderItem';
import { RawMaterialVariationTable } from '../../raw_material/database/RawMaterialVariationTable';


class PurchaseOrderItemTable extends Model<IPurchaseOrderItem, CreatePurchaseOrderItem> {
    declare rawMaterialVariation:RawMaterialVariationTable;
 }

PurchaseOrderItemTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value.toString());
                }
            }
        },
        purchaseOrderId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.purchaseOrderId;
                if (value) {
                    return Number(value);
                }
            },
        },
        rawMaterialId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return Number(value);
                }
            },
        },
        qty:{
            type: DataTypes.DECIMAL(10,2),
            allowNull: false,
        },
        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'purchase_order_items',
        timestamps: true,
        paranoid: true,
    },
);




PurchaseOrderItemTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "purchase_order_items",
        instance,
        options
    );
});

PurchaseOrderItemTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "purchase_order_items",
        instance,
        options
    );
});

PurchaseOrderItemTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "purchase_order_items",
        instance,
        options
    );
});

export { PurchaseOrderItemTable };