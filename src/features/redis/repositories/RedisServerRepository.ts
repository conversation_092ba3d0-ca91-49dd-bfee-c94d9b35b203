import { createClient } from "redis";
import { IRedisServerRepository } from "./IRedisServerRepository";



enum REDIS_KEYS {
    LAST_ACTIVITY_TIME = "last_activity_time:",
}



class RedisServerRepository implements IRedisServerRepository {
    private readonly client;

    constructor() {
        this.client = createClient();
        this.client.on("connect", () => console.log("✅ Connected to Redis"));
        this.client.on("error", (err) => console.error("❌ Redis Error:", err));
    }

    /* Connect to Redis Server */
    public async connect(): Promise<void> {

        if (this.client.isReady) {
            return;
        }
        try {
            await this.client.connect();
        } catch (error) {
            throw new Error("Failed to connect to Redis");
        }
    }


    /** Get value by key */
    async get<T>(key: string): Promise<T | null> {
        const data = await this.client.get(key);
        return data ? (JSON.parse(data) as T) : null;
    }

    /** Set value with optional expiration (in seconds) */
    async set<T>(key: string, value: T, ttl?: number): Promise<void> {
        const data = JSON.stringify(value);
        if (ttl) {
            await this.client.set(key, data, {
                EX: ttl,
            });
        } else {
            await this.client.set(key, data);
        }
    }

    /** Delete a key */
    async delete(key: string): Promise<void> {
        await this.client.del(key);
    }

    /** Check if a key exists */
    async exists(key: string): Promise<boolean> {
        return (await this.client.exists(key)) === 1;
    }

    /** Increment a value */
    async increment(key: string): Promise<number> {
        return await this.client.incr(key);
    }

    /** Decrement a value */
    async decrement(key: string): Promise<number> {
        return await this.client.decr(key);
    }

    /** Disconnect Redis Server */
    public async disconnect(): Promise<void> {
        await this.client.quit();
        console.log("🔴 Disconnected from Redis");
    }
}


export { RedisServerRepository, REDIS_KEYS };
