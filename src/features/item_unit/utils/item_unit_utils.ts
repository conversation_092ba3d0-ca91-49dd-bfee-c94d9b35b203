import { parseUserToMetaUser } from "../../users/core/parser/core_user_parser"
import { ParsedItemUnit } from "../models/IItemUnit"

export class ItemUnitUtils {
    static parseItemUnit = (unit: any): ParsedItemUnit => {
        const data: ParsedItemUnit = {
            id: unit.id,
            name: unit.name,
            status: unit.status,
            auditData: {
                createdBy: parseUserToMetaUser(unit.creator),
                updatedBy: unit?.updater ? parseUserToMetaUser(unit.updater) : unit.updater,
                deletedBy: unit?.deleter ? parseUserToMetaUser(unit.deleter) : unit.deleter,
            },
            createdAt: unit.created_at,
            updatedAt: unit.updated_at,
            deletedAt: unit.deleted_at,
        }
        return data
    }
}