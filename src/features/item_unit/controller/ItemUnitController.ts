import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { ITEM_UNIT_STAUS } from "../models/ItemUnitMisc";
import { ICreateItemUnit } from "../models/IItemUnit";
import { sequelizeInit } from "../../../sequelize_init";
import { HelperMethods } from "../../../core/HelperMethods";

export class ItemUnitController {

    static async create(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id",);

            const payload = pick(req.body, ["name", "status",]) as ICreateItemUnit;
            payload.status = ITEM_UNIT_STAUS.ACTIVE;
            payload.createdById = Number(userId!);
            payload.name = payload.name.toLowerCase().trim();
            const result = await RepoProvider.itemUnitRepo.create(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async update(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = Number(get(req.params, "id"));

            const userId = get(req, "user_id",);


            const payload = {
                ...req.body,
                updatedById: Number(userId),
            };

            const result = await RepoProvider.itemUnitRepo.update(id, payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async delete(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const ids: any = pick(req.body, "ids");

            const userId = get(req, "user_id",);

            const result = await RepoProvider.itemUnitRepo.delete(ids, Number(userId), T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not deleted"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));

            const result = await RepoProvider.itemUnitRepo.getAll(page, pageSize, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.itemUnitRepo.getById(Number(id), T);
            if (!result.success) {
                throw new Error('Unit data does nor exists.')
            }
            T.commit();
            res.status(200).send(result);
        } catch (error) {
            T.rollback();
            res.status(200).send(error instanceof Error ? error.message : 'Something went wrong : unit data not exists.');
        }
    }

    static async searchByText(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const text = get(req.query, "text") as string;
            const result = await RepoProvider.itemUnitRepo.searchByText(text, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
}