import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { ItemUnitTable } from "../database/ItemUnitTable";
import { ICreateItemUnit } from "../models/IItemUnit";
import { ITEM_UNIT_STAUS } from "../models/ItemUnitMisc";
import { IItemUnitRepo } from "./IItemUnitRepo";
import { Op, Transaction, UniqueConstraintError } from "sequelize";

export class PostgresItemUnitRepo implements IItemUnitRepo {
    delete(ids: number[], deletedById: number, transaction: Transaction): Promise<APIBaseResponse<null>> {
        throw new Error("Method not implemented.");
    }

    async create(payload: ICreateItemUnit, transaction: Transaction): Promise<APIBaseResponse<ItemUnitTable | null>> {
        try {
            const result = await ItemUnitTable.create(payload, {
                userId: payload.createdById,
                transaction
            });
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Unit already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(id: number, payload: ICreateItemUnit, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            const deletionUpdates = {
                deletedAt: new Date(),
                deletedById: payload.updatedById,
            };
            if (payload.status === ITEM_UNIT_STAUS.DELETED) {
                Object.assign(payload, deletionUpdates);
            }
            await ItemUnitTable.update(payload, {
                where: {
                    id: id
                },
                userId: payload.updatedById!,
                individualHooks: true,
                transaction
            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Unit already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<ItemUnitTable> | null>> {
        try {
            const offset = (page - 1) * pageSize;
            const { count, rows } = await ItemUnitTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: {
                    status: ITEM_UNIT_STAUS.ACTIVE,
                    deletedAt: null,
                }, transaction
            });

            const totalPages = Math.ceil(count / pageSize);

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchByText(text: string, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<ItemUnitTable> | null>> {
        try {
            const { count, rows } = await ItemUnitTable.findAndCountAll({
                limit: 10,
                order: [['createdAt', 'DESC']],
                transaction,
                where: {

                    name: {
                        [Op.iLike]: `%${text}%`
                    },
                    status: ITEM_UNIT_STAUS.ACTIVE
                },
            });

            const totalPages = 1;

            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number, transaction: Transaction): Promise<APIBaseResponse<ItemUnitTable | null>> {
        try {
            const result = await ItemUnitTable.findByPk(id, { transaction });
            if (!result) {
                throw new Error(`Unit data not exists of this id (${id})`)
            }
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse(error instanceof Error ? error.message : 'Unit data not exists');
        }
    }


}