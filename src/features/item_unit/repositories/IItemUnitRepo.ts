import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { ItemUnitTable } from "../database/ItemUnitTable";
import { ICreateItemUnit, } from "../models/IItemUnit";

export interface IItemUnitRepo {
    create(payload: ICreateItemUnit,transaction:Transaction): Promise<APIBaseResponse<ItemUnitTable | null>>;

    update(id: Number, payload: ICreateItemUnit,transaction:Transaction): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<ItemUnitTable> | null>>;

    searchByText(text: string,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<ItemUnitTable> | null>>;

    getById(id: number, transaction: Transaction): Promise<APIBaseResponse<ItemUnitTable | null>>;

    delete(ids: number[], deletedById: number,transaction:Transaction): Promise<APIBaseResponse<null>>;
}