import { AuditData, InterfaceMetaData } from "../../../core/CoreInterfaces";
import { ITEM_UNIT_STAUS } from "./ItemUnitMisc";

interface IItemUnit extends InterfaceMetaData {
    id: number;
    name: string;
    status: ITEM_UNIT_STAUS;
}

interface ICreateItemUnit extends InterfaceMetaData {
    name: string;
    status: ITEM_UNIT_STAUS;
}
interface ParsedItemUnit extends Pick<IItemUnit,'createdAt'|'deletedAt'|'updatedAt'>, AuditData {
    id: number;
    name: string;
    status: ITEM_UNIT_STAUS;
}


export { IItemUnit, ICreateItemUnit,ParsedItemUnit };