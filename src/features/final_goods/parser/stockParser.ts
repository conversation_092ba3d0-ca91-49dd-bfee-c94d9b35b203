
import { parseUserToMetaUser } from "../../users/core/parser/core_user_parser";
// import { ParsedStock } from "../interface/Stock";

function parsedStock(stock:any) {
    const data ={
        id:stock.id,
        stock:stock.stock,
        finalGoodsId:stock.finalGoods,
        finalGoodsVariationId:stock.finalGoodsVariation,
        createdBy: parseUserToMetaUser(stock.createdByUser),
        updatedBy: stock.updatedByUser ? parseUserToMetaUser(stock.updatedByUser) : null,
        deletedBy: stock.deletedByUser ? parseUserToMetaUser(stock.deletedByUser) : null,
        createdAt: stock.createdAt,
        updatedAt: stock.updatedAt,
        deletedAt: stock.deletedAt,
    };
    return data;
}

export {parsedStock};