import { ItemCategoryUtils } from "../../item_category/utils/item_category_utils";
import { ItemUnitUtils } from "../../item_unit/utils/item_unit_utils";
import { parseUserToMetaUser } from "../../users/core/parser/core_user_parser";
import { ParsedFinalGoods } from "../interface/IFinalGood";

const finalGoodsParser = (finalGoods: any): ParsedFinalGoods => {
    const data: ParsedFinalGoods = {
        id: finalGoods.id,
        name: finalGoods.name,
        category: finalGoods.finalGoodsCategories.map((category) => ItemCategoryUtils.parseItemCategory(category)),
        description: finalGoods.description,
        status: finalGoods.status,
        hsn:finalGoods.hsn,
        images: finalGoods.images,
        unit: finalGoods.unit ? ItemUnitUtils.parseItemUnit(finalGoods.unit) : null,
        auditData: {
            createdBy: parseUserToMetaUser(finalGoods.createdByUser),
            updatedBy: finalGoods.updatedByUser ? parseUserToMetaUser(finalGoods.updatedByUser) : null,
            deletedBy: finalGoods.deletedByUser ? parseUserToMetaUser(finalGoods.deletedByUser) : null,
        },
        createdById: finalGoods.createdById,
        deletedById: finalGoods.deletedById,
        updatedById: finalGoods.updatedById,
        createdAt: finalGoods.createdAt,
        updatedAt: finalGoods.updatedAt,
        deletedAt: finalGoods.deletedAt,

    };
    return data;
}

export { finalGoodsParser };
