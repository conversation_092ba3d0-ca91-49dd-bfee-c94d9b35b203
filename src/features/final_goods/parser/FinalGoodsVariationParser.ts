import { parseTaxRate } from "../../tax-rate/parser/taxRateParser";
import { parseUserToMetaUser } from "../../users/core/parser/core_user_parser";
import { ParsedFinalGoodsVariations } from "../interface/IFinalGoodsVariation";
import { finalGoodsParser } from "./finalGoodsParser";
// import { FinalGoodsTypesForParse, ParsedFinalGoodsVariation, } from "../interface/IFinalGoodsVariation";
import { parsedStock } from "./stockParser";

// function parsedFinalGoodsVariations(variations: any): ParsedFinalGoodsVariations[] {
//     if (!variations || !Array.isArray(variations)) {
//         return [];
//     }
//     return variations.map(variation => ({
//         id: variation.id,
//         finalGoodsId: variation.finalGoodsId,
//         stock: variation.stock ? parsedStock(variation.stock) : null,
//         price: !isNaN(Number(variation.price)) ? Number(variation.price) : 0,
//         attributes: variation.attributes,
//         sku: variation.sku,
//         createdBy: parseUserToMetaUser(variation.createdByUser),
//         updatedBy: variation.updatedByUser ? parseUserToMetaUser(variation.updatedByUser) : null,
//         deletedBy: variation.deletedByUser ? parseUserToMetaUser(variation.deletedByUser) : null,
//         createdAt: variation.createdAt,
//         updatedAt: variation.updatedAt,
//         deletedAt: variation.deletedAt,
//     }));
// }

function parsedFinalGoodsVariationSingle(variation: any): ParsedFinalGoodsVariations {
    console.log('varition',variation)
    return {
        id: variation.id,
        finalGoodsId: variation.finalGoodsId,
        stock: variation.stock ? parsedStock(variation.stock) : null,
        price: !isNaN(Number(variation.price)) ? Number(variation.price) : 0,
        attributes: variation.attributes,
        attributeValue:variation.variationAttributes,
        sku: variation.sku,
        tax: variation.tax ? parseTaxRate(variation.tax) : null,
        finalGoods: variation.finalGoods ? finalGoodsParser(variation.finalGoods) : null,
        createdBy: parseUserToMetaUser(variation.createdByUser),
        updatedBy: variation.updatedByUser ? parseUserToMetaUser(variation.updatedByUser) : null,
        deletedBy: variation.deletedByUser ? parseUserToMetaUser(variation.deletedByUser) : null,
        createdAt: variation.createdAt,
        updatedAt: variation.updatedAt,
        deletedAt: variation.deletedAt,
    };
}

export { parsedFinalGoodsVariationSingle };
