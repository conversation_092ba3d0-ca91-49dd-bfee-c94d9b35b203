import { get } from "lodash";
import { Request, Response } from "express";
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from "../../../core/RepoProvider";
import { HelperMethods } from "../../../core/HelperMethods";
import { isString } from "../../../core/common-utils";
import { FinalGoodsFilter } from "../interface/IFinalGood";
import { ERROR_MESSAGE } from "../../../core/constants";
import { FinalGoodsVariationFilter } from "../interface/IFinalGoodsVariation";

export default class FinalGoodsController {
    static async createFinalGoods(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {

            const payload: any = req?.body;

            const user_id = get(req, "user_id");
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }

            const finalGoodsDetails = await RepoProvider.finalGoodsRepo.create(
                payload,
                user_id,
                transaction
            );

            if (!finalGoodsDetails.success) {
                throw new Error(finalGoodsDetails.message)
            }

            await transaction.commit();
            res.status(200).send(finalGoodsDetails);
        } catch (error) {
            await transaction.rollback();
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while fetching final goods."),
                )
            );
        }
    }

    static async getAllFinalGoods(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {

            const size = !isNaN(Number(get(req.query, "pageSize"))) ? Number(get(req.query, "pageSize")) : 10;
            const page = !isNaN(Number(get(req.query, "page"))) ? Number(get(req.query, "page")) : 1
            let search = get(req.query, "search");
            // Can be string | string[] | ParsedQs | ParsedQs[]
            let [sortField, sortOrder] = get(req.query, "sorting", "id DESC").toString().split(" ");


            const { text }: FinalGoodsFilter = req.query

            const validFrom = get(req.query, "startDate");
            if (validFrom && !isString(validFrom)) {
                throw new Error("startDate should be string")
            }
            const validTo = get(req.query, "endDate");
            if (validTo && !isString(validTo)) {
                throw new Error("endDate should be string")
            }


            const textValue = (text ? { text } : {})
            const filters = Object.assign({},textValue)
            console.log("fg filter",filters);
            

            // Parse filters into an object
            const finalGoods = await RepoProvider.finalGoodsRepo.getAll(page, size, filters, transaction
            );
            if (!finalGoods.success) {
                throw new Error(finalGoods.message);
            }

            await transaction.commit();
            res.status(200).send(finalGoods);
        } catch (err) {
            await transaction.rollback();
            HelperMethods.handleError(err);
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (err instanceof Error ? err.message : "An error occurred while fetching final goods."),
                )
            );
        }
    }
    static async getAllFinalGoodsVariations(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {

            const size = !isNaN(Number(get(req.query, "pageSize"))) ? Number(get(req.query, "pageSize")) : 10;
            const page = !isNaN(Number(get(req.query, "page"))) ? Number(get(req.query, "page")) : 1

            const filters: FinalGoodsVariationFilter = {}
            const name = get(req.query, "name")
            const sku = get(req.query, "sku")
            if (name) {
                filters.name = get(req.query, "name")!.toString()
            }
            if (sku) {
                filters.sku = get(req.query, "sku")!.toString()
            }

            // Parse filters into an object
            const finalGoods = await RepoProvider.finalGoodsRepo.getAllVariation(page, size, filters, transaction
            );
            if (!finalGoods.success) {
                throw new Error(finalGoods.message);
            }

            await transaction.commit();
            res.status(200).send(finalGoods);
        } catch (err) {
            await transaction.rollback();
            HelperMethods.handleError(err);
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (err instanceof Error ? err.message : "An error occurred while fetching final goods."),
                )
            );
        }
    }

    static async updateVariations(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const id = parseInt(get(req.params, "id"));
            if (!id || isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER)
            }

            const payload = req.body;
            payload.id = id;

            const user_id = get(req, "user_id");
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }

            payload.updatedBy = user_id;
            const finalGoods = await RepoProvider.finalGoodsRepo.updateVariation(id, payload, user_id, transaction);

            if (!finalGoods.success) throw new Error(finalGoods.message)

            await transaction.commit();
            res.status(200).send(finalGoods);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while updating final goods."),
                )
            );
        }
    }

    static async addNewVariationOfFinalGoods(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const id = parseInt(get(req.params, "id"));
            if (!id || isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER)
            }

            const payload = req.body;
            payload.id = id;

            const user_id = get(req, "user_id");
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }

            payload.updatedBy = user_id;
            const finalGoods = await RepoProvider.finalGoodsRepo.addNewVariationOfFinalGoods(id, payload.variations, user_id, transaction);

            if (!finalGoods.success) throw new Error(finalGoods.message)

            await transaction.commit();
            res.status(200).send(finalGoods);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while updating final goods."),
                )
            );
        }
    }

    static async getFinalGoodsById(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const id = parseInt(get(req.params, "id"));
            if (!id || isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER)
            }
            const finalGoods = await RepoProvider.finalGoodsRepo.getById(id, transaction);

            if (!finalGoods.success) {
                throw new Error(finalGoods.message)
            }
            await transaction.commit();
            res.status(200).send(finalGoods);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while fetching final goods."),
                )
            );
        }
    }

    static async updateFinalGoods(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const id = parseInt(get(req.params, "id"));
            if (!id || isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER)
            }

            const payload = req.body;
            payload.id = id;

            const user_id = get(req, "user_id");
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }

            payload.updatedBy = user_id;

            const finalGoods = await RepoProvider.finalGoodsRepo.update(id, payload, user_id, transaction);

            if (!finalGoods.success) throw new Error(finalGoods.message)

            await transaction.commit();
            res.status(200).send(finalGoods);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while updating final goods."),
                )
            );
        }
    }

    static async deleteFinalGoods(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {

            const id = Number(get(req.params, "id"));
            const user_id = parseInt(get(req, "user_id")!)

            if (isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER)
            }
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }
            const deletedCount = await RepoProvider.finalGoodsRepo.delete(id, user_id, transaction);
            if (!deletedCount.success) {
                throw new Error(deletedCount.message);
            }

            await transaction.commit();
            res.status(200).send(deletedCount);
        } catch (error) {
            await transaction.rollback();
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while deleting final goods."),
                )
            );
        }

    }


    static async getVariationById(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const id = parseInt(get(req.params, "id"));
            if (!id || isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER)
            }
            const variationResponse = await RepoProvider.finalGoodsRepo.getVariationById(id, transaction);

            if (!variationResponse.success) {
                throw new Error(variationResponse.message)
            }
            await transaction.commit();
            res.status(200).send(variationResponse);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while fetching final goods."),
                )
            );
        }
    }


    static async deleteVariation(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {

            const ids = get(req.body, "ids");
            const user_id = parseInt(get(req, "user_id")!)

            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }

            const deletedResponse = await RepoProvider.finalGoodsRepo.deleteVariation(ids, user_id, transaction);
            if (!deletedResponse.success) {
                throw new Error(deletedResponse.message);
            }

            await transaction.commit();
            res.status(200).send(deletedResponse);
        } catch (error) {
            await transaction.rollback();
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while deleting final goods."),
                )
            );
        }

    }


}

