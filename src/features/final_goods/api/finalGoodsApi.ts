import * as express from 'express';
import FinalGoodsController from '../controllers/FinalGoodsController';
import finalGoodsValidations from '../validations/finalGoodsValidations';
const finalGoodsRouter = express.Router();

const { createFinalGoods, deleteFinalGoods, getAllFinalGoods, getFinalGoodsById, updateFinalGoods, addNewVariationOfFinalGoods, deleteVariation, updateVariations, getAllFinalGoodsVariations, getVariationById } = FinalGoodsController;
const apiInitialPath = "/final-goods";

finalGoodsRouter.post(`${apiInitialPath}/create`, finalGoodsValidations.validateCreate, createFinalGoods);
finalGoodsRouter.delete(`${apiInitialPath}/variation`, finalGoodsValidations.validateDeleteVariation, deleteVariation);

finalGoodsRouter.delete(`${apiInitialPath}/delete/:id`, finalGoodsValidations.validateDelete, deleteFinalGoods);
finalGoodsRouter.get(`${apiInitialPath}/variations/`, finalGoodsValidations.validateList, getAllFinalGoodsVariations);
finalGoodsRouter.put(`${apiInitialPath}/update/:id`, finalGoodsValidations.validateUpdate, updateFinalGoods);
finalGoodsRouter.put(`${apiInitialPath}/add-new-variation/:id`, finalGoodsValidations.addNewVariation, addNewVariationOfFinalGoods);
finalGoodsRouter.put(`${apiInitialPath}/update-variation/:id`, updateVariations);
finalGoodsRouter.get(`${apiInitialPath}/`, finalGoodsValidations.validateList, getAllFinalGoods);
finalGoodsRouter.get(`${apiInitialPath}/variation/:id`, finalGoodsValidations.validateGetById, getVariationById);
finalGoodsRouter.get(`${apiInitialPath}/:id`, finalGoodsValidations.validateGetById, getFinalGoodsById);
export default finalGoodsRouter;
