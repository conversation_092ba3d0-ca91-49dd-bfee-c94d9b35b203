import { Optional } from "sequelize";
import { InterfaceMetaData } from "../../../core/CoreInterfaces";

interface FinalGoodsVariationAndAttrbutesRelationAttributes extends InterfaceMetaData {
    id: number;
    final_goods_variation_id: number;
    attribute_value_id: number;
}

interface ICreateFinalGoodsVariationAndAttrbutesRelationAttributes extends Optional<FinalGoodsVariationAndAttrbutesRelationAttributes, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'> {}

interface FinalGoodsVariationAndAttrbutesRelationPayload {
    finalGoodsVariationId: number,
    attributeValueId: number
}

export {
    FinalGoodsVariationAndAttrbutesRelationAttributes,
    ICreateFinalGoodsVariationAndAttrbutesRelationAttributes,
    FinalGoodsVariationAndAttrbutesRelationPayload
}