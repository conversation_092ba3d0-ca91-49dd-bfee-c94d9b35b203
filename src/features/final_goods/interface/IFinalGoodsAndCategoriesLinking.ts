import { InterfaceMetaData } from "../../../core/CoreInterfaces";

interface IAttributes {
    finalGoodsId: number,
    categoryId: number,
}
interface IFinalGoodsAndCategoriesLinkingCreationAttributes extends IAttributes {

}

interface IFinalGoodsAndCategoriesLinkingAttributes extends IAttributes, Pick<InterfaceMetaData, 'createdAt' | 'id' | 'deletedAt' | "updatedAt"> {

}

export {
    IFinalGoodsAndCategoriesLinkingCreationAttributes,
    IFinalGoodsAndCategoriesLinkingAttributes
}