import { FinalGoodsVariationAttrbutes } from "./IFinalGoodsVariation";
import { ParsedItemCategory } from "../../item_category/models/IItemCategory";
import { AuditData, InterfaceMetaData, IQueryFilter } from "../../../core/CoreInterfaces";
import { IFinalGoodsCreationZodPayload, IFinalGoodsUpdateZodPayload, IFinalGoodsZodPayload } from "../validations/finalGoodsValidationsSchema";
import { IItemUnit, ParsedItemUnit } from "../../item_unit/models/IItemUnit";
import { FinalGoodsAndCategoriesLinkingModel } from "../model/finalGoodsAndCategoriesLinking";
import { ItemCategoryTable } from "../../item_category/database/ItemCategoryTable";
import { StockAttributes } from "./Stock";
import { TaxRateTable } from "../../tax-rate/interface/taxRate";
import { FinalGoodsVariationAndAttrbutesRelationAttributes } from "./FinalGoodsVariationAndAttributesRelation";
import { IItemAttributeValue } from "../../item_attribute_value/models/IItemAttributeValue";

enum FINAL_GOODS_STATUS {
    ACTIVE = "active",
    INACTIVE = "inactive",
}

interface IFinalGoodsCreationPayload extends IFinalGoodsCreationZodPayload {
    createdById: number;
}

interface IFinalGoodsCreationAttributes extends Omit<IFinalGoodsZodPayload, 'categoryIds'> {
    createdById: number;
}


interface IFinalGoodsAttributes extends IFinalGoodsCreationAttributes, InterfaceMetaData {
    status: FINAL_GOODS_STATUS;
}

interface FinalGoodsFilter extends IQueryFilter {
   
}


interface ParsedFinalGoods extends Omit<IFinalGoodsAttributes, "categoryIds" | "unitId" | "taxRateId">, AuditData {
    category: ParsedItemCategory;
    unit: ParsedItemUnit | null
    // taxRate: number | null;
    // variations: ParsedFinalGoodsVariations[]
}

interface IFinalGoodsUpdatePayload extends IFinalGoodsUpdateZodPayload {
    updatedById: number;
}

interface IFinalGoods extends IFinalGoodsAttributes {
    finalGoodsCategories: FinalGoodsAndCategoriesLinkingModel & { category: ItemCategoryTable }[],
    unit: IItemUnit | null,
}

interface IFinalGoodsGetSingle extends IFinalGoods {
    variations: {
        stock: StockAttributes,
        tax: TaxRateTable,
        finalGoods: IFinalGoods,
        variationAttributes: FinalGoodsVariationAndAttrbutesRelationAttributes & { attributeValue: IItemAttributeValue }
    } & FinalGoodsVariationAttrbutes
}


export { FINAL_GOODS_STATUS as FinalGoodsStatus, IFinalGoodsAttributes, FinalGoodsFilter, ParsedFinalGoods, IFinalGoodsCreationAttributes, IFinalGoodsCreationPayload, IFinalGoodsUpdatePayload, IFinalGoodsGetSingle }