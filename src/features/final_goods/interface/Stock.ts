import { Optional } from "sequelize";
import { InterfaceMetaData, BaseMeta, ParsedMeta } from "../../../core/CoreInterfaces";


interface StockAttributes extends InterfaceMetaData {
    id: number;
    finalGoodsId: number;
    final_goods_variation_id: number | null;
    stock: number;
}

interface ICreateStockAttributes extends Optional<StockAttributes, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'> {}

interface StockPayload {
    stock:number
}

interface ParsedStock extends ParsedMeta {
    id: number;
    finalGoodsId: number;
    finalGoodsVariationId: number;
    stock: number;
}

export {
    StockAttributes,
    ICreateStockAttributes,
    StockPayload,
    ParsedStock
}
