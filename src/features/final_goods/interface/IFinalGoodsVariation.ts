import { Optional } from "sequelize";
import { InterfaceMetaData, <PERSON><PERSON>uery<PERSON><PERSON>er, ParsedMeta } from "../../../core/CoreInterfaces";
import { ParsedStock } from "./Stock";
import { IFinalGoodsVariationZodPayload } from "../validations/finalGoodsValidationsSchema";
import { ParsedTaxRate } from "../../tax-rate/interface/taxRate";
import { ParsedFinalGoods } from "./IFinalGood";
import { IParsedItemAttributeValue } from "../../item_attribute_value/models/IItemAttributeValue";


interface FinalGoodsVariationAttrbutes extends Omit<IFinalGoodsVariationZodPayload, 'attributes'>, InterfaceMetaData {
    id: number;
}

interface ICreateFinalGoodsVariationAttributes extends Optional<FinalGoodsVariationAttrbutes, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'> { }

interface FinalGoodsVariationFilter extends IQueryFilter {
    name?: string,
    sku?: string
}

interface FinalGoodsVariationPayload extends IFinalGoodsVariationZodPayload { }

interface UpdateFinalGoodVariation extends FinalGoodsVariationPayload {
    id: number;
}

interface ParsedFinalGoodsVariations extends ParsedMeta {
    id: number;
    finalGoodsId: number;
    sku: string;
    price: number;
    stock: ParsedStock | null;
    attributes: number;
    tax: ParsedTaxRate | null;
    finalGoods: ParsedFinalGoods | null,
    attributeValue: IParsedItemAttributeValue
}

export { FinalGoodsVariationAttrbutes, FinalGoodsVariationPayload, ICreateFinalGoodsVariationAttributes, UpdateFinalGoodVariation, ParsedFinalGoodsVariations, FinalGoodsVariationFilter }