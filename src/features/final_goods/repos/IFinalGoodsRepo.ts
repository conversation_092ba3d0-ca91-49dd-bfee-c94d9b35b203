import { Transaction } from "sequelize";
import { FinalGoodsFilter, IFinalGoodsCreationPayload, IFinalGoodsUpdatePayload } from "../interface/IFinalGood";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { FinalGoodsVariationPayload, UpdateFinalGoodVariation } from "../interface/IFinalGoodsVariation";
import { FinalGoodsTable } from "../model/finalGoodsTable";
import { FinalGoodsVariationTable } from "../model/finalGoodsVariationTable";

export interface IFinalGoodsRepo {
    create(product: IFinalGoodsCreationPayload, createdBy: number, transaction: Transaction): Promise<APIBaseResponse<FinalGoodsTable | null>>;

    update(id: number, product: IFinalGoodsUpdatePayload, updatedBy: number, transaction: Transaction): Promise<APIBaseResponse<FinalGoodsTable | null>>;

    updateVariation(id: number, payload: UpdateFinalGoodVariation, updatedBy: number, transaction: Transaction): Promise<APIBaseResponse<FinalGoodsVariationTable | null>>;

    addNewVariationOfFinalGoods(finalGoodsId: number, payload: FinalGoodsVariationPayload[], createdBy: number, transaction: Transaction): Promise<APIBaseResponse<FinalGoodsVariationTable[] | null>>;

    delete(id: number, user_id: number, transaction: Transaction): Promise<APIBaseResponse<FinalGoodsTable | null>>;

    getById(id: number, transaction: Transaction): Promise<APIBaseResponse<FinalGoodsTable | null>>;

    getAll(page: number, limit: number, filters: FinalGoodsFilter, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<FinalGoodsTable> | null>>

    getAllVariation(page: number, limit: number, filters: FinalGoodsFilter, transaction: Transaction
    ): Promise<APIBaseResponse<PaginatedBaseResponse<FinalGoodsVariationTable> | null>>

    getVariationById(id: number,transaction: Transaction): Promise<APIBaseResponse<FinalGoodsVariationTable | null>>
    
    deleteVariation(ids: number[], user_id: number, transaction: Transaction): Promise<APIBaseResponse< null>>;
}
