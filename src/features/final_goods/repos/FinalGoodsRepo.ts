import { IFinalGoodsRepo } from "./IFinalGoodsRepo";
import { Op, Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { PaginationProvider } from "../../pagination/PaginationProvider";
import { HelperMethods } from "../../../core/HelperMethods";
import { FinalGoodsFilter, IFinalGoodsCreationPayload, IFinalGoodsUpdatePayload } from "../interface/IFinalGood";
import { FinalGoodsVariationTable } from "../model/finalGoodsVariationTable";
import { StockTable } from "../model/stockTable";
import { FinalGoodsVariationAndAttributesRelationTable } from "../model/FinalGoodsVariationAndAttributesRelationTable";
import { FinalGoodsVariationFilter, FinalGoodsVariationPayload, UpdateFinalGoodVariation } from "../interface/IFinalGoodsVariation";
import { finalGoodsInclude, finalGoodsVariationsInclude } from "../includes/finalGoodsInclude";
import { FinalGoodsTable } from "../model/finalGoodsTable";
import { FinalGoodsAndCategoriesLinkingModel } from "../model/finalGoodsAndCategoriesLinking";
import { RepoProvider } from "../../../core/RepoProvider";

export class PostgresFinalGoodsRepo implements IFinalGoodsRepo {

    private _categoryRepo = RepoProvider.itemCategoryRepo
    private _attributeValueRepo = RepoProvider.itemAttributeValueRepo
    private _taxRateRepo = RepoProvider.taxRateRepo
    private _itemUnitRepo = RepoProvider.itemUnitRepo

    /**
     * Helper method to transform variation data to match the structure used in getById()
     * This ensures consistency between getById() and addNewVariationOfFinalGoods() responses
     */
    private transformVariationData(variationData: any) {
        variationData.expire_days = Number(variationData.expire_days);
        const clone = structuredClone(variationData);
        delete clone.variationAttributes;

        const attributes = variationData.variationAttributes?.map((attributeLinkingData: any) => {
            const attributeData = attributeLinkingData.attributeValue;
            const attributeValue = structuredClone(attributeData);
            delete attributeValue.attribute;
            const attribute = attributeData.attribute;
            return {
                attribute,
                attributeValue
            };
        }) || [];

        clone.attributes = attributes;
        return clone;
    }

    async create(
        payload: IFinalGoodsCreationPayload,
        createdBy: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<FinalGoodsTable | null>> {
        try {
            const isFinalGoodsExists = await FinalGoodsTable.findOne({
                where: {
                    name: payload.finalGoods.name,
                },
                transaction,
            })

            if (isFinalGoodsExists) {
                throw new Error(`Final goods with name ${payload.finalGoods.name} already exists.`);
            }
            const unitId = payload.finalGoods.unitId
            const itemUnitResponse = await this._itemUnitRepo.getById(unitId, transaction)

            if (!itemUnitResponse.success) {
                throw new Error(`Unit data not exists of this id (${unitId})`)
            }

            const createdFinalGoods = await FinalGoodsTable.create({
                name: payload.finalGoods.name,
                images: payload.finalGoods.images,
                unitId: unitId,
                description: payload.finalGoods.description,
                hsn: payload.finalGoods.hsn,
                // status: payload.status,
                createdById: createdBy,
            }, {
                transaction, userId: (createdBy)
            })

            if (!createdFinalGoods) {
                throw new Error("Failed to create final goods.");
            }
            const finalGoods = createdFinalGoods.toJSON();

            if (payload.finalGoods.categoryIds && Array.isArray(payload.variations)) {

                for (const categoryId of payload.finalGoods.categoryIds) {

                    const categoryResponse = await this._categoryRepo.getById(categoryId, transaction)
                    if (!categoryResponse.success) {
                        throw new Error(`Category data not exists of this id (${categoryId}).`)
                    }

                    const createdReponse = await FinalGoodsAndCategoriesLinkingModel.create({
                        categoryId: categoryId,
                        finalGoodsId: finalGoods.id
                    }, { transaction: transaction, userId: createdBy })

                    if (!createdReponse) {
                        throw new Error('something went wrong : category not created')
                    }
                }

            }

            if (payload.variations && Array.isArray(payload.variations)) {

                for (const variation of payload.variations) {
                    const taxRateResponse = await this._taxRateRepo.getById(variation.taxRateId, transaction)

                    if (!taxRateResponse.success) {
                        throw new Error(`Taxrate is not exists of this is id(${variation.taxRateId})`)
                    }

                    const createdVariation = await FinalGoodsVariationTable.create({
                        name: variation.name,
                        finalGoodsId: finalGoods.id,
                        discount: variation.discount,
                        taxRateId: variation.taxRateId,
                        sku: variation.sku,
                        price: variation.price,
                        expire_days:variation.expire_days,
                        images: variation.images,
                        moq: variation.moq,
                        msq: variation.msq,
                        createdById: createdBy,
                    }, { transaction, userId: createdBy });

                    if (!createdVariation) {
                        throw new Error("Failed to create final goods variation.");
                    }
                    const variationData = createdVariation.toJSON();
                    const createdStock = await StockTable.create({
                        final_goods_variation_id: variationData.id,
                        stock: 0,
                        finalGoodsId: finalGoods.id,
                        createdById: createdBy,
                    }, { transaction, userId: createdBy });
                    if (!createdStock) {
                        throw new Error("Failed to create stock for final goods variation.");
                    }

                    if (variation.attributes && Array.isArray(variation.attributes)) {
                        for (const attribute of variation.attributes) {
                            const attributeResponse = await this._attributeValueRepo.getById(attribute.attributeValueId, transaction)
                            if (!attributeResponse.success) {
                                throw new Error(`Attribute value data not exists of this id (${attribute.attributeValueId}).`)
                            }
                            await FinalGoodsVariationAndAttributesRelationTable.create({
                                final_goods_variation_id: variationData.id,
                                attribute_value_id: attribute.attributeValueId,
                                createdById: createdBy,
                            }, { transaction, userId: (createdBy) });
                        }

                    }
                }
            }
            return HelperMethods.getSuccessResponse(createdFinalGoods);
        } catch (error) {
            HelperMethods.handleError(error)

            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async update(
        id: number,
        payload: IFinalGoodsUpdatePayload,
        updatedBy: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<FinalGoodsTable | null>> {
        try {
            const isFinalGoodsExists = await FinalGoodsTable.findByPk(id, { transaction });
            if (!isFinalGoodsExists) {
                throw new Error(`Final goods not found with this id(${id}).`);
            }
            const unitId = payload.unitId
            const itemUnitResponse = await this._itemUnitRepo.getById(unitId, transaction)

            if (!itemUnitResponse.success) {
                throw new Error(`Unit data not exists of this id (${unitId})`)
            }

            await isFinalGoodsExists.update({
                name: payload.name,
                images: payload.images,
                unitId: payload.unitId,
                description: payload.description,
                hsn: payload.hsn,
                status: payload.status,
                updatedById: updatedBy,
            }, {
                userId: updatedBy,
                transaction,
            });
            const isExists = await FinalGoodsAndCategoriesLinkingModel.findOne({
                where: {
                    finalGoodsId: id,
                },
                transaction,
            })
            if (isExists) {
                await FinalGoodsAndCategoriesLinkingModel.destroy({
                    where: {
                        finalGoodsId: id,
                    },
                    transaction,
                    userId: updatedBy
                })
            }
            if (payload.categoryIds.length > 0 && Array.isArray(payload.categoryIds)) {
                for (const categoryId of payload.categoryIds) {
                    const categoryResponse = await this._categoryRepo.getById(categoryId, transaction)
                    if (!categoryResponse.success) {
                        throw new Error(`Category data not exists of this id (${categoryId}).`)
                    }
                    await FinalGoodsAndCategoriesLinkingModel.create({
                        finalGoodsId: id,
                        categoryId: categoryId
                    }, { transaction: transaction, userId: updatedBy })

                }

            }
            return HelperMethods.getSuccessResponse(isFinalGoodsExists);
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async updateVariation(
        id: number,
        payload: UpdateFinalGoodVariation,
        updatedBy: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<FinalGoodsVariationTable | null>> {
        try {
            const isVariationExists = await FinalGoodsVariationTable.findOne({
                where: {
                    id,
                },
                transaction,
            });
            if (!isVariationExists) {
                throw new Error(`Final goods variation not found with this id(${id}).`);
            }

            await isVariationExists.update({
                price: payload.price,
                sku: payload.sku,
                taxRateId: payload.taxRateId,
                name: payload.name,
                discount: payload.discount,
                moq: payload.moq,
                msq: payload.msq,
                images: payload.images,
                expire_days:payload.expire_days,
                finalGoodsId: payload.finalGoodsId,
                updatedById: updatedBy,
            }, { transaction, userId: updatedBy });

            const variationData = isVariationExists.toJSON()

            if (payload.attributes && Array.isArray(payload.attributes)) {
                for (const attribute of payload.attributes) {
                    const relation = await FinalGoodsVariationAndAttributesRelationTable.findOne({
                        where: {
                            final_goods_variation_id: variationData.id,
                            attribute_value_id: attribute.attributeValueId,
                        },
                        transaction,
                    });

                    if (!relation) {
                        const attributeResponse = await this._attributeValueRepo.getById(attribute.attributeValueId, transaction)
                        if (!attributeResponse.success) {
                            throw new Error(`Attribute value data not exists of this id (${attribute.attributeValueId}).`)
                        }
                        await FinalGoodsVariationAndAttributesRelationTable.create({
                            final_goods_variation_id: variationData.id,
                            attribute_value_id: attribute.attributeValueId,
                            createdById: updatedBy,
                        }, { transaction, userId: (updatedBy) });
                    }
                }
            }


            // Fetch the updated variation with all nested data using the same structure as getById()
            const updatedVariation = await this.getVariationById(id, transaction);

            if (!updatedVariation.success || !updatedVariation.data) {
                throw new Error("Failed to fetch updated variation data");
            }

            // Transform the variation data to match the structure used in getById()
            const updatedVariationData = updatedVariation.data.toJSON() as any;
            const transformedVariation = this.transformVariationData(updatedVariationData);

            return HelperMethods.getSuccessResponse(transformedVariation);
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async addNewVariationOfFinalGoods(finalGoodsId: number, payload: FinalGoodsVariationPayload[], createdBy: number, transaction: Transaction): Promise<APIBaseResponse<FinalGoodsVariationTable[] | null>> {
        try {
            const isFinalGoodsExists = await FinalGoodsTable.findByPk(finalGoodsId, { transaction });
            if (!isFinalGoodsExists) {
                throw new Error(`Final goods not found with this id(${finalGoodsId}).`);
            }

            if (!Array.isArray(payload) || payload.length === 0) {
                throw new Error("No variations provided to add.");
            }

            const variations: FinalGoodsVariationTable[] = []

            for (const payloadVariation of payload) {

                const taxRateResponse = await this._taxRateRepo.getById(payloadVariation.taxRateId, transaction)

                if (!taxRateResponse.success) {
                    throw new Error(`Taxrate is not exists of this is id(${payloadVariation.taxRateId})`)
                }

                const createdVariation = await FinalGoodsVariationTable.create({
                    name: payloadVariation.name,
                    finalGoodsId: finalGoodsId,
                    discount: payloadVariation.discount,
                    taxRateId: payloadVariation.taxRateId,
                    sku: payloadVariation.sku,
                    price: payloadVariation.price,
                    expire_days:payloadVariation.expire_days,
                    images: payloadVariation.images,
                    moq: payloadVariation.moq,
                    msq: payloadVariation.msq,
                    createdById: createdBy,
                }, { transaction, userId: (createdBy) });

                if (!createdVariation) {
                    throw new Error("Failed to create final goods variation.");
                }

                // Create stock record for the new variation
                const variationData = createdVariation.toJSON();
                const createdStock = await StockTable.create({
                    final_goods_variation_id: variationData.id,
                    stock: 0,
                    finalGoodsId: finalGoodsId,
                    createdById: createdBy,
                }, { transaction, userId: createdBy });

                if (!createdStock) {
                    throw new Error("Failed to create stock for final goods variation.");
                }

                if (payloadVariation.attributes && Array.isArray(payloadVariation.attributes)) {
                    for (const attribute of payloadVariation.attributes) {
                        const attributeResponse = await this._attributeValueRepo.getById(attribute.attributeValueId, transaction)
                        if (!attributeResponse.success) {
                            throw new Error(`Attribute value data not exists of this id (${attribute.attributeValueId}).`)
                        }
                        await FinalGoodsVariationAndAttributesRelationTable.create({
                            final_goods_variation_id: createdVariation.toJSON().id,
                            attribute_value_id: attribute.attributeValueId,
                            createdById: createdBy,
                        }, { transaction, userId: (createdBy) });
                    }
                }
                const variation = await this.getVariationById(createdVariation.toJSON().id, transaction)
                if (variation.success && variation.data) {
                    // Transform the variation data to match the structure used in getById()
                    const variationData = variation.data.toJSON() as any;
                    const transformedVariation = this.transformVariationData(variationData);
                    variations.push(transformedVariation);
                }
            }

            return HelperMethods.getSuccessResponse(variations)
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async getAll(page: number, limit: number, filters: FinalGoodsFilter, transaction: Transaction
    ): Promise<APIBaseResponse<PaginatedBaseResponse<FinalGoodsTable> | null>> {
        try {
            const where: any = {}
            if (filters.text) {
                where[Op.or] = [
                    { name: { [Op.iLike]: `%${filters.text}%` } },
                    { description: { [Op.iLike]: `%${filters.text}%` } },
                ];
            }
           

            const paginatedData = await new PaginationProvider<any, FinalGoodsTable>().getPaginatedRecords(FinalGoodsTable, { include: finalGoodsInclude(), limit: limit, page: page, where: where, dateColumn: "createdAt" }, transaction)
            // if (paginatedData.rows.length === 0) {
            //     return HelperMethods.getErrorResponse('data not exists')
            // }
            return HelperMethods.getSuccessResponse({
                totalData: paginatedData.total,
                data: paginatedData.rows,
                totalPages: paginatedData.totalPages,
                currentPage: paginatedData.currentPage, // Calculate current page
            })
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }


    async getAllVariation(page: number, limit: number, filters: FinalGoodsVariationFilter, transaction: Transaction
    ): Promise<APIBaseResponse<PaginatedBaseResponse<FinalGoodsVariationTable> | null>> {
        try {
            const where: any = {}
            if (filters.name || filters.sku) {
                where[Op.or] = [
                    { name: { [Op.iLike]: `%${filters.name}%` } },
                    { sku: { [Op.iLike]: `%${filters.sku}%` } },
                ];
            }
            const paginatedData = await new PaginationProvider<any, FinalGoodsVariationTable>().getPaginatedRecords(FinalGoodsVariationTable, { include: finalGoodsVariationsInclude(), limit: limit, page: page, where: where, dateColumn: "createdAt" }, transaction)
            return HelperMethods.getSuccessResponse({
                totalData: paginatedData.total,
                data: paginatedData.rows,
                totalPages: paginatedData.totalPages,
                currentPage: paginatedData.currentPage, // Calculate current page
            })
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async getById(
        id: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<FinalGoodsTable | null>> {
        try {
            // Fetch finalGoods by primary key (ID)
            const finalGoods = await FinalGoodsTable.findByPk(id, {
                transaction,
                include: [
                    ...finalGoodsInclude(),
                    {
                        model: FinalGoodsVariationTable,
                        as: 'variations',
                        include: finalGoodsVariationsInclude()
                    }
                ]
            })

            if (!finalGoods) throw new Error(`Final goods not of this id(${id})`);


            const finalGoodsData = finalGoods.toJSON() as any;

            const variations = finalGoodsData.variations

            const updatedVariations = variations.map((variation: any) => {
                return this.transformVariationData(variation);
            })
            finalGoodsData.variations = updatedVariations


            return HelperMethods.getSuccessResponse(finalGoodsData);
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }
    async getVariationById(
        id: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<FinalGoodsVariationTable | null>> {
        try {
            // Fetch finalGoods by primary key (ID)
            const variationResponse = await FinalGoodsVariationTable.findByPk(id, {
                transaction,
                include: finalGoodsVariationsInclude()
            })

            if (!variationResponse) throw new Error(`Final goods not of this id(${id})`)

            return HelperMethods.getSuccessResponse(variationResponse);
        } catch (error) {
            HelperMethods.handleError(error)
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

    async delete(
        id: number,
        user_id: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<FinalGoodsTable | null>> {

        try {
            const isExists = await FinalGoodsTable.findByPk(id)
            if (!isExists) throw new Error('final goods not exists')

            const response = await FinalGoodsTable.destroy({
                where: {
                    id: id,
                },
                userId: (user_id),
                transaction
            });

            if (response === 0) {
                return HelperMethods.getErrorResponse("deletion failed")
            }
            return HelperMethods.getSuccessResponse(isExists, 'Data deleted successfully')
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }


    async deleteVariation(
        ids: number[],
        user_id: number,
        transaction: Transaction,
    ): Promise<APIBaseResponse<null>> {

        try {
            console.log(ids)
            for (const id of ids) {
                const isExists = await FinalGoodsVariationTable.findByPk(id)
                if (!isExists) {
                    throw new Error(`Data not exists of this id ${id}`)
                }
            }

            const response = await FinalGoodsVariationTable.destroy({
                where: {
                    id: ids,
                },
                userId: user_id,
                transaction
            });

            if (response === 0) {
                return HelperMethods.getErrorResponse("deletion failed")
            }
            return HelperMethods.getSuccessResponse(null, 'Data deleted successfully')
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse()
            }
        }
    }

}
