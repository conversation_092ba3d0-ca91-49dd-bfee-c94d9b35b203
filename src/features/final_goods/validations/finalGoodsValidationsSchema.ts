import { array, z } from "zod";
import { FinalGoodsStatus } from "../interface/IFinalGood";

const categoryIds = {
    categoryIds: z
        .array(
            z.number({
                required_error: "Category ID is required.",
                invalid_type_error: "Category ID must be a number.",
            })
                .int("Category ID must be an integer.")
        )
        .min(1, "At least one category is required."),
}
const finalGoods = {
    name: z
        .string()
        .min(1, "Name is required.")
        .transform((val) => val.toLowerCase()),

    unitId: z
        .number({
            required_error: "Unit ID is required.",
            invalid_type_error: "Unit ID must be a number.",
        })
        .int("Unit ID must be an integer."),
    description: z
        .string()
        .min(15, "Description must be at least 15 characters long.")
        .nullable(),
    images: z
        .array(
            z.string().url("Each image must be a valid URL.")
        ).nullable(),
    hsn: z.string(
        {
            required_error: "hsn is required.",
            invalid_type_error: "hsn must be a string.",
        }
    ).min(3, "Hsn must be at least 3 characters long."),
};
const variationObj = {
    taxRateId: z.number().int().positive("Tax Rate ID must be greater than 0"),
    name: z.string().min(1, "Name is required").transform((val) => val.toLowerCase()),
    discount: z.number().int().nonnegative("Discount must be 0 or greater"),
    moq: z.number({
        invalid_type_error: `moq should be a number.`,
    }).int(`moq cannot be empty.`).nullable(),
    sku: z.string().min(4, "sku must be 4 character."),
    msq: z.number({
        invalid_type_error: `msq should be a number.`,
    }).int(`msq cannot be empty.`).nullable(),
    price: z.number().positive("Price must be greater than 0"),
    images: z.array(z.string()),
    attributes: z.array(z.object({
        attributeValueId: z.number({
            required_error: `attributeValueId is required.`,
            invalid_type_error: `attributeValueId should be a number.`,
        }).int(`attributeValueId cannot be empty.`),
    })),
    expire_days: z
        .number()
        .nonnegative("Expire days must be 0 or greater"),

}
const variationsSchema = z.object(variationObj)

class finalGoodsValidationSchema {
    static create = z.object({
        finalGoods: z.object({
            ...finalGoods,
            ...categoryIds,
        }),
        variations: z.array(variationsSchema),
    });
    static finalGoodsList = z.object({
        page: z.string()
            .transform(val => Number(val))
            .refine(val => !isNaN(val) && val > 0, {
                message: "Page must be a positive number"
            }),
        pageSize: z.string()
            .transform(val => Number(val))
            .refine(val => !isNaN(val), {
                message: "Page must be a number"
            }),
        // .transform(Number).refine(val => !isNaN(val) && val > 0, {
        //     message: "Page size must be a positive number",
        // }),
        status: z.enum(["active", "inactive"]).optional(),
        search: z.string().optional(),
        sorting: z.string().optional(),
        validFrom: z.string().optional(),
        validTo: z.string().optional(),
    });


    static finalGoodsById = z.object({
        id: z.coerce.number().int("Id must be an integer"),
    });

    static updateFinalGoods = z.object({
        ...finalGoods,
    })

    static finalGoodsDeleteValidationSchema = z.object({
        params: z.object({
            id: z.string({
                required_error: `"id" is required.`,
                invalid_type_error: `"id" should be a string.`,
            }).nonempty(`"id" cannot be empty.`),
        }),
    })

    static StockPayloadSchema = z.object({
        stock: z.number()
    });

    // Schema for attributesPayload
    static AttributesPayloadSchema = z.object({
        attributeValueId: z.number()
    });

    // Schema for FinalGoodsVariationPayload
    static FinalGoodsVariationPayloadSchema = z.object({
        sku: z.string(),
        price: z.number(),
        stock: this.StockPayloadSchema,
        attributes: z.array(this.AttributesPayloadSchema)
    });


    static finalGoodsUpdateValidationSchema = z.object({
        ...finalGoods,
        ...categoryIds,
        status: z.nativeEnum(FinalGoodsStatus, {
            errorMap: () => ({
                message: 'Status must be either "active" or "inactive".',
            }),
        }),
    });
    static deleteVariation = z.object({
        ids: z.array(
            z.number().int().positive("id must be greater than 0"),
        ).min(1, 'At least one id is required.')
    })

    static addNewVariation = z.object({
        variations: z.array(z.object({
            ...variationObj,
            // finalGoodsId: z.number().int().positive("finalGoodsId must be greater than 0"),
        }))
    })
}


const FG = z.object({ ...finalGoods })
type IFinalGoodsZodPayload = z.infer<typeof FG>;
type IFinalGoodsUpdateZodPayload = z.infer<typeof finalGoodsValidationSchema.finalGoodsUpdateValidationSchema>;
type IFinalGoodsCreationZodPayload = z.infer<typeof finalGoodsValidationSchema.create>;
type IFinalGoodsVariationZodPayload = z.infer<typeof variationsSchema> & { finalGoodsId: number };
export {
    IFinalGoodsZodPayload,
    finalGoodsValidationSchema,
    IFinalGoodsCreationZodPayload,
    IFinalGoodsVariationZodPayload,
    IFinalGoodsUpdateZodPayload
}