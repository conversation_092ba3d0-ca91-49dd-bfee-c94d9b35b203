import { Includeable } from "sequelize";
import { FinalGoodsVariationAndAttributesRelationTable } from "../model/FinalGoodsVariationAndAttributesRelationTable";
import { StockTable } from "../model/stockTable";
import { FinalGoodsAndCategoriesLinkingModel } from "../model/finalGoodsAndCategoriesLinking";
import { ItemUnitTable } from "../../item_unit/database/ItemUnitTable";
import { TaxRateModel } from "../../tax-rate/models/TaxRateTable";
import { FinalGoodsTable } from "../model/finalGoodsTable";
import { ItemCategoryTable } from "../../item_category/database/ItemCategoryTable";
import { ItemAttributeValueTable } from "../../item_attribute_value/database/ItemAttributeValueTable";
import { ItemAttributeTable } from "../../item_attribute/database/ItemAttributeTable";

const finalGoodsInclude = () => {
  const include: Includeable[] = [
    {
      model: FinalGoodsAndCategoriesLinkingModel,
      as: "finalGoodsCategories",
      include: [
        {
          model: ItemCategoryTable,
          as: "category"
        }
      ]
    },
    {
      model: ItemUnitTable,
      as: "unit",
    },
    // { model: CoreUserTable, as: 'createdByUser' },
    // { model: CoreUserTable, as: 'updatedByUser' },
    // { model: CoreUserTable, as: 'deletedByUser' },
  ];
  return include
};

const finalGoodsVariationsInclude = () => {
  const include: Includeable[] = [
    {
      model: StockTable,
      as: "stock",
    },
    {
      model: TaxRateModel,
      as: "tax",
    },
    {
      model: FinalGoodsTable,
      as: "finalGoods",
      include: [
        {
          model: ItemUnitTable,
          as: "unit",
        },

      ]
    },
    {
      model: FinalGoodsVariationAndAttributesRelationTable,
      as: "variationAttributes",
      include: [
        {
          model: ItemAttributeValueTable,
          as: 'attributeValue',
          include:[
            {
              as:'attribute',
              model:ItemAttributeTable
            }
          ]
        }
      ]
    }


    // {
    //   model: FinalGoodsVariationAndAttributesRelationTable,
    //   as: "attributesRelation",
    //   include: [
    //     {
    //       model: ItemAttributeValueTable,
    //       as: "attributeValue",
    //     },
    //   ],
    // },
  ]
  return include
}

export { finalGoodsInclude, finalGoodsVariationsInclude }
