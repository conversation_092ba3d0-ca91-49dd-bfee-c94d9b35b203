import { DataTypes, Model } from "sequelize";
import { ICreateStockAttributes, StockAttributes } from "../interface/Stock";
import { RepoProvider } from "../../../core/RepoProvider";
import { sequelizeInit } from "../../../sequelize_init";

class StockTable extends Model<StockAttributes,ICreateStockAttributes> implements StockAttributes {
    public id!: number;
    public finalGoodsId!: number;
    public final_goods_variation_id!: number | null;
    public stock!: number;
    public createdAt!: Date;
    public updatedAt!: Date;
    public deletedAt!: Date | null;
    public createdById!: number ;
    public updatedById!: number | null;
    public deletedById!: number | null;
    
    public static initModel() {
        StockTable.init({
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            finalGoodsId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            final_goods_variation_id: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            stock: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            createdById: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            updatedById: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            deletedById: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: "created_at",
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: "updated_at",
            },
            deletedAt: {
                type: DataTypes.DATE,
                allowNull: true,
                defaultValue: null,
                field: "deleted_at",
            }
        },{
            sequelize: sequelizeInit,
            underscored:true,
            tableName: "final_goods_stock",
            timestamps: true,
        });
        return StockTable;
    }

    public static hooks() {
        StockTable.addHook("afterCreate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "create",
                "final goods stock",
                instance,
                options
            );
        });

        // After Update Hook - Log the updated fields of the Product
        StockTable.addHook("afterUpdate", async (instance, options) => {
            // Now call logModelAction as before
            await RepoProvider.logRepo.logModelAction(
                "update",
                "final goods stock",
                instance,
                options
            );
        });

        // After Destroy Hook - Log the deletion of the Product
        StockTable.addHook("afterDestroy", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "delete",
                "final goods stock",
                instance,
                options
            );
        });
    }
}

export {
    StockTable
}