import { DataTypes, Model } from "sequelize";
import { FinalGoodsStatus, IFinalGoodsAttributes, IFinalGoodsCreationAttributes, } from "../interface/IFinalGood";
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from "../../../core/RepoProvider";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { FinalGoodsAndCategoriesLinkingModel } from "./finalGoodsAndCategoriesLinking";
import { ItemUnitTable } from "../../item_unit/database/ItemUnitTable";
import { FinalGoodsVariationTable } from "./finalGoodsVariationTable";

class FinalGoodsTable extends Model<IFinalGoodsAttributes, IFinalGoodsCreationAttributes> {
    public id!: number;
    public name!: string;
    public price!: number;
    public images!: string[];
    public unitId!: number;
    public taxRateId!: number;
    public description!: string | null;
    public status!: FinalGoodsStatus;
    public createdAt!: Date;
    public updatedAt!: Date;
    public deletedAt!: Date | null;

    public static initModel() {
        FinalGoodsTable.init(
            {
                id: {
                    type: DataTypes.INTEGER,
                    autoIncrement: true,
                    primaryKey: true,
                },
                unitId: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                },
                name: {
                    type: DataTypes.STRING,
                    allowNull: false,
                },
                hsn: {
                    type: DataTypes.STRING,
                    allowNull: false,
                },
                description: {
                    type: DataTypes.TEXT,
                    allowNull: true,
                },
           
                images: {
                    type: DataTypes.JSONB,
                    allowNull: true
                },
                status: {
                    type: DataTypes.ENUM(FinalGoodsStatus.ACTIVE, FinalGoodsStatus.INACTIVE),
                    defaultValue: FinalGoodsStatus.ACTIVE,
                    allowNull: false,
                },
                createdById: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                },
                updatedById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                deletedById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                createdAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                },
                deletedAt: {
                    type: DataTypes.DATE,
                    allowNull: true,
                },

            },
            {
                sequelize: sequelizeInit, // Assuming sequelizeInit is your Sequelize instance
                modelName: 'FinalGoods',
                tableName: 'final_goods',
                underscored: true,
                timestamps: true, // This will add createdAt and updatedAt fields
            }
        );
        return FinalGoodsTable;
    }

    public static associate() {
        FinalGoodsTable.belongsTo(CoreUserTable, { foreignKey: 'createdById', as: 'createdByUser' });
        FinalGoodsTable.belongsTo(CoreUserTable, { foreignKey: 'updatedById', as: 'updatedByUser' });
        FinalGoodsTable.belongsTo(CoreUserTable, { foreignKey: 'deletedById', as: 'deletedByUser' });
        FinalGoodsTable.hasMany(FinalGoodsVariationTable, { foreignKey: 'finalGoodsId', as: 'variations' })
        FinalGoodsTable.hasMany(FinalGoodsAndCategoriesLinkingModel, { foreignKey: 'finalGoodsId', as: 'finalGoodsCategories' })
        FinalGoodsTable.belongsTo(ItemUnitTable, { foreignKey: 'unitId', as: 'unit' })
    }

    public static hooks() {
        FinalGoodsTable.addHook("afterCreate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "create",
                "FinalGoods",
                instance,
                options,
            );
        });

        // After Update Hook - Log the updated fields of the FinalGoods
        FinalGoodsTable.addHook("afterUpdate", async (instance, options) => {
            // Now call logModelAction as before
            await RepoProvider.logRepo.logModelAction(
                "update",
                "FinalGoods",
                instance,
                options,
            );
        });

        // After Destroy Hook - Log the deletion of the FinalGoods
        FinalGoodsTable.addHook("afterDestroy", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "delete",
                "FinalGoods",
                instance,
                options,
            );
        });
    }
}

export { FinalGoodsTable }