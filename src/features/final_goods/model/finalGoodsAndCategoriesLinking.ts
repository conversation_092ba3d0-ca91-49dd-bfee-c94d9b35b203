import { Model, DataTypes } from "sequelize";
import { RepoProvider } from "../../../core/RepoProvider";
import { sequelizeInit } from "../../../sequelize_init";
import { IFinalGoodsAndCategoriesLinkingAttributes, IFinalGoodsAndCategoriesLinkingCreationAttributes } from "../interface/IFinalGoodsAndCategoriesLinking";
import { FinalGoodsTable } from "./finalGoodsTable";
import { ItemCategoryTable } from "../../item_category/database/ItemCategoryTable";

class FinalGoodsAndCategoriesLinkingModel extends Model<IFinalGoodsAndCategoriesLinkingAttributes, IFinalGoodsAndCategoriesLinkingCreationAttributes> {
    public id!: number;

    public createdAt!: Date;
    public updatedAt!: Date;
    public deletedAt!: Date | null;

    public static initModel() {
        FinalGoodsAndCategoriesLinkingModel.init(
            {
                id: {
                    type: DataTypes.INTEGER,
                    autoIncrement: true,
                    primaryKey: true,
                },
                finalGoodsId: {
                    type: DataTypes.INTEGER,
                    allowNull: false
                },
                categoryId: {
                    type: DataTypes.INTEGER,
                    allowNull: false
                },
                createdAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                },
                deletedAt: {
                    type: DataTypes.DATE,
                    allowNull: true,
                },

            },
            {
                sequelize: sequelizeInit, // Assuming sequelizeInit is your Sequelize instance
                modelName: 'final_goods_and_categories_linking',
                underscored: true,
                timestamps: true, // This will add createdAt and updatedAt fields
                indexes: [
                    {
                        fields: ['final_goods_id', 'category_id'],
                        unique: true,
                        name: "final_goods_unique_category"
                    }
                ]
            }
        );
        return FinalGoodsAndCategoriesLinkingModel;
    }

    public static associate() {
        FinalGoodsAndCategoriesLinkingModel.belongsTo(FinalGoodsTable, {
            foreignKey: 'final_goods_id',
            as: 'finalGoods'
        })

        FinalGoodsAndCategoriesLinkingModel.belongsTo(ItemCategoryTable, {
            foreignKey: 'category_id',
            as: 'category'
        })
    }

    public static hooks() {
        FinalGoodsAndCategoriesLinkingModel.addHook("afterCreate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "create",
                "FinalGoodsAndCategoriesLinking",
                instance,
                options,
            );
        });

        // After Update Hook - Log the updated fields of the FinalGoods
        FinalGoodsAndCategoriesLinkingModel.addHook("afterUpdate", async (instance, options) => {
            // Now call logModelAction as before
            await RepoProvider.logRepo.logModelAction(
                "update",
                "FinalGoodsAndCategoriesLinking",
                instance,
                options,
            );
        });

        // After Destroy Hook - Log the deletion of the FinalGoods
        FinalGoodsAndCategoriesLinkingModel.addHook("afterDestroy", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "delete",
                "FinalGoodsAndCategoriesLinking",
                instance,
                options,
            );
        });
    }
}

export { FinalGoodsAndCategoriesLinkingModel }