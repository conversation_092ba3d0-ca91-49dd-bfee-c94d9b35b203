
import { DataTypes, Model } from "sequelize";
import { FinalGoodsVariationAttrbutes, ICreateFinalGoodsVariationAttributes } from "../interface/IFinalGoodsVariation";
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from "../../../core/RepoProvider";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { StockTable } from "./stockTable";
import { FinalGoodsTable } from "./finalGoodsTable";
import { TaxRateModel } from "../../tax-rate/models/TaxRateTable";

class FinalGoodsVariationTable extends Model<FinalGoodsVariationAttrbutes, ICreateFinalGoodsVariationAttributes> implements FinalGoodsVariationAttrbutes {
    public id!: number;
    public name!: string;
    public finalGoodsId!: number;
    public taxRateId!: number;
    public discount!: number;
    public price!: number;
    public images!: string[];
    public sku!: string;
    public expire_days!: number;
    public moq!: number | null;
    public msq!: number | null;
    public createdAt!: Date;
    public updatedAt!: Date;
    public deletedAt!: Date | null;
    public createdById!: number;
    public updatedById!: number | null;
    public deletedById!: number | null;

    public static initModel() {
        FinalGoodsVariationTable.init(
            {
                id: {
                    type: DataTypes.INTEGER,
                    autoIncrement: true,
                    primaryKey: true,
                },
                name: {
                    type: DataTypes.STRING,
                    allowNull: false,
                },
                discount: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                },
                taxRateId: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                },
                finalGoodsId: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                },
                expire_days: {
                    type: DataTypes.DECIMAL(10, 2), // e.g., 999.99 max
                    allowNull: false,
                },
                images: {
                    type: DataTypes.JSONB,
                    allowNull: false
                },
                sku: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                moq: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                msq: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                price: {
                    type: DataTypes.FLOAT,
                    allowNull: false,
                },
                createdById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                updatedById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                deletedById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                createdAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                    defaultValue: DataTypes.NOW,
                    field: 'created_at',
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                    defaultValue: DataTypes.NOW,
                    field: 'updated_at',
                },
                deletedAt: {
                    type: DataTypes.DATE,
                    allowNull: true,
                    defaultValue: null,
                    field: 'deleted_at',
                },
            },
            {
                sequelize: sequelizeInit, // Assuming sequelizeInit is your Sequelize instance
                modelName: 'FinalGoodsVariationTable',
                tableName: 'final_goods_variations',
                underscored: true,
                paranoid: true,
                timestamps: true, // This will add createdAt and updatedAt fields
            }
        );
        return FinalGoodsVariationTable
    }

    public static associate() {
        FinalGoodsVariationTable.belongsTo(CoreUserTable, { foreignKey: 'createdById', as: 'createdByUser' });
        FinalGoodsVariationTable.belongsTo(CoreUserTable, { foreignKey: 'updatedById', as: 'updatedByUser' });
        FinalGoodsVariationTable.belongsTo(CoreUserTable, { foreignKey: 'deletedById', as: 'deletedByUser' });
        FinalGoodsVariationTable.belongsTo(FinalGoodsTable, { foreignKey: 'final_goods_id', as: 'finalGoods' });
        FinalGoodsVariationTable.hasOne(StockTable, { foreignKey: 'final_goods_variation_id', as: 'stock' })
        FinalGoodsVariationTable.belongsTo(TaxRateModel, { foreignKey: 'tax_rate_id', as: 'tax' });
    }

    public static hooks() {
        FinalGoodsVariationTable.addHook("afterCreate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "create",
                "FinalGoodsVariations",
                instance,
                options
            );
        });

        // After Update Hook - Log the updated fields of the Product
        FinalGoodsVariationTable.addHook("afterUpdate", async (instance, options) => {
            // Now call logModelAction as before
            await RepoProvider.logRepo.logModelAction(
                "update",
                "FinalGoodsVariations",
                instance,
                options
            );
        });

        // After Destroy Hook - Log the deletion of the Product
        FinalGoodsVariationTable.addHook("afterDestroy", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "delete",
                "FinalGoodsVariations",
                instance,
                options
            );
        });
    }

}

export { FinalGoodsVariationTable }