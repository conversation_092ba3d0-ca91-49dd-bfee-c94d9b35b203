import { DataTypes, Model } from "sequelize";
import { FinalGoodsVariationAndAttrbutesRelationAttributes, ICreateFinalGoodsVariationAndAttrbutesRelationAttributes } from "../interface/FinalGoodsVariationAndAttributesRelation";
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from "../../../core/RepoProvider";
import { FinalGoodsVariationTable } from "./finalGoodsVariationTable";
import { ItemAttributeValueTable } from "../../item_attribute_value/database/ItemAttributeValueTable";

class FinalGoodsVariationAndAttributesRelationTable extends
    Model<FinalGoodsVariationAndAttrbutesRelationAttributes, ICreateFinalGoodsVariationAndAttrbutesRelationAttributes>
    implements FinalGoodsVariationAndAttrbutesRelationAttributes {
    public id!: number;
    public final_goods_variation_id!: number;
    public attribute_value_id!: number;
    public createdAt!: Date;
    public updatedAt!: Date;
    public deletedAt!: Date | null;
    public createdById!: number;
    public updatedById!: number | null;
    public deletedById!: number | null;

    public static initModel() {
        FinalGoodsVariationAndAttributesRelationTable.init(
            {
                id: {
                    type: DataTypes.INTEGER,
                    autoIncrement: true,
                    primaryKey: true,
                },
                final_goods_variation_id: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                },
                attribute_value_id: {
                    type: DataTypes.INTEGER,
                    allowNull: false,
                },
                createdById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                updatedById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                deletedById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                createdAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                    defaultValue: DataTypes.NOW,
                    field: 'created_at',
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                    defaultValue: DataTypes.NOW,
                    field: 'updated_at',
                },
                deletedAt: {
                    type: DataTypes.DATE,
                    allowNull: true,
                    field: 'deleted_at',
                }
            },
            {
                sequelize: sequelizeInit, // Assuming sequelizeInit is imported from your sequelize initialization file
                tableName: 'final_goods_variation_and_attributes_relation',
            }
        );
        return FinalGoodsVariationAndAttributesRelationTable;
    }
    public static associate() {
        FinalGoodsVariationTable.hasMany(FinalGoodsVariationAndAttributesRelationTable,{
            foreignKey: "final_goods_variation_id",
            as:'variationAttributes'
        })
        FinalGoodsVariationAndAttributesRelationTable.belongsTo(FinalGoodsVariationTable, {
            foreignKey: "final_goods_variation_id",
            as: 'variation'
        })

        FinalGoodsVariationAndAttributesRelationTable.belongsTo(ItemAttributeValueTable, {
            foreignKey: "attribute_value_id",
            as: "attributeValue",
        })
        
    }
    public static hooks() {
        FinalGoodsVariationAndAttributesRelationTable.addHook("afterCreate", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "create",
                "FinalGoodsVariationAndAttributesRelation",
                instance,
                options
            );
        });

        FinalGoodsVariationAndAttributesRelationTable.addHook("afterUpdate", async (instance, options) => {
            // Now call logModelAction as before
            await RepoProvider.logRepo.logModelAction(
                "update",
                "FinalGoodsVariationAndAttributesRelation",
                instance,
                options
            );
        });

        FinalGoodsVariationAndAttributesRelationTable.addHook("afterDestroy", async (instance, options) => {
            await RepoProvider.logRepo.logModelAction(
                "delete",
                "FinalGoodsVariationAndAttributesRelation",
                instance,
                options
            );
        });
    }
}

export { FinalGoodsVariationAndAttributesRelationTable }