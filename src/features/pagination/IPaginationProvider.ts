
import {Model, Transaction} from "sequelize";

export interface IPaginationProvider<T extends Model<any, any>,F> {
    getPaginatedRecords(
        model: any,
        options: {
            include?: any[];
            limit?: number;
            page?: number;
            where?: any;
            dateColumn?: keyof T["_attributes"];
            startDate?: Date; // Start date (optional)
            endDate?: Date; // End date (optional)
            order?: [string, "ASC" | "DESC"][];
        },
        transaction:Transaction,
        parseFunction?: (rows: T) => any // Optional transformation function
    ): Promise<{ total: number; rows: F[] , totalPages:number,currentPage:number }>;
}
