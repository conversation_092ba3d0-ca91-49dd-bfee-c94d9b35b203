import type { IPaginationProvider } from "./IPaginationProvider";
import { Model, Op, Transaction } from "sequelize";
import { sequelizeInit } from "../../sequelize_init";
import { HelperMethods } from "../../core/HelperMethods";

export class PaginationProvider<T extends Model<any, any>, F> implements IPaginationProvider<T, F> {
    async getPaginatedRecords(
        model: any,
        options: {
            include?: any[];
            limit?: number | null;
            page?: number;
            where?: any;
            dateColumn?: keyof T["_attributes"];
            startDate?: Date | string | undefined; // Start date (optional)
            endDate?: Date | string | undefined; // End date (optional)
            order?: [string, "ASC" | "DESC"][]; // Sorting order
        },
        transaction: Transaction,
        parseFunction?: (rows: T) => any // Optional transformation function
    ): Promise<{ total: number; rows: F[], totalPages: number, currentPage: number }> {
        const t = transaction ?? await sequelizeInit.transaction();
        try {
            let {
                where = {},
                include,
                limit,
                page,
                dateColumn,
                startDate,
                endDate,
                order
            } = options;

            const offset = (Number(page || 1) - 1) * Number(limit);


            // Apply date range filter if provided
            if (dateColumn && startDate && endDate) {
                limit = null;
                where[dateColumn as string] = {
                    [Op.between]: [startDate, endDate],
                };
            }
            let size = {}
            if (limit !== -1) {
                size = Object.assign({}, size, {limit: (limit ? limit : null), offset})
            }
            // Fetch records and count total matches
            const result = await model.findAndCountAll({
                where,
                include,
                ...size,
                order: order ?? [["createdAt", "DESC"]],
                transaction: t// Default order
            });
            // Apply the parse function if provided
            const transformedRows = parseFunction && result.rows.length > 0 ? result.rows.map((d: any) => parseFunction(d.toJSON())) : result.rows as F;

            const totalPages = Math.ceil(result.count / (limit ?? 1));

            const currentPage = Math.floor(offset / (limit ?? 1)) + 1;

            if (!transaction) {
                await t.commit();
            }

            return {
                total: result.count,
                rows: transformedRows,
                totalPages,
                currentPage
            };
        } catch (error: any) {
            HelperMethods.handleError(error)
            if (!transaction) {
                await t.rollback();
            }
            return {
                total: 0,
                rows: [],
                totalPages: 0,
                currentPage: 0
            };
        }
    }
}
