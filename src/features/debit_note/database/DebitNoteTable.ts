import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreateDebitNote, IDebitNote } from '../models/IDebitNote';
import { DEBIT_NOTE_SOURCE, DEBIT_NOTE_STATUS } from '../models/DebitNoteMisc';
import { PurchaseInvoiceTable } from '../../purchase_invoice/database/PurchaseInvoiceTable';
import { RawMaterialVariationTable } from '../../raw_material/database/RawMaterialVariationTable';
import { RepoProvider } from '../../../core/RepoProvider';


class DebitNoteTable extends Model<IDebitNote, ICreateDebitNote> {

    declare purchaseInvoice: PurchaseInvoiceTable;
    declare rawMaterial: RawMaterialVariationTable;
}

DebitNoteTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },

        purchaseInvoiceId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.purchaseInvoiceId;
                if (value) {
                    return Number(value);
                }
            },
        },

        qty: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },

        rawMaterialId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return Number(value);
                }
            },
        },

        actualPrice: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },

        purchasedPrice: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },

        note: {
            type: DataTypes.STRING,
            allowNull: true,
        },

        status: {
            type: DataTypes.ENUM(...Object.values(DEBIT_NOTE_STATUS)),
            allowNull: false,
            defaultValue: DEBIT_NOTE_STATUS.UNPAID,
        },
        source: {
            type: DataTypes.ENUM(...Object.values(DEBIT_NOTE_SOURCE)),
            allowNull: false,
        },
        is_manual: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false
        },
        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'debit-notes',
        timestamps: true,
        paranoid: true,
    },
);

DebitNoteTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "DebitNote",
        instance,
        options
    );
});

DebitNoteTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "DebitNote",
        instance,
        options
    );
});

DebitNoteTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "DebitNote",
        instance,
        options
    );
});

export { DebitNoteTable };