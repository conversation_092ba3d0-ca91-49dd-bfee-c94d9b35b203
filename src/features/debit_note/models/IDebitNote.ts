import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { DEBIT_NOTE_SOURCE, DEBIT_NOTE_STATUS } from "./DebitNoteMisc";


interface ICreateDebitNote {
    purchaseInvoiceId: number;
    rawMaterialId: number;
    qty: number;
    actualPrice: number;
    source: DEBIT_NOTE_SOURCE;
    purchasedPrice: number;
    note:string | null,
    createdById: number;
    is_manual:boolean;
}

interface IMarkDebitNotePaid {
    purchaseInvoiceId: number;
    updatedId: number;
    note: string | null;
}

interface IDebitNote extends ICreateDebitNote, InterfaceMetaData {
    is_manual: boolean;
    note: string | null;
    status: DEBIT_NOTE_STATUS;

}

interface IDebitNoteResponse {
    id: number;
    supplier: string;
    rawMaterial: string;
    qty: number;
    purchaseInvoiceId: number;
    purchaseInvoice: string;
    purchaseInvoiceDate: Date;
    actualPrice: number;
    purchasedPrice: number;
    debitAmount: number;
    status: DEBIT_NOTE_STATUS;
    source: DEBIT_NOTE_SOURCE;
    rejectionReason: string | null;
}

interface IDebitNoteOverviewResponse {
    supplier: string;
    purchaseInvoiceId: number;
    purchaseInvoice: string;
    purchaseInvoiceDate: Date;
    debitAmount: number;
    source: DEBIT_NOTE_SOURCE;
}
//For Manual
interface IDebitNoteData {
    rawMaterialId: number;
    rejectedQty: number;
    rejectedReason: string;
}

interface ICreateDebitNotesData {
    purchaseInvoiceId: number;
    debitNotes:IDebitNoteData[];
    createdById:number;
}

interface IUpdateDebitNote extends ICreateDebitNote {
    updatedById:number;
}

export { IDebitNote, ICreateDebitNote, IDebitNoteResponse, IMarkDebitNotePaid, IDebitNoteOverviewResponse, ICreateDebitNotesData, IDebitNoteData, IUpdateDebitNote };