import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { DebitNoteTable } from "../database/DebitNoteTable";
import { ICreateDebitNote, ICreateDebitNotesData, IDebitNoteOverviewResponse, IDebitNoteResponse, IMarkDebitNotePaid, IUpdateDebitNote, } from "../models/IDebitNote";

export interface IDebitNoteRepo {
    create(payload: ICreateDebitNote, transaction: Transaction): Promise<APIBaseResponse<DebitNoteTable | null>>;

    getAll(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IDebitNoteOverviewResponse> | null>>;

    searchByText(text: string, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IDebitNoteOverviewResponse> | null>>;

    getByPurchasInvoiceId(purchaseInvoiceId: number, transaction: Transaction): Promise<APIBaseResponse<IDebitNoteResponse[] | null>>;

    markPaid(payload: IMarkDebitNotePaid, transaction: Transaction): Promise<APIBaseResponse<null>>;

    createDebitNotes(payload: ICreateDebitNotesData, transaction: Transaction): Promise<APIBaseResponse<DebitNoteTable | null>>

    updateDebitNotes(id: Number, payload: IUpdateDebitNote, transaction: Transaction): Promise<APIBaseResponse<DebitNoteTable | null>>

}