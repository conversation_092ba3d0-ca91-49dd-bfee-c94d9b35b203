import { NextFunction, Request, Response } from "express";
import { ICreateDebitNotesData, IMarkDebitNotePaid, IUpdateDebitNote } from "../models/IDebitNote";
import { RepoProvider } from "../../../core/RepoProvider";
import { get } from "lodash";
import { sequelizeInit } from "../../../sequelize_init";
import { HelperMethods } from "../../../core/HelperMethods";

export class DebitNoteController {

    static async create(req: Request, res: Response, next: NextFunction) {
        // const T = await sequelizeInit.transaction()
        // try {
        // const userId = get(req, "user_id");

        // const payload = pick(req.body, ["name", "status",]) as ICreateDebitNote;
        // payload.status = ITEM_CATEGORY_STAUS.ACTIVE;
        // payload.createdById = Number(userId);
        // payload.name = payload.name.toLowerCase();

        // const result = await RepoProvider.DebitNoteRepo.create(payload,T);
        // if (!result.success) {
        // throw new Error(result.message);
        // }
        // await T.commit()
        // res.status(200).send(result);
        // } catch (error) {
        //     await T.rollback()
        //     const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
        //     res.status(500).send(HelperMethods.getErrorResponse(msg))
        // }

    }

    static async markPaid(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id");

            const payload: IMarkDebitNotePaid = {
                purchaseInvoiceId: req.body.purchaseInvoiceId,
                note: req.body.note,
                updatedId: Number(userId!),
            };

            const result = await RepoProvider.debitNoteRepo.markPaid(payload, T);
            if (!result.success) {
                throw new Error(result.message);
            }
            res.status(200).send(result);
            await T.commit()
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : unable to mark paid"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }



    static async getAll(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));

            const result = await RepoProvider.debitNoteRepo.getAll(page, pageSize, T);
            if (!result.success) {
                throw new Error(result.message);
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async getByPurchasInvoiceId(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.debitNoteRepo.getByPurchasInvoiceId(Number(id), T);
            if (!result.success) {
                throw new Error(result.message);
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }


    static async searchByText(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const text = get(req.query, "text") as string;
            const result = await RepoProvider.debitNoteRepo.searchByText(text, T);
            if (!result.success) {
                throw new Error(result.message);
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async createDebitNotes(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id");
            const payload: ICreateDebitNotesData = { ...req.body, createdById: Number(userId!) };
            const result = await RepoProvider.debitNoteRepo.createDebitNotes(payload, T);
            if (!result.success) {
                throw new Error(result.message);
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async updateDebitNotes(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id");
            const payload: IUpdateDebitNote = {
                ...req.body,
                updatedById: Number(userId!),
            };
            const id = Number(get(req.params, "id"));
            const result = await RepoProvider.debitNoteRepo.updateDebitNotes(id, payload, T);
            if (!result.success) {
                throw new Error(result.message);
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

}