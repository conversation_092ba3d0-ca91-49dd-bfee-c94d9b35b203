import { z } from "zod";
import { DEBIT_NOTE_SOURCE } from "../models/DebitNoteMisc";

export class DebitNoteSchema {
    static markPaidSchema =
        z.object({
            id: z.number().positive("Invalid is to update"),
            note: z.string().nullable(),
        });

    static searchByText =
        z.object({
            text: z.string().min(3, "Invalid request"),
        });
    
    static DebitNoteSchema = z.object({
            rawMaterialId: z.number().int().positive({ message: "Raw Material ID must be a positive integer" }),
            rejectedQty: z.number().positive({ message: "Rejected Quantity must be a positive integer" }),
            rejectedReason: z.string().min(3, { message: "Rejected Reason must be at least 3 characters long" }),
          });
    
    static CreateDebitNotesSchema = z.object({
        purchaseInvoiceId: z.number().int().positive({ message: "Purchase Invoice ID must be a positive integer" }),
        debitNotes: z.array(this.DebitNoteSchema).min(1, { message: "At least one debit note is required" }),
      });

    static DebitNoteSourceSchema = z.nativeEnum(DEBIT_NOTE_SOURCE);

    static UpdateDebitNotesSchema = z.object({
        purchaseInvoiceId: z.number().int().positive({
          message: "Purchase Invoice ID must be a positive integer",
        }),
        rawMaterialId: z.number().int().positive({
          message: "Raw Material ID must be a positive Number",
        }),
        qty: z.number().positive({ message: "Quantity must be a positive integer" }),
        actualPrice: z.number().positive({ message: "Actual price must be a positive number" }),
        source: this.DebitNoteSourceSchema,
        purchasedPrice: z.number().positive({ message: "Purchased price must be a positive number" }),
        note: z.string().max(255, "Note must be at most 255 characters").nullable().optional(),
        is_manual: z.boolean().optional(),
      });

}