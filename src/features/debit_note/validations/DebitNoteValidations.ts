import { Request, Response, NextFunction } from "express";
import { HelperMethods } from "../../../core/HelperMethods";
import { get, } from "lodash";
import { DebitNoteSchema } from "./DebitNoteSchema";
import { CoreSchemas } from "../../../core/CoreSchemas";

export class DebitNoteValidations {

    static validateMarkPaid = (req: Request, res: Response, next: NextFunction) => {

        const result = DebitNoteSchema.markPaidSchema.safeParse(req.body);

        let errorMessage = "";
        if (!result.success) {

            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString().toUpperCase() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }



    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {

        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }

    static validateGetById = (req: Request, res: Response, next: NextFunction) => {

        const id = get(req.params, "id");
        if (!id || typeof Number(id) !== "number") {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid id"));
            return;
        }

        return next();
    }

    static validateSearch = (req: Request, res: Response, next: NextFunction) => {

        const text = get(req.query, "text");
        if (!text) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }


    static validateSearchByText = (req: Request, res: Response, next: NextFunction) => {
        const result = DebitNoteSchema.searchByText.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }

    static validateCreateManual = (req: Request, res: Response, next: NextFunction) => {
        const result = DebitNoteSchema.CreateDebitNotesSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }

    static validateUpdateManual = (req: Request, res: Response, next: NextFunction) => {
        const result = DebitNoteSchema.UpdateDebitNotesSchema.safeParse(req.body);
        
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }
}