import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { DepartmentTable } from "../database/DepartmentTable";
import { ICreateDepartment, IDepartmentResponse } from "../models/IDepartment";

export interface IDepartmentRepo {
    create(payload: ICreateDepartment, transaction: Transaction): Promise<APIBaseResponse<DepartmentTable | null>>;

    update(id: number, payload: ICreateDepartment, transaction: Transaction): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IDepartmentResponse> | null>>;

    searchByText(text: string, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IDepartmentResponse> | null>>;

    getById(id: number, transaction: Transaction): Promise<APIBaseResponse<IDepartmentResponse | null>>;

    delete(ids: number[], deletedById: number, transaction: Transaction): Promise<APIBaseResponse<null>>;
}
