import * as express from "express";
import { DepartmentValidations } from "../validations/DepartmentValidations";
import { DepartmentController } from "../controller/DepartmentController";
import { permissionsMiddleware } from "../../../middlewares/permissionsMiddleware";
import { AppPermissions } from "../../users/sub_feaures/user_permissions/AppPermissions";

const apiInitialPath = "/departments";
const departmentRouter = express.Router();

departmentRouter.post(apiInitialPath + "/create",
    permissionsMiddleware(AppPermissions.DEPARTMENT.CREATE),
    DepartmentValidations.validateCreate, DepartmentController.create);

departmentRouter.put(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.DEPARTMENT.UPDATE),
    DepartmentValidations.validateUpdate, DepartmentController.update);

departmentRouter.delete(apiInitialPath + "/delete",
    permissionsMiddleware(AppPermissions.DEPARTMENT.UPDATE),
    DepartmentValidations.validateDelete, DepartmentController.delete);

departmentRouter.get(apiInitialPath + "/searchByText",
    // permissionsMiddleware(AppPermissions.DEPARTMENT.READ),
    // permissionsMiddleware(AppPermissions.PURCHASE_ORDER.READ),
    DepartmentValidations.validateSearchByText, DepartmentController.searchByText);

departmentRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.DEPARTMENT.READ),
    DepartmentValidations.validateGetAll, DepartmentController.getAll);

departmentRouter.get(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.DEPARTMENT.READ),
    DepartmentValidations.validateGetById, DepartmentController.getById);

export { departmentRouter };
