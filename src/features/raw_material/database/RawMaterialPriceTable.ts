import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreateRawMaterialPrice, IRawMaterialPrice } from '../models/IRawMaterial';
import { SupplierTable } from '../../supplier/database/SupplierTable';
import { RepoProvider } from '../../../core/RepoProvider';


class RawMaterialPriceTable extends Model<IRawMaterialPrice, ICreateRawMaterialPrice> {
    declare supplier: SupplierTable;
}

RawMaterialPriceTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        price: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        moq: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        supplierId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.supplierId;
                if (value) {
                    return Number(value);
                }
            },
        },
        rawMaterialId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return Number(value);
                }
            },
        },

        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw-materials-prices',
        timestamps: true,
        paranoid: true,
    },
);



RawMaterialPriceTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "RawMaterialPrice",
        instance,
        options
    );
});

RawMaterialPriceTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "RawMaterialPrice",
        instance,
        options
    );
});

RawMaterialPriceTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "RawMaterialPrice",
        instance,
        options
    );
});

export { RawMaterialPriceTable };