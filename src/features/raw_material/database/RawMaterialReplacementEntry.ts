import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from '../../../core/RepoProvider';
import { ICreateRawMaterialReplacementEntry, IRawMaterialReplacementEntry } from '../models/IRawMaterialReplacementEntry';


class RawMaterialReplacementEntryTable extends Model<IRawMaterialReplacementEntry, ICreateRawMaterialReplacementEntry> {

}

RawMaterialReplacementEntryTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        purchaseInvoiceId:{
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.purchaseInvoiceId;
                if (value) {
                    return Number(value);
                }
            },
        },
        rawMaterialId:{
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return Number(value);
                 }
            },
        },
       qty:{
        type: DataTypes.DECIMAL(10,2),
        allowNull: false,
       },
        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw_material_replacement_entries',
        timestamps: true,
        paranoid: true,
    },
);

RawMaterialReplacementEntryTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "RawMaterialReplacementEntries",
        instance,
        options
    );
});

RawMaterialReplacementEntryTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "RawMaterialReplacementEntries",
        instance,
        options
    );
});

RawMaterialReplacementEntryTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "RawMaterialReplacementEntries",
        instance,
        options
    );
});

export { RawMaterialReplacementEntryTable };