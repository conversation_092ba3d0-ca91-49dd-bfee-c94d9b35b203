import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from '../../../core/RepoProvider';
import { ICreateRawMaterialExcessEntry, IRawMaterialExcessEntry } from '../models/IRawMaterialExcessEntry';


class RawMaterialExcessEntryTable extends Model<IRawMaterialExcessEntry, ICreateRawMaterialExcessEntry> {
}

RawMaterialExcessEntryTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        purchaseInvoiceId:{
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.purchaseInvoiceId;
                if (value) {
                    return Number(value);
                }
            },
        },
        rawMaterialId:{
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return Number(value);
                 }
            },
        },
       qty:{
        type: DataTypes.DECIMAL(10,2),
        allowNull: false,
       },

        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw_material_excess_entries',
        timestamps: true,
        paranoid: true,
    },
);

RawMaterialExcessEntryTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "rawMaterialExcessEntries",
        instance,
        options
    );
});

RawMaterialExcessEntryTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "rawMaterialExcessEntries",
        instance,
        options
    );
});

RawMaterialExcessEntryTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "rawMaterialExcessEntries",
        instance,
        options
    );
});

export { RawMaterialExcessEntryTable };