import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { IRawMaterialVariation, ICreateRawMaterialVariation } from '../models/IRawMaterial';
import { RAW_MATERIAL_STAUS } from '../models/RawMaterialMisc';
import { RawMaterialPriceTable } from './RawMaterialPriceTable';
import { RepoProvider } from '../../../core/RepoProvider';
import { RawMaterialStockIssuanceTable } from '../../raw_material_stock/database/RawMaterialStockIssuanceTable';
import { RawMaterialVariationsAndAttributesRelationTable } from './RawMaterialsAndAttributesRelationTable';
import { RawMaterialMainTable } from './RawMaterialMainTable';


class RawMaterialVariationTable extends Model<IRawMaterialVariation, ICreateRawMaterialVariation> {
    declare prices: RawMaterialPriceTable[];
    declare stockIssuances: RawMaterialStockIssuanceTable[];
    declare attributesRelation: RawMaterialVariationsAndAttributesRelationTable[];
    declare rawMaterial:RawMaterialMainTable;
    
    
}

RawMaterialVariationTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        parentRawMaterialId: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
        },
        msq: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        sku: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        moq: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM(...Object.values(RAW_MATERIAL_STAUS)),
            allowNull: false,
        },
        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        },
      
        // hsn: {
        //     type: DataTypes.STRING,
        //     allowNull: false,
        // },
        // gstPercentage: {
        //     type: DataTypes.DECIMAL(10, 2),
        //     allowNull: false,
        // },
        // unitId: {
        //     type: DataTypes.INTEGER,
        //     allowNull: false,
          
        // },
        // categoryId: {
        //     type: DataTypes.INTEGER,
        //     allowNull: false,
        // } 
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw-materials',
        timestamps: true,
        paranoid: true,

    },
);


RawMaterialVariationTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "RawMaterial",
        instance,
        options
    );
});

RawMaterialVariationTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "RawMaterial",
        instance,
        options
    );
});

RawMaterialVariationTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "RawMaterial",
        instance,
        options
    );
});


export { RawMaterialVariationTable };