import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { RAW_MATERIAL_STAUS } from '../models/RawMaterialMisc';
import { ItemUnitTable } from '../../item_unit/database/ItemUnitTable';
import { ItemCategoryTable } from '../../item_category/database/ItemCategoryTable';
import { RepoProvider } from '../../../core/RepoProvider';
import { ICreateRawMaterialMain, IRawMaterialMain } from '../models/IRawMaterialMain';
import { RawMaterialVariationTable } from './RawMaterialVariationTable';


class RawMaterialMainTable extends Model<IRawMaterialMain, ICreateRawMaterialMain> {
    declare unit: ItemUnitTable;
    declare category: ItemCategoryTable;
    declare variations: RawMaterialVariationTable[];
}

RawMaterialMainTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,

        },
        categoryId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: ItemCategoryTable,
                key: 'id',
            },
        },
        unitId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: ItemUnitTable,
                key: 'id',
            },
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        hsn: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        gstPercentage: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM(...Object.values(RAW_MATERIAL_STAUS)),
            allowNull: false,
        },
        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw_materials_main',
        timestamps: true,
        paranoid: true,
        indexes: [
            {
                unique: true,
                fields: ["name"],
                name: 'unique_raw_material_name',
            },
        ],

    },
);


RawMaterialMainTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "RawMaterialsMain",
        instance,
        options
    );
});

RawMaterialMainTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "RawMaterialsMain",
        instance,
        options
    );
});

RawMaterialMainTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "RawMaterialsMain",
        instance,
        options
    );
});


export { RawMaterialMainTable };