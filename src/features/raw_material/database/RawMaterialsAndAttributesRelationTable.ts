import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from '../../../core/RepoProvider';
import { ICreateRawMaterialsAndAttributesRelation, IRawMaterialsAndAttributesRelation } from '../models/RawMaterialsAndAttributesRelation';
import { RawMaterialVariationTable } from './RawMaterialVariationTable';
import { ItemAttributeValueTable } from '../../item_attribute_value/database/ItemAttributeValueTable';


class RawMaterialVariationsAndAttributesRelationTable extends Model<IRawMaterialsAndAttributesRelation, ICreateRawMaterialsAndAttributesRelation> {
    declare attributeValue: ItemAttributeValueTable;

}

RawMaterialVariationsAndAttributesRelationTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        rawMaterialVariationId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            field: 'raw_material_variation_id',
            references: {
                model: 'raw-materials',
                key: 'id',
            },
            

        },
        itemAttributeValueId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            field: 'item_attribute_value_id',
            references: {
                model: 'item_attributes_values',
                key: 'id',
            },
        },
        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,

        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,

        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,

        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw-material-variations-and-attributes-relation',
        timestamps: true,
        paranoid: true,
        underscored: true,
        indexes: [
            {
                unique: true,
                fields: ["raw_material_variation_id", "item_attribute_value_id"],
                name: 'unique_raw_material_variation_and_attribute_relation',
            },
        ],

    },
);


RawMaterialVariationsAndAttributesRelationTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "RawMaterial",
        instance,
        options
    );
});

RawMaterialVariationsAndAttributesRelationTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "RawMaterial",
        instance,
        options
    );
});

RawMaterialVariationsAndAttributesRelationTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "RawMaterial",
        instance,
        options
    );
});


export { RawMaterialVariationsAndAttributesRelationTable  };