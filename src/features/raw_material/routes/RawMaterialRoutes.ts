import * as express from "express";
import { RawMaterialValidations } from "../validations/RawMaterialValidations";
import { RawMaterialController } from "../controller/RawMaterialController";
import { AppPermissions } from "../../users/sub_feaures/user_permissions/AppPermissions";
import { permissionsMiddleware } from "../../../middlewares/permissionsMiddleware";

const apiInitialPath = "/raw-materials";
const variationsInitialPath = "/raw-materials/variations";
const rawMaterialRouter = express.Router();


rawMaterialRouter.post(variationsInitialPath + "/",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.UPDATE),
    RawMaterialValidations.validateAddVariation, RawMaterialController.addNewVariation);

rawMaterialRouter.put(variationsInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.UPDATE),
    RawMaterialValidations.validateUpdateVariation, RawMaterialController.updateVariation);

rawMaterialRouter.delete(variationsInitialPath + "/",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.UPDATE),
    RawMaterialValidations.validateDeleteVariation, RawMaterialController.deleteVariation);

rawMaterialRouter.get(variationsInitialPath + "/",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.READ),
    RawMaterialValidations.validateGetAll, RawMaterialController.getVariations);


rawMaterialRouter.post(apiInitialPath + "/create",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.CREATE),
    RawMaterialValidations.validateCreate, RawMaterialController.create);


rawMaterialRouter.put(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.UPDATE),
    RawMaterialValidations.validateUpdate, RawMaterialController.updateRawMaterial);

rawMaterialRouter.get(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.READ),
    RawMaterialValidations.validateGetById, RawMaterialController.getRawMaterialById);

rawMaterialRouter.delete(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.UPDATE),
    RawMaterialValidations.validateDelete, RawMaterialController.deleteRawMaterials);
rawMaterialRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL.READ),
    RawMaterialValidations.validateGetAll, RawMaterialController.getRawMaterials);


// rawMaterialRouter.delete(apiInitialPath + "/delete", RawMaterialValidations.validateDelete, RawMaterialController.delete);




// rawMaterialRouter.get(apiInitialPath + "/searchByText",
//     permissionsMiddleware(AppPermissions.RAW_MATERIAL.READ),
//     RawMaterialValidations.validateSearchByText, RawMaterialController.searchByText);



export { rawMaterialRouter };