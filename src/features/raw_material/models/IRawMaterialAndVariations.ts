import { z } from "zod";
import { IItemAttribute } from "../../item_attribute/models/IItemAttribute";
import { IItemAttributeValue } from "../../item_attribute_value/models/IItemAttributeValue";
import { IItemCategory } from "../../item_category/models/IItemCategory";
import { IItemUnit } from "../../item_unit/models/IItemUnit";
import { ISupplier } from "../../supplier/models/ISupplier";
import { RAW_MATERIAL_STAUS } from "./RawMaterialMisc";

interface IRawMaterialAddRequest {
    rawMaterial: {
        categoryId: number;
        unitId: number;
        name: string;
        hsn: string;
        gstPercentage: number;
    },
    variations: {
        name: string;
        sku: string;
        msq: number;
        moq: number;
        attributes: {
            attributeValueId: number;
        }[];
        priceData: {
            supplierId: number;
            price: number;
        }[];
    }[];
}

interface IRawMaterialOverview {
    id: number;
    name: string;
    hsn: string;
    gstPercentage: number;
    unitName: string;
    categoryName: string;
}

interface IRawMaterialAddPayload extends IRawMaterialAddRequest {
    createdById: number;
}


interface IRawMaterialVariationNested {
    id: number;
    sku: string;
    name: string;
    msq: number;
    moq: number;
    attributes: {
        attribute: IItemAttribute;
        attributeValue: IItemAttributeValue;
    }[];
    priceData: {
        supplier: ISupplier;
        price: number;
    }[];
}

interface IRawMaterialNew {
    rawMaterial: {
        id: number;
        category: IItemCategory;
        unit: IItemUnit;
        name: string;
        hsn: string;
        gstPercentage: number;
        status: RAW_MATERIAL_STAUS;
    },
    variations: IRawMaterialVariationNested[];
}

interface IRawMaterialUpdateRequest {
    id: number;
    categoryId: number;
    unitId: number;
    name: string;
    hsn: string;
    gstPercentage: number;
    updatedById: number;
}


interface IRawMaterialDeleteRequest {
    ids: number[];
    deletedById: number;
}

const AddVariationRequestSchema = z.object({
    rawMaterialId: z.number().int().positive("Invalid raw material id"),
    name: z.string().min(3, "Name must be at least 3 characters long").max(100, "Name must be up to 100 characters long"),
    sku: z.string().min(3, "SKU must be at least 3 characters long").max(100, "SKU must be up to 100 characters long"),
    msq: z.number().nonnegative("MSQ must be a positive number"),
    moq: z.number().nonnegative("MOQ must be a positive number"),
    attributes: z.array(
        z.object({
            attributeValueId: z.number().int().positive("Invalid attribute value id"),
        })
    ).min(1, "At least one attribute is required"),
    priceData: z.array(
        z.object({
            supplierId: z.number(),
            price: z.number()
        })
    ).min(1, "At least one price data is required"),
});

const AddVariationRequestSchemaArray = z.array(AddVariationRequestSchema);

type TAddVariationRequest = z.infer<typeof AddVariationRequestSchema> & { createdById: number };

const UpdateVariationRequestSchema = AddVariationRequestSchema.extend({
    id: z.number().int().positive("Invalid variation id"),
});

type TUpdateVariationRequest = z.infer<typeof UpdateVariationRequestSchema> & { updatedById: number };

const DeleteVariationRequestSchema = z.object({
    ids: z.array(z.number().int().positive("Invalid variation ids")).min(1, "At least one variation id is required"),
});

type TDeleteVariationRequest = z.infer<typeof DeleteVariationRequestSchema> & { deletedById: number };



interface IRawMaterialVariationResponse {
    id: number;
    name: string;
    unit: IItemUnit;
    category: IItemCategory;
    sku: string;
    msq: number;
    hsn: string;
    gstPercentage: number;
    priceData: {
        supplier: ISupplier;
        price: number;
    }[];
}

export {
    IRawMaterialAddRequest, IRawMaterialAddPayload, IRawMaterialOverview,
    IRawMaterialNew,
    IRawMaterialVariationNested,
    IRawMaterialUpdateRequest,
    IRawMaterialDeleteRequest,
    TAddVariationRequest,
    TUpdateVariationRequest,
    TDeleteVariationRequest,
    AddVariationRequestSchema,
    UpdateVariationRequestSchema,
    DeleteVariationRequestSchema,
    AddVariationRequestSchemaArray,
    IRawMaterialVariationResponse,
};