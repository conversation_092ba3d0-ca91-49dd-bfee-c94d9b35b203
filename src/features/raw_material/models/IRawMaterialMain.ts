import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { RAW_MATERIAL_STAUS } from "./RawMaterialMisc";



interface ICreateRawMaterialMain {
    categoryId: number;
    unitId: number;
    name: string;
    hsn: string;
    gstPercentage: number;
    status: RAW_MATERIAL_STAUS;
    createdById: number;
}

interface IRawMaterialMain extends ICreateRawMaterialMain, InterfaceMetaData {
    id: number;
}

export { IRawMaterialMain, ICreateRawMaterialMain };