import { InterfaceMetaData } from "../../../core/CoreInterfaces";

interface ICreateRawMaterialsAndAttributesRelation {
    rawMaterialVariationId: number;
    itemAttributeValueId: number;
    createdById: number;

}

interface IRawMaterialsAndAttributesRelation extends ICreateRawMaterialsAndAttributesRelation, InterfaceMetaData {

}

export { ICreateRawMaterialsAndAttributesRelation, IRawMaterialsAndAttributesRelation };