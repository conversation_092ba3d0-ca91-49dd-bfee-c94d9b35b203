import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { RAW_MATERIAL_STAUS } from "./RawMaterialMisc";


interface ICreateRawMaterialVariation {
    parentRawMaterialId: number;
    name: string;
    sku: string;
    msq: number;
    moq: number;
    status: RAW_MATERIAL_STAUS;
    createdById: number;
}

interface IRawMaterialVariation extends Omit<ICreateRawMaterialVariation, "priceData">, InterfaceMetaData {
    id: number;
}

interface IRawMaterialDetails extends IRawMaterialVariation {
    unitName: string;
    categoryName: string;
    priceData: IRawMaterialPriceDetails[];
}

interface ISingleRawMaterialDetails extends IRawMaterialVariation {
    unitName: string;
    categoryName: string;
    price: number;
}

interface ICreateRawMaterialPrice {
    price: number;
    moq: number;
    supplierId: number;
    rawMaterialId: number;
    createdById: number;
}

interface IUpdateRawMaterialPrice {
    price: number;
    moq: number;
    supplierId: number;
    rawMaterialId: number;
    updatedById: number;
}

interface IRawMaterialPrice extends InterfaceMetaData, ICreateRawMaterialPrice {
    averagePrice?: number;
}

interface IRawMaterialPriceDetails {
    price: number;
    moq: number;
    supplierId: number;
    supplier: string;
    rawMaterialId: number;
    rawMaterial: string;
}

export { IRawMaterialVariation, IRawMaterialDetails, IRawMaterialPriceDetails, IRawMaterialPrice, ICreateRawMaterialPrice, IUpdateRawMaterialPrice, ISingleRawMaterialDetails, ICreateRawMaterialVariation };