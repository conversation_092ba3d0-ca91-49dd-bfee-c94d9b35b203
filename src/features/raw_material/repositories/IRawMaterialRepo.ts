import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { IRawMaterialDetails, IRawMaterialVariation, ISingleRawMaterialDetails } from "../models/IRawMaterial";
import { TAddVariationRequest, IRawMaterialAddPayload, IRawMaterialDeleteRequest, IRawMaterialNew, IRawMaterialOverview, IRawMaterialUpdateRequest, IRawMaterialVariationNested, TUpdateVariationRequest, TDeleteVariationRequest, IRawMaterialVariationResponse } from "../models/IRawMaterialAndVariations";

export interface IRawMaterialRepo {
    create(payload: IRawMaterialAddPayload,transaction:Transaction): Promise<APIBaseResponse<null>>;
    getRawMaterials(page: number, pageSize: number, transaction:Transaction,text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialOverview> | null>>;
    getRawMaterialById(id: number,transaction:Transaction): Promise<APIBaseResponse<IRawMaterialNew | null>>;
    updateRawMaterial(payload: IRawMaterialUpdateRequest,transaction:Transaction): Promise<APIBaseResponse<null>>;
    deleteRawMaterials(payload: IRawMaterialDeleteRequest,transaction:Transaction): Promise<APIBaseResponse<null>>;
    addNewVariations(payload: TAddVariationRequest[],transaction:Transaction): Promise<APIBaseResponse<IRawMaterialVariationNested[] | null>>;
    updateVariation(payload: TUpdateVariationRequest,transaction:Transaction): Promise<APIBaseResponse<IRawMaterialVariationNested | null>>;
    deleteVariation(payload: TDeleteVariationRequest,transaction:Transaction): Promise<APIBaseResponse<null>>;
    getvariations(page: number, pageSize: number, transaction:Transaction,text?: string,supplierId?: number): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialVariationResponse> | null>>;
}