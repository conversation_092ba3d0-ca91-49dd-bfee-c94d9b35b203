import { text } from "stream/consumers";
import { z } from "zod";

export class RawMaterialSchema {

    private static rawMaterialPriceData = z.object({
        price: z.number().positive("Price must be a positive number"),
        supplierId: z.number().positive("Supplier Id must be a positive number"),
    });

    static create =
        z.object({
            rawMaterial: z.object({
                name: z.string().min(3, "Raw Material Name must be at least 3 characters long").max(100, "Name must be up to 100 characters long"),
                unitId: z.number().positive("Unit Id must be a positive number"),
                categoryId: z.number().positive("Category Id must be a positive number"),
                hsn: z.string().min(3, "HSN must be at least 3 characters long").max(30, "HSN must be up to 30 characters long"),
                gstPercentage: z.number().positive("GST percentage must be a positive number"),
            }),
            variations: z.array(
                z.object({
                    name: z.string().min(3, "Variation Name must be at least 3 characters long").max(100, "Name must be up to 100 characters long"),
                    sku: z.string().min(3, "SKU must be at least 3 characters long").max(100, "SKU must be up to 100 characters long"),
                    msq: z.number().nonnegative("MSQ must be a positive number"),
                    moq: z.number().nonnegative("MOQ must be a positive number"),
                    attributes: z.array(
                        z.object({
                            attributeValueId: z.number().positive("Attribute Value Id must be a positive number"),
                        })
                    ),
                    priceData: z.array(RawMaterialSchema.rawMaterialPriceData),
                })
            ),
        });

    static update =
        z.object({
            id: z.number().positive("Id must be a positive number"),
            name: z.string().min(3, "Raw Material Name must be at least 3 characters long").max(100, "Name must be up to 100 characters long"),
            unitId: z.number().positive("Unit Id must be a positive number"),
            categoryId: z.number().positive("Category Id must be a positive number"),
            hsn: z.string().min(3, "HSN must be at least 3 characters long").max(30, "HSN must be up to 30 characters long"),
            gstPercentage: z.number().positive("GST percentage must be a positive number"),
        });
}
