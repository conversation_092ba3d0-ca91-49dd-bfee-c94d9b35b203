import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { IRawMaterialAddPayload, IRawMaterialAddRequest, IRawMaterialDeleteRequest, IRawMaterialUpdateRequest, TAddVariationRequest, TDeleteVariationRequest, TUpdateVariationRequest } from "../models/IRawMaterialAndVariations";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";

export class RawMaterialController {

    static async create(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const userId = get(req, "user_id");

            const bodyData = req.body as IRawMaterialAddRequest;
            const payload: IRawMaterialAddPayload = {
                rawMaterial: {
                    categoryId: bodyData.rawMaterial.categoryId,
                    unitId: bodyData.rawMaterial.unitId,
                    name: bodyData.rawMaterial.name.trim().toLowerCase(),
                    hsn: bodyData.rawMaterial.hsn.trim().toLowerCase(),
                    gstPercentage: bodyData.rawMaterial.gstPercentage,
                },
                variations: bodyData.variations.map(data => ({
                    name: data.name.trim().toLowerCase(),
                    sku: data.sku.trim().toLowerCase(),
                    msq: data.msq,
                    moq: data.moq,
                    attributes: data.attributes.map(attribute => ({
                        attributeValueId: attribute.attributeValueId,
                    })),
                    priceData: data.priceData.map(priceData => ({
                        supplierId: priceData.supplierId,
                        price: priceData.price,
                    })),
                })),
                createdById: Number(userId!),
            };
            const result = await RepoProvider.rawMaterialRepo.create(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async updateRawMaterial(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = Number(get(req.params, "id"));
            const userId = get(req, "user_id",);
            const bodydata = req.body as IRawMaterialUpdateRequest;
            const payload: IRawMaterialUpdateRequest = {
                id: id,
                categoryId: bodydata.categoryId,
                unitId: bodydata.unitId,
                name: bodydata.name.trim().toLowerCase(),
                hsn: bodydata.hsn.trim().toLowerCase(),
                gstPercentage: bodydata.gstPercentage,
                updatedById: Number(userId!),
            };

            const result = await RepoProvider.rawMaterialRepo.updateRawMaterial(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async deleteRawMaterials(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const bodyData: any = pick(req.body, "ids");
            const userId = get(req, "user_id",);
            const payload: IRawMaterialDeleteRequest = {
                ids: bodyData.ids,
                deletedById: Number(userId!),
            };
            const result = await RepoProvider.rawMaterialRepo.deleteRawMaterials(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not deleted"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getRawMaterials(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            let page = Number(get(req.query, "page"));
            if (page < 0) {
                page = 0;
            }

            let pageSize = Number(get(req.query, "pageSize"));


            let text = get(req.query, "text") as string | undefined;
            if (text) {
                text = text.trim().toLowerCase();
            }

            const result = await RepoProvider.rawMaterialRepo.getRawMaterials(page, pageSize, T, text);

            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async getRawMaterialById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.rawMaterialRepo.getRawMaterialById(Number(id), T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
    static async addNewVariation(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id",);
            const bodyData = req.body as TAddVariationRequest[];
            const payload: TAddVariationRequest[] = [];
            for (const data of bodyData) {
                payload.push({
                    rawMaterialId: data.rawMaterialId,
                    name: data.name.trim().toLowerCase(),
                    sku: data.sku.trim().toLowerCase(),
                    msq: data.msq,
                    moq: data.moq,
                    attributes: data.attributes,
                    priceData: data.priceData,
                    createdById: Number(userId!),
                });
            }

            const result = await RepoProvider.rawMaterialRepo.addNewVariations(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
    static async updateVariation(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id",);
            const id = get(req.params, "id");
            const bodyData = req.body as TUpdateVariationRequest;
            const payload: TUpdateVariationRequest = {
                id: Number(id),
                rawMaterialId: bodyData.rawMaterialId,
                name: bodyData.name.trim().toLowerCase(),
                sku: bodyData.sku.trim().toLowerCase(),
                msq: bodyData.msq,
                moq: bodyData.moq,
                attributes: bodyData.attributes,
                priceData: bodyData.priceData,
                updatedById: Number(userId!),
            };
            const result = await RepoProvider.rawMaterialRepo.updateVariation(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
    static async deleteVariation(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id",);
            const bodyData = req.body as TDeleteVariationRequest;
            const payload: TDeleteVariationRequest = {
                ids: bodyData.ids,
                deletedById: Number(userId!),
            };
            const result = await RepoProvider.rawMaterialRepo.deleteVariation(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not deleted"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
    static async getVariations(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
            let text = get(req.query, "text") as string | undefined;
            if (text) {
                text = text.trim().toLowerCase();
            }
            const supplierId = get(req.query, "supplierId") as number | undefined;
            const result = await RepoProvider.rawMaterialRepo.getvariations(page, pageSize, T, text, supplierId);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }


}