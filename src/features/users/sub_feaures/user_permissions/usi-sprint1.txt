USI Sprint 1 - Timeline: Feb 6, 2025

1. PO (Purchase Order) CRUD

    Create: The user will search for a supplier, then search for the raw materials offered by that supplier. The user will then input the quantity of each raw material and the expected delivery date. A Purchase Order (PO) number input field will be included.

    Affected Features: During purchase invoice creation, the user will now search by PO number.

    Update: Users must be able to update these details.

    Read: The table view will provide an overview of all POs. A detailed view will display the details of each PO.

    Database: Tables must have proper relationships. Operation logs will be maintained. POs will be of two types: Manual and Automatic. Create a nullable column, "SO Number" (Sales Order number), to link POs to SOs in the case of automatic POs.

2. Upload Opening Stock (Excel Sheet)

    Allow users to upload an Excel sheet with a predefined structure.

    UI: Include a button to upload the Excel sheet and a date selection control.

    API: Extract data from the Excel sheet. If the data is valid, insert it into the database. For every opening stock quantity, increment the total and usable stock of the corresponding raw material.

    Database: Create a new table (or tables), raw_materials_opening_stock_entries, to list all opening stock entries of a raw material, including the date and the user who saved the details.

3. Opening Stock (No Excel)

    UI: Users will be able to search for raw materials and select the quantity of each raw material in stock. A date selection control will be included.

    Database: Create a new table (or tables), raw_materials_opening_stock_entries, to list all opening stock entries of a raw material, including the date and the user who saved the details.

4. Raw Materials Linking with Item Attributes

    UI Changes: In the form, users will be able to select item attributes. Existing data will not have item attributes initially, but users will be able to add them.

    API Changes: Handle item attributes from the request body.

    Database Changes: Create a new table (or tables), raw_materials_variations, to list all the variations of a raw material according to its item attributes.