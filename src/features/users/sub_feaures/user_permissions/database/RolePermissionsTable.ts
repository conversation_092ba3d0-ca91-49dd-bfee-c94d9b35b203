import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from '../../../../../sequelize_init';
import { RepoProvider } from '../../../../../core/RepoProvider';
import { ICreateRolePermissions, IRolePermissions } from '../models/IRolePermission';
import { UserRoleTable } from '../../user_roles/database/UserRoleTable';


class RolePermissionsTable extends Model<IRolePermissions, ICreateRolePermissions> {
    declare role: UserRoleTable;
}

RolePermissionsTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },

        },
        roleId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.roleId;
                if (value) {
                    return Number(value);
                }
            },
        },

        permissions: {
            type: DataTypes.ARRAY(DataTypes.TEXT),
            allowNull: false,
        },

        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'role_permissions',
        timestamps: true,
        paranoid: true,
    },
);


RolePermissionsTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "role_permissions",
        instance,
        options
    );
});

RolePermissionsTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "role_permissions",
        instance,
        options
    );
});

RolePermissionsTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "role_permissions",
        instance,
        options
    );
});


export { RolePermissionsTable };