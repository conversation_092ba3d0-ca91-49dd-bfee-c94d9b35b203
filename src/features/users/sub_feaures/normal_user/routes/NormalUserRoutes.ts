import * as express from "express";
import { NormalUserValidations } from "../validations/NormalUserValidations";
import { NormalUserController } from "../controller/NormalUserController";
import { permissionsMiddleware } from "../../../../../middlewares/permissionsMiddleware";
import { AppPermissions } from "../../user_permissions/AppPermissions";

const apiInitialPath = "/users";
const normalUserRouter = express.Router();

normalUserRouter.post(apiInitialPath + "/create",
    permissionsMiddleware(AppPermissions.USER.CREATE),
    NormalUserValidations.validateCreate, NormalUserController.create);

normalUserRouter.put(apiInitialPath + "/update/:id",
    permissionsMiddleware(AppPermissions.USER.UPDATE),
    NormalUserValidations.validateUpdate, NormalUserController.update);

normalUserRouter.get(apiInitialPath + "/getByFirebaseToken",
    NormalUserController.getByFirebaseToken);

normalUserRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.USER.READ),
    NormalUserValidations.validateGetAll, NormalUserController.getAll);


normalUserRouter.post(apiInitialPath + "/reset-password",
    permissionsMiddleware(AppPermissions.USER.PASSWORD_RESET),
    NormalUserValidations.validateResetPassword,
    NormalUserController.resetPassword);

// Logout endpoint - clears session from Redis (must be before /:id route)
normalUserRouter.get(apiInitialPath + "/logout",
    NormalUserController.logout);

normalUserRouter.get(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.USER.READ),
    NormalUserValidations.validateGetById, NormalUserController.getById);


export { normalUserRouter };