import { Request, Response, NextFunction } from "express";
import { NormalUserSchema } from "./NormalUserSchema";
import { HelperMethods } from "../../../../../core/HelperMethods";
import { CoreSchemas } from "../../../../../core/CoreSchemas";

export class NormalUserValidations {

    static validateCreate = (req: Request, res: Response, next: NextFunction) => {
        const result = NormalUserSchema.createSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }


    static validateResetPassword = (req: Request, res: Response, next: NextFunction) => {
        const result = NormalUserSchema.resetPasswordSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {

        let result: any = CoreSchemas.updateByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        result = NormalUserSchema.updateSchema.safeParse(req.body);

        if (!result.success) {

            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }



    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        return next();
    }

    static validateGetById = (req: Request, res: Response, next: NextFunction) => {

        const result = CoreSchemas.getByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        return next();
    }
}