import { z } from "zod";
import { CoreUserSchema } from "../../../core/validations/CoreUserSchema";
import { AddressSchema } from "../../../../address/validations/AddressSchema";
import { USER_STAUS } from "../../../core/models/UserMisc";

export class NormalUserSchema {
    static createSchema =
        CoreUserSchema.createSchema.extend({
            address: AddressSchema.createSchema,
        });

    static updateSchema =
        NormalUserSchema.createSchema.omit({
            email: true,
            password: true,
        }).extend({
            status: z.nativeEnum(USER_STAUS).refine(val => Object.values(USER_STAUS).includes(val), {
                message: "Invalid status",
            }),
            address: AddressSchema.updateSchema,
        });


    static resetPasswordSchema =
     z.object({
         userId: z.number().positive("Invalid user Id"),
         password: z.string().min(6,"Password must be at least 6 characters"),
        });

}