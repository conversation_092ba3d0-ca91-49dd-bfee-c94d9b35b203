import { Op, Transaction, UniqueConstraintError, WhereOptions } from "sequelize";
import * as admin from "firebase-admin";
import { INormalUserRepo } from "./INormalUserRepo";
import { ICreateNormalUser, INormalUserResponse, IUpdateNormalUser, ResetPasswordPayload } from "../models/INormalUser";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../../../core/CoreInterfaces";
import { NormalUserTable } from "../database/NormalUserTable";
import { UserRoleTable } from "../../user_roles/database/UserRoleTable";
import { USER_ROLE_STATUS } from "../../user_roles/models/UserRolesMisc";
import { HelperMethods } from "../../../../../core/HelperMethods";
import { RepoProvider } from "../../../../../core/RepoProvider";
import { sequelizeInit } from "../../../../../sequelize_init";
import { CoreUserTable } from "../../../core/database/CoreUserTable";
import { AddressTable } from "../../../../address/database/AddressTable";
import { ADDRESS_STATUS } from "../../../../address/models/AddressMisc";
import { ICoreUser } from "../../../core/models/ICoreUser";
import { USER_STAUS } from "../../../core/models/UserMisc";


export class PostgresNormalUserRepo implements INormalUserRepo {

    async create(payload: ICreateNormalUser, transaction: Transaction): Promise<APIBaseResponse<NormalUserTable | null>> {

        let firebaseUserUid: string | null = null;

        try {

            /* check for valid role */
            const role = await UserRoleTable.findOne({
                where: {
                    id: payload.roleId,
                    status: USER_ROLE_STATUS.ACTIVE
                },
                transaction
            });

            if (!role) {
                return HelperMethods.getErrorResponse("Invalid role");
            }


            /* create firebase user */
            const firebaseUser = await admin.auth().createUser({
                email: payload.email,
                password: payload.password,
            });

            firebaseUserUid = firebaseUser.uid;

            payload.firebaseUID = firebaseUserUid;

            /* create core user */
            const coreUser = await CoreUserTable.create(payload, {
                transaction: transaction,
                userId: payload.createdById,
            });

            /* create address */
            const address = await AddressTable.create({
                street: payload.address.street,
                postalCode: payload.address.postalCode,
                city: payload.address.city,
                state: payload.address.state,
                country: payload.address.country,
                status: ADDRESS_STATUS.ACTIVE,
                createdById: payload.createdById,
            }, {
                transaction: transaction,
                userId: payload.createdById,
            });

            /* create normal user */
            const result = await NormalUserTable.create({
                coreUserId: coreUser.dataValues.id,
                addressId: address.dataValues.id,

            }, {
                userId: payload.createdById,
                transaction: transaction,
            });



            return HelperMethods.getSuccessResponse(result);
        } catch (error) {

            if (firebaseUserUid) {
                await admin.auth().deleteUser(firebaseUserUid);
            }
            HelperMethods.handleError(error);

            if ((error as any)?.errorInfo?.code === "auth/email-already-exists") {
                return HelperMethods.getErrorResponse('Email already exists');
            }
            else if ((error as any)?.errorInfo?.code === "auth/invalid-email") {
                return HelperMethods.getErrorResponse('Invalid email');
            }
            else if (error instanceof UniqueConstraintError) {

                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'mobile') {
                    return HelperMethods.getErrorResponse('Mobile number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(payload: IUpdateNormalUser, transaction: Transaction): Promise<APIBaseResponse<null>> {

        try {
            /* check if user exists */

            const user = await CoreUserTable.findOne({
                where: {
                    id: payload.id
                },
                transaction: transaction,
            });

            if (!user) {
                return HelperMethods.getErrorResponse("User not found");
            }


            /* check for valid role */
            const role = await UserRoleTable.findOne({
                where: {

                    id: payload.roleId,
                    status: USER_ROLE_STATUS.ACTIVE
                },
                transaction
            });

            if (!role) {
                return HelperMethods.getErrorResponse("Invalid role");
            }

            const deletionUpdates = {
                deletedAt: new Date(),
                deletedById: payload.updatedById,
            };
            if (payload.status === USER_STAUS.DELETED) {
                Object.assign(payload, deletionUpdates);
            }

            /* update core user */
            const coreUser = await CoreUserTable.update(payload, {
                where: {
                    id: user.dataValues.id
                },
                transaction: transaction,
                userId: payload.updatedById,
            });

            /* update address */
            const address = await AddressTable.update({
                street: payload.address.street,
                postalCode: payload.address.postalCode,
                city: payload.address.city,
                state: payload.address.state,
                country: payload.address.country,
                status: ADDRESS_STATUS.ACTIVE,
                updatedById: payload.updatedById,
            }, {
                where: {
                    id: payload.address.id
                },
                transaction: transaction,
                userId: payload.updatedById,
            });


            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'phone') {
                    return HelperMethods.getErrorResponse('Contact number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number, transaction: Transaction, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<INormalUserResponse> | null>> {
        try {
            let whereConditions: WhereOptions<ICoreUser> = {
                status: USER_STAUS.ACTIVE
            };

            if (text) {
                whereConditions = {
                    [Op.or]:
                        [
                            { firstName: { [Op.iLike]: `%${text}%` } },
                            { lastName: { [Op.iLike]: `%${text}%` } },
                            { email: { [Op.iLike]: `%${text}%` } },
                            { mobile: { [Op.iLike]: `%${text}%` } }
                        ],
                    status: USER_STAUS.ACTIVE,
                };
            }

            const offset = (page - 1) * pageSize;
            const { count, rows } = await NormalUserTable.findAndCountAll({
                attributes: ["id", "coreUserId", "addressId"],
                limit: pageSize,
                offset: offset,
                transaction,
                include: [
                    {
                        model: CoreUserTable,
                        as: 'coreUser',
                        where: whereConditions,
                        required: true,
                        order: [['createdAt', 'DESC']],
                        include: [
                            {
                                model: UserRoleTable,
                                as: 'role',
                                where: {
                                    status: USER_ROLE_STATUS.ACTIVE
                                }
                            }
                        ],
                    },
                    {
                        model: AddressTable,
                        as: 'address',
                        where: {
                            status: ADDRESS_STATUS.ACTIVE
                        }
                    }
                ],
            });

            const data: INormalUserResponse[] = rows.map(row => {
                return {
                    id: Number(row.dataValues.id),
                    coreUserId: Number(row.dataValues.coreUserId),
                    firstName: row.coreUser.dataValues.firstName,
                    lastName: row.coreUser.dataValues.lastName,
                    email: row.coreUser.dataValues.email,
                    mobile: row.coreUser.dataValues.mobile,
                    role: {
                        id: Number(row.coreUser.role.dataValues.id),
                        role: row.coreUser.role.dataValues.role,
                    },
                    status: row.coreUser.dataValues.status,
                    address: row.address.dataValues,
                    firebaseUID: row.coreUser.dataValues.firebaseUID,
                }
            });


            const totalPages = Math.ceil(count / pageSize);
            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number, transaction?: Transaction): Promise<APIBaseResponse<INormalUserResponse | null>> {
        try {
            const result = await NormalUserTable.findOne({
                attributes: ["id", "coreUserId", "addressId"],
                where: {
                    coreUserId: id
                }, transaction,
                include: [
                    {
                        model: CoreUserTable,
                        as: 'coreUser',
                        where: {
                            id: id
                        },
                        required: true,
                        include: [
                            {
                                model: UserRoleTable,
                                as: 'role',
                                where: {
                                    status: USER_ROLE_STATUS.ACTIVE
                                }
                            }
                        ],
                    },
                    {
                        model: AddressTable,
                        as: 'address',
                        where: {
                            status: ADDRESS_STATUS.ACTIVE
                        }
                    }
                ],
            });

            if (!result) {
                return HelperMethods.getErrorResponse("User not found");
            }

            const data: INormalUserResponse = {
                id: Number(result.dataValues.id),
                coreUserId: Number(result.dataValues.coreUserId),
                firstName: result.coreUser.dataValues.firstName,
                lastName: result.coreUser.dataValues.lastName,
                email: result.coreUser.dataValues.email,
                mobile: result.coreUser.dataValues.mobile,
                role: {
                    id: Number(result.coreUser.role.dataValues.id),
                    role: result.coreUser.role.dataValues.role,
                }, status: result.coreUser.dataValues.status,
                address: result.address.dataValues,
                firebaseUID: result.coreUser.dataValues.firebaseUID,
            }
            data.address.id = Number(data.address.id);
            return HelperMethods.getSuccessResponse(data);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }


    async resetPassword(payload: ResetPasswordPayload, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {

            const response = await RepoProvider.coreUserRepo.getById(payload.userId, transaction);
            if (!response.success) {
                return HelperMethods.getErrorResponse("User not found!");
            }

            const firebaseUID = response.data!.dataValues.firebaseUID;
            await admin.auth().updateUser(firebaseUID, { password: payload.password });
            await RepoProvider.logRepo.resetPasswordLog("update", "core_user", { data: "Password changed" }, payload.updatedById);
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getByFirebaseId(fbId: string, transaction: Transaction): Promise<APIBaseResponse<INormalUserResponse | null>> {
        try {
            const result = await CoreUserTable.findOne({
                where: {
                    firebaseUID: fbId
                },
                transaction,
                include: [
                    {
                        model: UserRoleTable,
                        as: 'role',
                        where: {
                            status: USER_ROLE_STATUS.ACTIVE
                        }
                    },
                    {
                        model: NormalUserTable,
                        as: 'normalUser',
                        required: true,
                        include: [
                            {
                                model: AddressTable,
                                as: 'address',
                                where: {
                                    status: ADDRESS_STATUS.ACTIVE
                                }
                            }
                        ]
                    }
                ],
            });

            if (!result) {
                return HelperMethods.getErrorResponse("User not found");
            }

            const data: INormalUserResponse = {
                id: Number(result.normalUser.dataValues.id),
                coreUserId: Number(result.dataValues.id),
                firstName: result.dataValues.firstName,
                lastName: result.dataValues.lastName,
                email: result.dataValues.email,
                mobile: result.dataValues.mobile,
                role: {
                    id: Number(result.role.dataValues.id),
                    role: result.role.dataValues.role,
                },
                status: result.dataValues.status,
                address: result.normalUser.address.dataValues,
                firebaseUID: result.dataValues.firebaseUID,
            }
            data.address.id = Number(data.address.id);
            return HelperMethods.getSuccessResponse(data);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async updateWithFirebaseUID(payload: IUpdateNormalUser, fbId: string, transaction: Transaction): Promise<APIBaseResponse<null>> {

        try {


            /* check if user exists */

            const user = await CoreUserTable.findOne({
                where: {
                    id: payload.id
                },
                transaction: transaction,
            });

            if (!user) {
                return HelperMethods.getErrorResponse("User not found");
            }


            /* check for valid role */
            const role = await UserRoleTable.findOne({
                where: {

                    id: payload.roleId,
                    status: USER_ROLE_STATUS.ACTIVE
                },
                transaction
            });

            if (!role) {
                return HelperMethods.getErrorResponse("Invalid role");
            }

            const deletionUpdates = {
                deletedAt: new Date(),
                deletedById: payload.updatedById,
            };
            if (payload.status === USER_STAUS.DELETED) {
                Object.assign(payload, deletionUpdates);
            }

            /* update core user */
            await CoreUserTable.update({ ...payload, firebaseUID: fbId }, {
                where: {
                    id: user.dataValues.id
                },
                transaction: transaction,
                userId: payload.updatedById,
            });

            /* update address */
            await AddressTable.update({
                street: payload.address.street,
                postalCode: payload.address.postalCode,
                city: payload.address.city,
                state: payload.address.state,
                country: payload.address.country,
                status: ADDRESS_STATUS.ACTIVE,
                updatedById: payload.updatedById,
            }, {
                where: {
                    id: payload.address.id
                },
                transaction: transaction,
                userId: payload.updatedById,
            });


            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'phone') {
                    return HelperMethods.getErrorResponse('Contact number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }
}
