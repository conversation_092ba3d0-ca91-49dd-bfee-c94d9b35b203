import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../../../core/CoreInterfaces";
import { NormalUserTable } from "../database/NormalUserTable";
import { ICreateNormalUser, INormalUserResponse, IUpdateNormalUser, ResetPasswordPayload } from "../models/INormalUser";

export interface INormalUserRepo {
    create(payload: ICreateNormalUser, transaction: Transaction): Promise<APIBaseResponse<NormalUserTable | null>>;

    update(payload: IUpdateNormalUser, transaction: Transaction): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number, transaction: Transaction, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<INormalUserResponse> | null>>;

    getById(id: number, transaction?: Transaction): Promise<APIBaseResponse<INormalUserResponse | null>>;

    resetPassword(payload: ResetPasswordPayload, transaction: Transaction): Promise<APIBaseResponse<null>>
    
    getByFirebaseId(fbId: string, transaction: Transaction): Promise<APIBaseResponse<INormalUserResponse | null>>;

    updateWithFirebaseUID(payload: IUpdateNormalUser, fbId: string, transaction: Transaction): Promise<APIBaseResponse<null>>;

}