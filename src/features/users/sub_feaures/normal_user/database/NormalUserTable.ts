import { DataTypes, Model } from 'sequelize';
import { INormalUser } from '../models/INormalUser';
import { sequelizeInit } from '../../../../../sequelize_init';
import { RepoProvider } from '../../../../../core/RepoProvider';
import { CoreUserTable } from '../../../core/database/CoreUserTable';
import { AddressTable } from '../../../../address/database/AddressTable';


class NormalUserTable extends Model<INormalUser, Omit<INormalUser, "id">> {
    declare coreUser: CoreUserTable;
    declare address: AddressTable;
}

NormalUserTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                return Number(this.dataValues.id);
            }
        },
        coreUserId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                return Number(this.dataValues.coreUserId);
            }
        },
        addressId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                return Number(this.dataValues.addressId);
            }
        },
    },
    {
        sequelize: sequelizeInit,
        tableName: 'normal_users',
        timestamps: false,
    },
);




export { NormalUserTable };