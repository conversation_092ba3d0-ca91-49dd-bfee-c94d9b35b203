import { InterfaceMetaData } from "../../../../../core/CoreInterfaces";
import { USER_ROLE_STATUS } from "./UserRolesMisc";


interface ICreateUserRole {
    role: string;
    status: USER_ROLE_STATUS;
    createdById: number;
}

interface ICreateUserRoleWithPermissions extends ICreateUserRole {
    permissions: string[];
}

interface IUpdateUserRole extends Omit<ICreateUserRole, "createdById"> {
    id: number;
    updatedById: number;
}

interface IUpdateUserRoleWithPermissions extends IUpdateUserRole {
    permissions: string[];
}

interface IUserRole extends I<PERSON>reateUserRole, InterfaceMetaData {
}

interface IUserRoleResponse {
    id: number;
    role: string;
    status: USER_ROLE_STATUS;
    permissions: string[];
}

export { IUserRole, ICreateUserRole, IUpdateUserRole, ICreateUserRoleWithPermissions, IUpdateUserRoleWithPermissions, IUserRoleResponse };