import * as express from "express";
import { UserRoleController } from "../controller/UserRoleController";
import { UserRoleValidations } from "../validations/CoreUserValidations";
import { permissionsMiddleware } from "../../../../../middlewares/permissionsMiddleware";
import { AppPermissions } from "../../user_permissions/AppPermissions";

const apiInitialPath = "/user-roles";
const userRoleRouter = express.Router();

userRoleRouter.post(apiInitialPath + "/create",
    permissionsMiddleware(AppPermissions.USER_ROLE.CREATE),
    UserRoleValidations.validateCreate, UserRoleController.create);

userRoleRouter.put(apiInitialPath + "/update/:id",
    permissionsMiddleware(AppPermissions.USER_ROLE.UPDATE),
    UserRoleValidations.validateUpdate, UserRoleController.update);

userRoleRouter.get(apiInitialPath + "/",

    permissionsMiddleware(AppPermissions.USER_ROLE.READ),
    UserRoleValidations.validateGetAll, UserRoleController.getAll);

userRoleRouter.get(apiInitialPath + "/:id",
    UserRoleValidations.validateGetById, UserRoleController.getById);

export { userRoleRouter };