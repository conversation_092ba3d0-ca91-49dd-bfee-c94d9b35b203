import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../../../core/CoreInterfaces";
import { UserRoleTable } from "../database/UserRoleTable";
import { ICreateUserRole, ICreateUserRoleWithPermissions, IUpdateUserRole, IUpdateUserRoleWithPermissions, IUserRoleResponse } from "../models/IUserRole";

export interface IUserRoleRepo {
    create(payload: ICreateUserRole,transaction:Transaction): Promise<APIBaseResponse<UserRoleTable | null>>;

    update(payload: IUpdateUserRole,transaction:Transaction): Promise<APIBaseResponse<null>>;

    createWithPermissions(payload: ICreateUserRoleWithPermissions,transaction:Transaction): Promise<APIBaseResponse<UserRoleTable | null>>;

    updateWithPermissions(payload: IUpdateUserRoleWithPermissions,transaction:Transaction): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number, transaction:Transaction,text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<IUserRoleResponse> | null>>;

    getById(id: number,transaction:Transaction): Promise<APIBaseResponse<IUserRoleResponse | null>>;
}