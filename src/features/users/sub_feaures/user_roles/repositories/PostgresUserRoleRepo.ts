import { Op, Transaction, UniqueConstraintError, WhereOptions } from "sequelize";
import { IUserRoleRepo } from "./IUserRoleRepo";
import { ICreateUserRole, ICreateUserRoleWithPermissions, IUpdateUserRole, IUpdate<PERSON>serRoleWithPermissions, IUserRole, IUserRoleResponse } from "../models/IUserRole";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../../../core/CoreInterfaces";
import { UserRoleTable } from "../database/UserRoleTable";
import { HelperMethods } from "../../../../../core/HelperMethods";
import { USER_ROLE_STATUS } from "../models/UserRolesMisc";
import { sequelizeInit } from "../../../../../sequelize_init";
import { RolePermissionsTable } from "../../user_permissions/database/RolePermissionsTable";
import { USER_ROLES } from "../../../core/models/UserMisc";
import { AppPermissions } from "../../user_permissions/AppPermissions";

export class PostgresUserRoleRepo implements IUserRoleRepo {

    async createWithPermissions(payload: ICreateUserRoleWithPermissions, transaction: Transaction): Promise<APIBaseResponse<UserRoleTable | null>> {
        try {
            payload.role = payload.role.toLowerCase().trim();

            const result = await UserRoleTable.create(payload, {
                userId: payload.createdById,
                transaction,
            });

            await RolePermissionsTable.create({
                roleId: result.dataValues.id,
                permissions: payload.permissions,
                createdById: payload.createdById,
            }, {
                userId: payload.createdById,
                transaction,
            });

            return HelperMethods.getSuccessResponse(result);

        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'role') {
                    return HelperMethods.getErrorResponse('Role already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }
    async updateWithPermissions(payload: IUpdateUserRoleWithPermissions, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {


            payload.role = payload.role.toLowerCase().trim();

            await UserRoleTable.update(payload, {
                userId: payload.updatedById,
                transaction,
                where: {
                    id: payload.id
                },
            });
            await RolePermissionsTable.update({
                permissions: payload.permissions,
                updatedById: payload.updatedById,
            }, {
                where: {
                    roleId: payload.id
                },
                userId: payload.updatedById,
                transaction,

            });

            return HelperMethods.getSuccessResponse(null);

        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'role') {
                    return HelperMethods.getErrorResponse('Role already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async create(payload: ICreateUserRole, transaction: Transaction): Promise<APIBaseResponse<UserRoleTable | null>> {
        try {
            const result = await UserRoleTable.create(payload, {
                userId: payload.createdById,transaction
            });
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'role') {
                    return HelperMethods.getErrorResponse('Role already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(payload: IUpdateUserRole, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            const deletionUpdates = {
                deletedAt: new Date(),
                deletedById: payload.updatedById,
            };
            if (payload.status === USER_ROLE_STATUS.DELETED) {
                Object.assign(payload, deletionUpdates);
            }

            await UserRoleTable.update(payload, {
                where: {
                    id: payload.id
                },
                userId: payload.updatedById,
                individualHooks: true,
                transaction
            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'role') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number, transaction: Transaction,text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<IUserRoleResponse> | null>> {
        try {

            const whereConditions: WhereOptions<IUserRole> = {
                status: USER_ROLE_STATUS.ACTIVE,
                role: {
                    /* not admin*/
                    [Op.not]: 'admin'
                }
            };

            if (text) {
                whereConditions.role = {
                    [Op.iLike]: `%${text}%`
                };
            }

            const offset = (page - 1) * pageSize;
            const { count, rows } = await UserRoleTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: whereConditions,
                include: {
                    model: RolePermissionsTable,
                    as: 'permissions',
                },
                transaction
            });

            const totalPages = Math.ceil(count / pageSize);

            const data = rows.map(row => {
                return {
                    id: Number(row.dataValues.id),
                    role: row.dataValues.role,
                    status: row.dataValues.status,
                    permissions: row.permissions.dataValues.permissions,
                }
            });

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number, transaction: Transaction): Promise<APIBaseResponse<IUserRoleResponse | null>> {
        try {
            const result = await UserRoleTable.findOne({

                where: {
                    id: id,
                    status: USER_ROLE_STATUS.ACTIVE,
                }
                ,
                include:{
                    model: RolePermissionsTable,
                    as: 'permissions',
                },
                transaction
            });

            if (!result) {
                return HelperMethods.getErrorResponse("User role not found");
            }


            const permissions: string[] = [];

            if (result.dataValues.role === "admin") {

                for (const feature in AppPermissions) {
                    const appFeature = Object.values(AppPermissions[feature as keyof typeof AppPermissions]);
                    for (const permission of appFeature) {
                        permissions.push(permission);
                    }
                }
            }
            else {

                permissions.push(...result.permissions.dataValues.permissions);
            }


            return HelperMethods.getSuccessResponse({
                id: Number(result.dataValues.id),
                role: result.dataValues.role,
                status: result.dataValues.status,
                permissions: permissions,
            });

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

}