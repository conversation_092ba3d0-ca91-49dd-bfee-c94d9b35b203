import { Request, Response, NextFunction } from "express";
import { UserRoleSchema } from "./UserRoleSchema";
import { HelperMethods } from "../../../../../core/HelperMethods";
import { CoreSchemas } from "../../../../../core/CoreSchemas";

export class UserRoleValidations {

    static validateCreate = (req: Request, res: Response, next: NextFunction) => {
        const result = UserRoleSchema.createSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {

        let result: any = CoreSchemas.updateByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        result = UserRoleSchema.updateSchema.safeParse(req.body);

        if (!result.success) {

            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }



    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        return next();
    }

    static validateGetById = (req: Request, res: Response, next: NextFunction) => {

        const result = CoreSchemas.getByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        return next();
    }
}