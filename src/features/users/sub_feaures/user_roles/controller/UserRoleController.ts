import { NextFunction, Request, Response } from "express";
import { get } from "lodash";
import { ICreateUserRoleWithPermissions, IUpdateUserRoleWithPermissions } from "../models/IUserRole";
import { USER_ROLE_STATUS } from "../models/UserRolesMisc";
import { RepoProvider } from "../../../../../core/RepoProvider";
import { HelperMethods } from "../../../../../core/HelperMethods";
import { sequelizeInit } from "../../../../../sequelize_init";

export class UserRoleController {

    static async create(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id");

            const requestBody = req.body as ICreateUserRoleWithPermissions;

            const payload: ICreateUserRoleWithPermissions = {
                role: requestBody.role,
                status: USER_ROLE_STATUS.ACTIVE,
                createdById: Number(userId!),
                permissions: requestBody.permissions,
            };

            const result = await RepoProvider.userRoleRepo.createWithPermissions(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async update(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id");
            const id = Number(get(req.params, "id"));
            const requestBody = req.body as IUpdateUserRoleWithPermissions;

            const payload: IUpdateUserRoleWithPermissions = {
                id: id,
                role: requestBody.role,
                status: requestBody.status,
                updatedById: Number(userId!),
                permissions: requestBody.permissions,
            };

            const result = await RepoProvider.userRoleRepo.updateWithPermissions(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }


    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
            const text = get(req.query, "text") as string | undefined;

            const result = await RepoProvider.userRoleRepo.getAll(page, pageSize, T, text);

            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.userRoleRepo.getById(Number(id), T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
}