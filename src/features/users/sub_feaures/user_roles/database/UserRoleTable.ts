import { DataTypes, Model } from 'sequelize';
import { ICreateUserRole, IUserRole } from '../models/IUserRole';
import { USER_ROLE_STATUS } from '../models/UserRolesMisc';
import { sequelizeInit } from '../../../../../sequelize_init';
import { RepoProvider } from '../../../../../core/RepoProvider';
import { RolePermissionsTable } from '../../user_permissions/database/RolePermissionsTable';


class UserRoleTable extends Model<IUserRole, ICreateUserRole> {
    declare permissions: RolePermissionsTable;
}

UserRoleTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                return Number(this.dataValues.id);
            }
        },
        role: {
            type: DataTypes.STRING,
            allowNull: false,
        },

        status: {
            type: DataTypes.ENUM(...Object.values(USER_ROLE_STATUS)),
            allowNull: false,
        },

        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'user_roles',
        timestamps: true,
        paranoid: true,
    },
);


UserRoleTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "user_roles",
        instance,
        options
    );
});

UserRoleTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "user_roles",
        instance,
        options
    );
});

UserRoleTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "user_roles",
        instance,
        options
    );
});


export { UserRoleTable };