import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../../core/CoreInterfaces";
import { CoreUserTable } from "../database/CoreUserTable";
import { ICreateCoreUser, IUpdateCoreUser } from "../models/ICoreUser";

export interface ICoreUserRepo {
    create(payload: ICreate<PERSON>oreUser,transaction:Transaction): Promise<APIBaseResponse<CoreUserTable | null>>;

    update(payload: IUpdateCoreUser,transaction:Transaction): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number,transaction:Transaction, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<CoreUserTable> | null>>;

    getById(id: Number,transaction:Transaction): Promise<APIBaseResponse<CoreUserTable | null>>;
}