import { APIBaseResponse, PaginatedBaseResponse } from "../../../../core/CoreInterfaces";
import { HelperMethods } from "../../../../core/HelperMethods";
import { USER_STAUS } from "../models/UserMisc";
import { Op, Transaction, UniqueConstraintError, WhereOptions } from "sequelize";
import * as admin from "firebase-admin";
import { ICoreUserRepo } from "./IUserRepo";
import { ICoreUser, ICreateCoreUser, IUpdateCoreUser } from "../models/ICoreUser";
import { CoreUserTable } from "../database/CoreUserTable";
import { USER_ROLE_STATUS } from "../../sub_feaures/user_roles/models/UserRolesMisc";
import { UserRoleTable } from "../../sub_feaures/user_roles/database/UserRoleTable";


export class PostgresCoreUserRepo implements ICoreUserRepo {


    async create(payload: ICreateCoreUser,transaction:Transaction): Promise<APIBaseResponse<CoreUserTable | null>> {

        let firebaseUserUid: string | null = null;

        try {

            /* check for valid role */
            const role = await UserRoleTable.findOne({
                where: {

                    id: payload.roleId,
                    status: USER_ROLE_STATUS.ACTIVE
                },
transaction
            });

            if (!role) {
                return HelperMethods.getErrorResponse("Invalid role");
            }


            /* create firebase user */
            const firebaseUser = await admin.auth().createUser({
                email: payload.email,
                password: payload.password,
            });

            firebaseUserUid = firebaseUser.uid;

            payload.firebaseUID = firebaseUserUid;

            const result = await CoreUserTable.create(payload, {
                userId: payload.createdById,
                transaction
            });

            return HelperMethods.getSuccessResponse(result);
        } catch (error) {

            if (firebaseUserUid) {
                await admin.auth().deleteUser(firebaseUserUid);
            }
            HelperMethods.handleError(error);

            if ((error as any)?.errorInfo?.code === "auth/email-already-exists") {
                return HelperMethods.getErrorResponse('Email already exists');
            }
            else if ((error as any)?.errorInfo?.code === "auth/invalid-email") {
                return HelperMethods.getErrorResponse('Invalid email');
            }
            else if (error instanceof UniqueConstraintError) {

                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'mobile') {
                    return HelperMethods.getErrorResponse('Mobile number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(payload: IUpdateCoreUser,transaction:Transaction): Promise<APIBaseResponse<null>> {
        try {

            await CoreUserTable.update(payload, {
                where: {
                    id: payload.id
                },
                userId: payload.updatedById,
                individualHooks: true,transaction
            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                if (error.errors[0].path === 'email') {
                    return HelperMethods.getErrorResponse('Email already exists');
                }
                else if (error.errors[0].path === 'phone') {
                    return HelperMethods.getErrorResponse('Contact number already exists');
                }
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number,transaction:Transaction, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<CoreUserTable> | null>> {
        try {

            const whereConditions: WhereOptions<ICoreUser> = {
                status: USER_STAUS.ACTIVE
            };

            if (text) {
                whereConditions.firstName = {
                    [Op.iLike]: `%${text}%`
                };
                whereConditions.lastName = {
                    [Op.iLike]: `%${text}%`
                };
                whereConditions.email = {
                    [Op.iLike]: `%${text}%`
                };
                whereConditions.mobile = {
                    [Op.iLike]: `%${text}%`
                };
            }

            const offset = (page - 1) * pageSize;
            const { count, rows } = await CoreUserTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: whereConditions,transaction
            });

            const totalPages = Math.ceil(count / pageSize);

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number,transaction:Transaction): Promise<APIBaseResponse<CoreUserTable | null>> {
        try {
            const result = await CoreUserTable.findByPk(id,{transaction});

            if (!result) {
                return HelperMethods.getErrorResponse("User not found");
            }
            return HelperMethods.getSuccessResponse(result);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

}