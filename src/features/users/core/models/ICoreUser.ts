import { AuditData, AuditMetaKeys, InterfaceMetaData } from "../../../../core/CoreInterfaces";
import { IAddress } from "../../../address/models/IAddress";
import { USER_ROLES, USER_STAUS } from "./UserMisc";


interface ICreateCoreUser {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    mobile: string;
    roleId: number;
    status: USER_STAUS;
    firebaseUID: string;
    createdById: number;
}

interface IUpdateCoreUser extends Omit<ICreateCoreUser, "createdById" | "password" | "firebaseUID"> {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
    roleId: number;
    updatedById: number;
}

interface ICoreUser extends Omit<ICreateCoreUser, "password">, InterfaceMetaData {
    status: USER_STAUS;
}



interface ICoreUserResponse extends Omit<ICoreUser, AuditMetaKeys>, AuditData {
    status: USER_STAUS;
    address: IAddress;
    role: string;
}

export { <PERSON><PERSON>ore<PERSON>ser, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IUpdate<PERSON>ore<PERSON>ser, ICoreUserResponse };