import { z } from "zod";
import { USER_ROLES, USER_STAUS } from "../models/UserMisc";

export class CoreUserSchema {
    static createSchema =
        z.object({
            firstName: z.string().min(3, "firstName must be at least 3 characters").max(255, "firstName must be upto 255 characters"),
            lastName: z.string().min(3, "lastName must be at least 3 characters").max(255, "lastName must be upto 255 characters"),
            email: z.string().email("email is invalid"),
            password: z.string().min(6, "Password must be atleast 6 characters"),
            mobile: z.string().length(10, "mobile must be 10 characters"),
            roleId: z.number({
                errorMap: () => ({
                    message: "RoleId is required",
                })
            }).int().positive("RoleId must be a positive number"),
        });

    static updateSchema =
        CoreUserSchema.createSchema.extend({
            status: z.nativeEnum(USER_STAUS).refine(val => Object.values(USER_STAUS).includes(val), {
                message: "Invalid status",
            }),
        });

}