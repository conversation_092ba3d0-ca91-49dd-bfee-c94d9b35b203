import { NextFunction, Request, Response } from "express";
import { get } from "lodash";
import { ICreate<PERSON>oreUser, IUpdateCoreUser } from "../models/ICoreUser";
import { RepoProvider } from "../../../../core/RepoProvider";
import { USER_STAUS } from "../models/UserMisc";
import { HelperMethods } from "../../../../core/HelperMethods";
import { sequelizeInit } from "../../../../sequelize_init";

export class CoreUserController {

    static async create(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id",);

            const requestBody = req.body as ICreateCoreUser;

            const payload: ICreateCoreUser = {
                firstName: requestBody.firstName,
                lastName: requestBody.lastName,
                email: requestBody.email,
                password: req.body.password,
                mobile: requestBody.mobile,
                roleId: requestBody.roleId,
                firebaseUID: "",
                status: USER_STAUS.ACTIVE,
                createdById: Number(userId!),
            };

            const result = await RepoProvider.coreUserRepo.create(payload,T);
            if (!result.success) {
                res.status(500).send(result);
                return;
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async update(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const userId = get(req, "user_id",);
            const id = Number(get(req.params, "id"));
            const requestBody = req.body as IUpdateCoreUser;

            const payload: IUpdateCoreUser = {
                id: id,
                firstName: requestBody.firstName,
                lastName: requestBody.lastName,
                mobile: requestBody.mobile,
                updatedById: Number(userId!),
                roleId: requestBody.roleId,
                status: requestBody.status,
                email: requestBody.email,
            };

            const result = await RepoProvider.coreUserRepo.update(payload,T);
            if (!result.success) {
                res.status(500).send(result);
                return;
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
            const text = get(req.query, "text") as string | undefined;

            const result = await RepoProvider.coreUserRepo.getAll(page, pageSize,T, text);

            if (!result.success) {
                res.status(500).send(result);
                return;
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.coreUserRepo.getById(Number(id),T);
            if (!result.success) {
                res.status(500).send(result);
                return;
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getByFirebaseToken(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id",);
            const result = await RepoProvider.coreUserRepo.getById(Number(userId!),T);
            if (!result.success) {
                res.status(500).send(result);
                return;
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
}