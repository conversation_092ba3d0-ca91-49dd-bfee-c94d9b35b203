import { DataTypes, Model } from 'sequelize';
import { USER_ROLES, USER_STAUS } from '../models/UserMisc';
import { ICoreUser, ICreateCoreUser } from '../models/ICoreUser';
import { sequelizeInit } from '../../../../sequelize_init';
import { RepoProvider } from '../../../../core/RepoProvider';
import { UserRoleTable } from '../../sub_feaures/user_roles/database/UserRoleTable';
import { NormalUserTable } from '../../sub_feaures/normal_user/database/NormalUserTable';


class CoreUserTable extends Model<ICoreUser, ICreateCoreUser> {
    declare role: UserRoleTable;
    declare normalUser: NormalUserTable;
}

CoreUserTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                return Number(this.dataValues.id);
            }
        },
        firstName: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        lastName: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        email: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        mobile: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        firebaseUID: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM(...Object.values(USER_STAUS)),
            allowNull: false,
        },
        roleId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                return Number(this.dataValues.roleId);
            }
        },
        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'core_users',
        timestamps: true,
        paranoid: true,
    },
);


CoreUserTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "core_users",
        instance,
        options
    );
});

CoreUserTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "core_users",
        instance,
        options
    );
});

CoreUserTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "core_users",
        instance,
        options
    );
});


export { CoreUserTable };