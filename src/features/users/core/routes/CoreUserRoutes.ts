import * as express from "express";
import { CoreUserValidations } from "../validations/CoreUserValidations";
import { CoreUserController } from "../controller/CoreUserController";

const apiInitialPath = "/users";
const coreUserRouter = express.Router();

coreUserRouter.post(apiInitialPath + "/create", CoreUserValidations.validateCreate, CoreUserController.create);

coreUserRouter.put(apiInitialPath + "/update/:id", CoreUserValidations.validateUpdate, CoreUserController.update);

coreUserRouter.get(apiInitialPath + "/getByFirebaseToken", CoreUserController.getByFirebaseToken);

coreUserRouter.get(apiInitialPath + "/", CoreUserValidations.validateGetAll, CoreUserController.getAll);

coreUserRouter.get(apiInitialPath + "/:id", CoreUserValidations.validateGetById, CoreUserController.getById);

export { coreUserRouter };