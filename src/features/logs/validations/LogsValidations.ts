import { Request, Response, NextFunction } from "express";
import { HelperMethods } from "../../../core/HelperMethods";
import { CoreSchemas } from "../../../core/CoreSchemas";
import { z } from "zod";

export class LogsValidations {

    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {

        const result = CoreSchemas.paginationSchema.safeParse(req.query);

        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }

    /**
     * Validate date query parameter for log endpoints
     */
    static validateDateQuery = (req: Request, res: Response, next: NextFunction) => {
        try {
            const schema = z.object({
                date: z.string()
                    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
                    .refine((date) => {
                        const parsedDate = new Date(date);
                        return !isNaN(parsedDate.getTime()) && parsedDate.toISOString().split('T')[0] === date;
                    }, "Invalid date")
            });

            const result = schema.safeParse(req.query);

            if (!result.success) {
                res.status(400).json({
                    success: false,
                    message: "Validation failed",
                    errors: result.error.errors.map(err => ({
                        field: err.path.join('.'),
                        message: err.message
                    })),
                    data: null
                });
                return;
            }

            next();
        } catch (error) {
            console.error('Validation error:', error);
            res.status(500).json({
                success: false,
                message: "Internal server error",
                data: null
            });
        }
    };

    /**
     * Validate date and pagination parameters for paginated logs by date
     */
    static validatePaginatedLogsByDate = (req: Request, res: Response, next: NextFunction) => {
        try {
            const schema = z.object({
                date: z.string()
                    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
                    .refine((date) => {
                        const parsedDate = new Date(date);
                        return !isNaN(parsedDate.getTime()) && parsedDate.toISOString().split('T')[0] === date;
                    }, "Invalid date"),
                page: z.string()
                    .optional()
                    .transform(val => val ? parseInt(val) : 1)
                    .refine(val => !isNaN(val) && val > 0, "Page must be a positive number"),
                pageSize: z.string()
                    .optional()
                    .transform(val => val ? parseInt(val) : 50)
                    .refine(val => !isNaN(val) && val > 0 && val <= 1000, "Page size must be between 1 and 1000"),
                method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']).optional(),
                statusCode: z.string()
                    .optional()
                    .transform(val => val ? parseInt(val) : undefined)
                    .refine(val => val === undefined || (val >= 100 && val <= 599), "Invalid status code"),
                hasError: z.string()
                    .optional()
                    .transform(val => val === 'true' ? true : val === 'false' ? false : undefined),
                url: z.string().min(1, "URL filter cannot be empty").optional(),
                ip: z.string().min(1, "IP filter cannot be empty").optional()
            });

            const result = schema.safeParse(req.query);

            if (!result.success) {
                res.status(400).json({
                    success: false,
                    message: "Validation failed",
                    errors: result.error.errors.map(err => ({
                        field: err.path.join('.'),
                        message: err.message
                    })),
                    data: null
                });
                return;
            }

            // Set validated values back to req.query for controller use
            Object.assign(req.query, {
                page: result.data.page.toString(),
                pageSize: result.data.pageSize.toString(),
                date: result.data.date,
                ...(result.data.method && { method: result.data.method }),
                ...(result.data.statusCode && { statusCode: result.data.statusCode.toString() }),
                ...(result.data.hasError !== undefined && { hasError: result.data.hasError.toString() }),
                ...(result.data.url && { url: result.data.url }),
                ...(result.data.ip && { ip: result.data.ip })
            });

            next();
        } catch (error) {
            console.error('Validation error:', error);
            res.status(500).json({
                success: false,
                message: "Internal server error",
                data: null
            });
        }
    };

    /**
     * Helper method to validate and return parsed data for paginated logs by date
     */
    static parsePaginatedLogsByDateQuery = (req: Request) => {
        const schema = z.object({
            date: z.string()
                .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
                .refine((date) => {
                    const parsedDate = new Date(date);
                    return !isNaN(parsedDate.getTime()) && parsedDate.toISOString().split('T')[0] === date;
                }, "Invalid date"),
            page: z.string()
                .optional()
                .transform(val => val ? parseInt(val) : 1)
                .refine(val => !isNaN(val) && val > 0, "Page must be a positive number"),
            pageSize: z.string()
                .optional()
                .transform(val => val ? parseInt(val) : 50)
                .refine(val => !isNaN(val) && val > 0 && val <= 1000, "Page size must be between 1 and 1000"),
            method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']).optional(),
            statusCode: z.string()
                .optional()
                .transform(val => val ? parseInt(val) : undefined)
                .refine(val => val === undefined || (val >= 100 && val <= 599), "Invalid status code"),
            hasError: z.string()
                .optional()
                .transform(val => val === 'true' ? true : val === 'false' ? false : undefined),
            url: z.string().min(1, "URL filter cannot be empty").optional(),
            ip: z.string().min(1, "IP filter cannot be empty").optional()
        });

        const result = schema.safeParse(req.query);

        if (!result.success) {
            throw new Error(`Validation failed: ${result.error.errors.map(err => err.message).join(', ')}`);
        }

        return result.data;
    };

    /**
     * Validate optional date query parameter
     */
    static validateOptionalDateQuery = (req: Request, res: Response, next: NextFunction) => {
        try {
            const schema = z.object({
                date: z.string()
                    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
                    .refine((date) => {
                        const parsedDate = new Date(date);
                        return !isNaN(parsedDate.getTime()) && parsedDate.toISOString().split('T')[0] === date;
                    }, "Invalid date")
                    .optional()
            });

            const result = schema.safeParse(req.query);

            if (!result.success) {
                res.status(400).json({
                    success: false,
                    message: "Validation failed",
                    errors: result.error.errors.map(err => ({
                        field: err.path.join('.'),
                        message: err.message
                    })),
                    data: null
                });
                return;
            }

            next();
        } catch (error) {
            console.error('Validation error:', error);
            res.status(500).json({
                success: false,
                message: "Internal server error",
                data: null
            });
        }
    };

    /**
     * Validate log filtering parameters
     */
    static validateLogFilters = (req: Request, res: Response, next: NextFunction) => {
        try {
            const schema = z.object({
                date: z.string()
                    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
                    .refine((date) => {
                        const parsedDate = new Date(date);
                        return !isNaN(parsedDate.getTime()) && parsedDate.toISOString().split('T')[0] === date;
                    }, "Invalid date"),
                method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']).optional(),
                statusCode: z.string().transform(val => parseInt(val)).refine(val => val >= 100 && val <= 599, "Invalid status code").optional(),
                hasError: z.string().transform(val => val === 'true').optional(),
                url: z.string().min(1, "URL filter cannot be empty").optional(),
                page: z.string().transform(val => parseInt(val)).refine(val => val > 0, "Page must be positive").optional(),
                pageSize: z.string().transform(val => parseInt(val)).refine(val => val > 0 && val <= 1000, "Page size must be between 1 and 1000").optional()
            });

            const result = schema.safeParse(req.query);

            if (!result.success) {
                res.status(400).json({
                    success: false,
                    message: "Validation failed",
                    errors: result.error.errors.map(err => ({
                        field: err.path.join('.'),
                        message: err.message
                    })),
                    data: null
                });
                return;
            }

            // Set defaults
            req.query.page = req.query.page || '1';
            req.query.pageSize = req.query.pageSize || '100';

            next();
        } catch (error) {
            console.error('Validation error:', error);
            res.status(500).json({
                success: false,
                message: "Internal server error",
                data: null
            });
        }
    };
}