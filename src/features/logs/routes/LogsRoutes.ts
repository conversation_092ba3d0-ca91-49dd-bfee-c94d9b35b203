import * as express from "express";
import { Request, Response, NextFunction } from "express";
import { LogsValidations } from "../validations/LogsValidations";
import { LogsController } from "../controller/LogsController";
import { permissionsMiddleware } from "../../../middlewares/permissionsMiddleware";
import { AppPermissions } from "../../users/sub_feaures/user_permissions/AppPermissions";
import { WorkingENV, APP_ENV } from "../../../sequelize_init";

const apiInitialPath = "/logs";
const logsRouter = express.Router();


logsRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.LOG.READ),
    LogsValidations.validateGetAll, LogsController.getAll);

// Get available log dates
logsRouter.get(apiInitialPath + "/dates", 
    permissionsMiddleware(AppPermissions.LOG.READ),  LogsController.getAvailableLogDates);

// Get logs for a specific date (paginated with filters)
logsRouter.get(apiInitialPath + "/by-date", 
    permissionsMiddleware(AppPermissions.LOG.READ),
    LogsValidations.validatePaginatedLogsByDate, 
    LogsController.getLogsByDatePaginated);

// Get all logs for a specific date (non-paginated - legacy endpoint)
logsRouter.get(apiInitialPath + "/by-date/all", 
    permissionsMiddleware(AppPermissions.LOG.READ),
    LogsValidations.validateDateQuery, 
    LogsController.getLogsByDate);

// Get log statistics for a specific date
logsRouter.get(apiInitialPath + "/stats-by-date", 
    permissionsMiddleware(AppPermissions.LOG.READ),
    LogsValidations.validateDateQuery, 
    LogsController.getLogStatsByDate);

// Get log file management statistics
logsRouter.get(apiInitialPath + "/file-stats", permissionsMiddleware(AppPermissions.LOG.READ),LogsController.getLogFileStats);

export { logsRouter };