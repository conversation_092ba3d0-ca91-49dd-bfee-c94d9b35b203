import fs from 'fs';
import path from 'path';
import { createReadStream } from 'fs';
import { createInterface } from 'readline';

export interface IMorganLog {
    timestamp: string;
    id: string;
    ip: string;
    request: {
        timestamp: string;
        method: string;
        url: string;
        headers: Record<string, any>;
        body: any;
    };
    response: {
        status: number;
        headers: Record<string, any>;
        body: any;
        responseTime: string;
    };
    error: boolean | any;
}

class MorganLogParser {
    /**
     * Parse morgan log file and return array of log entries
     * @param filePath - Path to the log file
     * @returns Array of parsed log entries
     */
    static parseLog(filePath: string): IMorganLog[] {
        try {
            if (!fs.existsSync(filePath)) {
                throw new Error(`Log file not found: ${filePath}`);
            }

            // Check file size - if larger than 100MB, use streaming approach
            const stats = fs.statSync(filePath);
            const fileSizeInMB = stats.size / (1024 * 1024);
            
            if (fileSizeInMB > 100) {
                console.log(`Large file detected (${fileSizeInMB.toFixed(2)}MB), using streaming approach`);
                // For large files, return empty array and suggest using streaming method
                return [];
            }

            const content = fs.readFileSync(filePath, 'utf-8');
            return this.parseLogContent(content);
        } catch (error) {
            console.error('Error parsing log file:', error);
            return [];
        }
    }

    /**
     * Parse log content from string (JSON lines format)
     * @param content - Log file content as string
     * @returns Array of parsed log entries
     */
    static parseLogContent(content: string): IMorganLog[] {
        const logs: IMorganLog[] = [];
        
        // Split by lines and parse each JSON line
        const lines = content.split('\n').filter(line => line.trim());
        
        for (const line of lines) {
            const logEntry = this.parseLogLine(line.trim());
            if (logEntry) {
                logs.push(logEntry);
            }
        }
        
        return logs;
    }

    /**
     * Parse a single JSON log line
     * @param line - Single JSON log line
     * @returns Parsed log entry or null
     */
    static parseLogLine(line: string): IMorganLog | null {
        try {
            if (!line.trim()) return null;
            
            // Parse JSON line
            const logEntry = JSON.parse(line);
            
            // Validate required fields
            if (!logEntry.timestamp || !logEntry.id || !logEntry.ip || !logEntry.request || !logEntry.response) {
                return null;
            }
            
            return {
                timestamp: logEntry.timestamp,
                id: logEntry.id,
                ip: logEntry.ip,
                request: {
                    timestamp: logEntry.request.timestamp || logEntry.timestamp,
                    method: logEntry.request.method || '',
                    url: logEntry.request.url || '',
                    headers: logEntry.request.headers || {},
                    body: logEntry.request.body || null
                },
                response: {
                    status: logEntry.response.status || 0,
                    headers: logEntry.response.headers || {},
                    body: logEntry.response.body || null,
                    responseTime: logEntry.response.responseTime || '0ms'
                },
                error: logEntry.error || false
            };
        } catch (error) {
            console.log('Failed to parse JSON line:', line.substring(0, 100) + '...');
            return null;
        }
    }

    /**
     * Parse log file using streaming for large files
     * @param filePath - Path to the log file
     * @returns Promise that resolves to array of parsed log entries
     */
    static async parseLogStream(filePath: string): Promise<IMorganLog[]> {
        return new Promise((resolve, reject) => {
            const logs: IMorganLog[] = [];
            
            const fileStream = createReadStream(filePath);
            const rl = createInterface({
                input: fileStream,
                crlfDelay: Infinity
            });
            
            rl.on('line', (line) => {
                const logEntry = this.parseLogLine(line);
                if (logEntry) {
                    logs.push(logEntry);
                }
            });
            
            rl.on('close', () => {
                resolve(logs);
            });
            
            rl.on('error', (error) => {
                reject(error);
            });
        });
    }

    /**
     * Parse log file using streaming with pagination and filtering
     * @param filePath - Path to the log file
     * @param page - Page number (1-based)
     * @param pageSize - Number of entries per page
     * @param filters - Optional filters
     * @returns Promise that resolves to paginated and filtered log entries
     */
    static async parseLogStreamWithFilters(
        filePath: string, 
        page: number = 1, 
        pageSize: number = 50,
        filters: {
            method?: string;
            statusCode?: number;
            hasError?: boolean;
            url?: string;
            ip?: string;
        } = {}
    ): Promise<{
        logs: IMorganLog[];
        pagination: {
            currentPage: number;
            totalPages: number;
            totalData: number;
            pageSize: number;
            hasNextPage: boolean;
            hasPreviousPage: boolean;
        };
    }> {
        return new Promise((resolve, reject) => {
            const allLogs: IMorganLog[] = [];
            
            const fileStream = createReadStream(filePath);
            const rl = createInterface({
                input: fileStream,
                crlfDelay: Infinity
            });
            
            rl.on('line', (line) => {
                const logEntry = this.parseLogLine(line);
                if (logEntry && this.matchesFilters(logEntry, filters)) {
                    allLogs.push(logEntry);
                }
            });
            
            rl.on('close', () => {
                // Sort by timestamp (newest first)
                allLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
                
                // Apply pagination
                const startIndex = (page - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                const paginatedLogs = allLogs.slice(startIndex, endIndex);
                
                const totalPages = Math.ceil(allLogs.length / pageSize);
                
                resolve({
                    logs: paginatedLogs,
                    pagination: {
                        currentPage: page,
                        totalPages: totalPages,
                        totalData: allLogs.length,
                        pageSize: pageSize,
                        hasNextPage: page < totalPages,
                        hasPreviousPage: page > 1
                    }
                });
            });
            
            rl.on('error', (error) => {
                reject(error);
            });
        });
    }

    /**
     * Check if log entry matches the given filters
     * @param logEntry - Log entry to check
     * @param filters - Filters to apply
     * @returns True if log entry matches all filters
     */
    static matchesFilters(logEntry: IMorganLog, filters: any): boolean {
        if (filters.method && logEntry.request.method !== filters.method) {
            return false;
        }
        
        if (filters.statusCode && logEntry.response.status !== filters.statusCode) {
            return false;
        }
        
        if (filters.hasError !== undefined) {
            const hasError = logEntry.error !== false;
            if (filters.hasError !== hasError) {
                return false;
            }
        }
        
        if (filters.url && !logEntry.request.url.includes(filters.url)) {
            return false;
        }
        
        if (filters.ip && !logEntry.ip.includes(filters.ip)) {
            return false;
        }
        
        return true;
    }
}

export { MorganLogParser };