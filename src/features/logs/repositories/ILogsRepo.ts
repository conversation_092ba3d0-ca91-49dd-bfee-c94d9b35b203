import { Model } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { LogsTable } from "../database/LogsTable";

export interface ILogRepo {
    getAllLogs(
        page: number,
        pageSize: number
    ): Promise<APIBaseResponse<PaginatedBaseResponse<LogsTable> | null>>;

    logModelAction<T extends Model<T, any>>(
        action: string,
        modelName: string,
        instance: any,
        options: any
    ): Promise<void>;

    resetPasswordLog  (
        action: string,
        modelName: string,
        instance: any,
        userId: Number,
    ): Promise<APIBaseResponse<null>>;

    // getLogsByModelNameAndId(modelName: string, recordId: number): Promise<APIBaseResponse<PaginatedBaseResponse<LogsTable> | null>>;
}
