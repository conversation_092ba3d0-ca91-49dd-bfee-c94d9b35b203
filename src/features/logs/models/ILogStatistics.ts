export interface ILogStatisticsResponse {
    date: string;
    totalRequests: number;
    requestsByMethod: Record<string, number>; // e.g., {"GET": 150, "POST": 25}
    requestsByStatus: Record<string, number>; // e.g., {"200": 120, "404": 5, "500": 2}
    errorCount: number;
    averageResponseTime: number; // in milliseconds (not string)
    slowestRequests: Array<{
        url: string;
        method: string;
        responseTime: number;
        timestamp: string;
    }>;
    topIPs: Array<{
        ip: string;
        count: number;
    }>;
    errorDistribution: Record<string, number>; // Error types and their counts
    timeRange: {
        start: string; // ISO 8601 timestamp
        end: string; // ISO 8601 timestamp
    };
}
 