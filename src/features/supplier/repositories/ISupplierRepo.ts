import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { SupplierTable } from "../database/SupplierTable";
import { ISupplierRequest, ISupplierResponse, ISupplierUpdateRequest } from "../models/ISupplier";

export interface ISupplierRepo {
    create(supplier: ISupplierRequest,transaction:Transaction): Promise<APIBaseResponse<SupplierTable | null>>;

    update(id: number, supplier: ISupplierUpdateRequest,transaction:Transaction): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<SupplierTable> | null>>;

    searchByText(text: string,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<SupplierTable> | null>>;

    getById(id: number,transaction:Transaction): Promise<APIBaseResponse<ISupplierResponse | null>>;

    delete(ids: number[], deletedById: number,transaction:Transaction): Promise<APIBaseResponse<null>>;

    search(text: string,transaction:Transaction): Promise<APIBaseResponse<SupplierTable[] | null>>;

}