import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreateSupplier, ISupplier, } from '../models/ISupplier';
import { SUPPLIER_STAUS } from '../models/SupplierMisc';
import { AddressTable } from '../../address/database/AddressTable';
import { RepoProvider } from '../../../core/RepoProvider';


class SupplierTable extends Model<ISupplier, ICreateSupplier> {

    declare address: AddressTable;
}

SupplierTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        email: {
            type: DataTypes.STRING,
            allowNull: true,
            unique: true,
        },
        phone: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
        },
        gst: {
            type: DataTypes.STRING,
            allowNull: true,
            unique: true,
        },
        pan: {
            type: DataTypes.STRING,
            allowNull: true,
            unique: true,
        },
        addressId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.addressId;
                if (value) {
                    return Number(value);
                }
            },
        },

        status: {
            type: DataTypes.ENUM(...Object.values(SUPPLIER_STAUS)),
            allowNull: false,
        },

        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'suppliers',
        timestamps: true,
        paranoid: true,

    },
);


SupplierTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "Supplier",
        instance,
        options
    );
});

SupplierTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "Supplier",
        instance,
        options
    );
});

SupplierTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "Supplier",
        instance,
        options
    );
});


export { SupplierTable };