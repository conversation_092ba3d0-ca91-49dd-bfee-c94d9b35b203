import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { HelperMethods } from "../../../core/HelperMethods";
import { get, pick } from "lodash";
import { SUPPLIER_STAUS } from "../models/SupplierMisc";
import { ICreateSupplier, ISupplier } from "../models/ISupplier";
import { sequelizeInit } from "../../../sequelize_init";

export class SupplierController {

    static async create(req: Request, res: Response, _next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const user_id = get(req, "user_id",);
            const payload: any = req.body;
            payload.id = null;
            payload.address.id = null;
            payload.name = payload.name.trim().toLowerCase();
            payload.status = SUPPLIER_STAUS.ACTIVE;
            payload.createdById = user_id;

            const result = await RepoProvider.supplierRepo.create(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async update(req: Request, res: Response, _next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const user_id = get(req, "user_id",);

            const id = Number(get(req.params, "id"));
            const payload = {
                ...req.body,
                updatedById: user_id,
            };
            payload.name = payload.name.trim().toLowerCase();

            const result = await RepoProvider.supplierRepo.update(id, payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async delete(req: Request, res: Response, _next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const userId = get(req, "user_id",);

            const ids: any = pick(req.body, "ids");

            const result = await RepoProvider.supplierRepo.delete(ids, Number(userId), T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not deleted"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getAll(req: Request, res: Response, _next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));

            const result = await RepoProvider.supplierRepo.getAll(page, pageSize, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }
    static async searchByText(req: Request, res: Response, _next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const text = String(get(req.query, "text"));

            const result = await RepoProvider.supplierRepo.searchByText(text, T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getById(req: Request, res: Response, _next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.supplierRepo.getById(Number(id), T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async search(req: Request, res: Response, _next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const text = get(req.query, "text") as string;
            const result = await RepoProvider.supplierRepo.search(text, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
}