import { z } from "zod";
import { AddressSchema } from "../../address/validations/AddressSchema";

export class SupplierSchema {
    static createSchema =
        z.object({
            name: z.string().min(3, "Name must be at least 3 characters long").max(80, "Name must be less than 80 characters long"),
            email: z.string().email("Invalid email").nullable(),
            phone: z.string().min(10, "Phone number must be at least 10 characters long").max(15, "Phone number must be less than 15 characters long"),
            gst: z.string().length(15, "GST number must be 15 characters long").nullable(),
            pan: z.string().length(10, "PAN number must be 10 characters long").nullable(),
            address: AddressSchema.createSchema,
        });

    static updateSchema =
        z.object({
            name: z.string().min(3, "Name must be at least 3 characters long").max(80, "Name must be less than 80 characters long"),
            email: z.string().email("Invalid email").nullable(),
            phone: z.string().min(10, "Phone number must be at least 10 characters long").max(15, "Phone number must be less than 15 characters long"),
            gst: z.string().length(15, "GST number must be 15 characters long").nullable(),
            pan: z.string().length(10, "PAN number must be 10 characters long").nullable(),
            addressId: z.number(),
            address: AddressSchema.createSchema,
        });

    static search =
        z.object({
            text: z.string().min(3, "Invalid request"),
        });

}