import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { IAddress, ICreateAddress } from "../../address/models/IAddress";
import { SUPPLIER_STAUS } from "./SupplierMisc";



interface ICreateSupplier {
    name: string;
    email: string | null;
    phone: string;
    addressId: number;
    gst: string | null;
    pan: string | null;
    status: SUPPLIER_STAUS;
    createdById: number;
}

interface ISupplierRequest {
    name: string;
    email: string | null;
    phone: string;
    address: ICreateAddress;
    gst: string | null;
    pan: string | null;
    createdById: number;
}

interface ISupplierUpdateRequest {
    name: string;
    email: string | null;
    phone: string;
    addressId: number;
    address: ICreateAddress;
    gst: string | null;
    pan: string | null;
    updatedById: number;
    status: SUPPLIER_STAUS;
}

interface ISupplier extends Omit<ICreateSupplier, "address">, InterfaceMetaData {
    status: SUPPLIER_STAUS;
    addressId: number;
}

interface ISupplierResponse extends ISupplier {
    address: IAddress;
}


export { ISupplier, ICreateSupplier, ISupplierResponse, ISupplierRequest, ISupplierUpdateRequest };