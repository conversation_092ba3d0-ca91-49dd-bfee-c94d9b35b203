import * as express from "express";
import { SupplierValidations } from "../validations/SupplierValidations";
import { SupplierController } from "../controller/SupplierController";
import { permissionsMiddleware } from "../../../middlewares/permissionsMiddleware";
import { AppPermissions } from "../../users/sub_feaures/user_permissions/AppPermissions";

const apiInitialPath = "/suppliers";
const supplierRouter = express.Router();

supplierRouter.post(apiInitialPath + "/create",
    permissionsMiddleware(AppPermissions.SUPPLIER.CREATE),
    SupplierValidations.validateCreate, SupplierController.create);

supplierRouter.put(apiInitialPath + "/update/:id",
    permissionsMiddleware(AppPermissions.SUPPLIER.UPDATE),
    SupplierValidations.validateUpdate, SupplierController.update);

supplierRouter.delete(apiInitialPath + "/delete",
    permissionsMiddleware(AppPermissions.SUPPLIER.CREATE),

    SupplierValidations.validateDelete, SupplierController.delete);

supplierRouter.get(apiInitialPath + "/search",
    permissionsMiddleware(AppPermissions.SUPPLIER.READ),
    SupplierValidations.validateSearch, SupplierController.search);

supplierRouter.get(apiInitialPath + "/searchByText",
    permissionsMiddleware(AppPermissions.SUPPLIER.READ),
    SupplierValidations.validateSearchByText, SupplierController.searchByText);

supplierRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.SUPPLIER.READ),
    SupplierValidations.validateGetAll, SupplierController.getAll);

supplierRouter.get(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.SUPPLIER.READ),
    SupplierValidations.validateGetById, SupplierController.getById);

export { supplierRouter };