import { z } from "zod";

export class OpeningStockSchema {


    static createSchema =
        z.object({
            date: z.string().optional().refine(val => val != null && val != undefined, {
                message: "Invalid date",
            }),
            data: z.array(z.object({
                rawMaterialId: z.number().optional().refine(val => val && val > 0, {
                    message: "Invalid raw material id",
                }),
                quantity: z.number().optional().refine(val => val && val > 0, {
                    message: "Invalid quantity",
                }),
            }))
        });

    static updateSchema =
        z.object({
            data: z.array(z.object({
                id: z.number().optional().refine(val => val && val > 0, {
                    message: "Invalid id",
                }),
                rawMaterialId: z.number().optional().refine(val => val && val > 0, {
                    message: "Invalid raw material id",
                }),
                quantity: z.number().optional().refine(val => val && val > 0, {
                    message: "Invalid quantity",
                }),
            }))
        });

}