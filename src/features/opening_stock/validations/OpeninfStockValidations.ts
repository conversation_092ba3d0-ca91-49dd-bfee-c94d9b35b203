import { Request, Response, NextFunction } from "express";
import { OpeningStockSchema } from "./OpeningStockSchema";
import { HelperMethods } from "../../../core/HelperMethods";
import { CoreSchemas } from "../../../core/CoreSchemas";

export class OpeningStockValidations {

    static validateCreate = (req: Request, res: Response, next: NextFunction) => {
        const result = OpeningStockSchema.createSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {
        const result = OpeningStockSchema.updateSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        return next();
    }

}