import { InterfaceMetaData } from "../../../core/CoreInterfaces";

interface ICreateOpeningStock {
  rawMaterialId: number;
  quantity: number;
  date: Date;
}

interface IOpeningStockRequest {
  date: Date;
  data: {
    rawMaterialId: number;
    quantity: number;
  }[];
  createdById: number;

}

interface IUpdateOpeningStockRequest {
  data: {
    id: number;
    rawMaterialId: number;
    quantity: number;
  }[];
  updatedById: number;

}



interface IOpeningStock extends ICreateOpeningStock, InterfaceMetaData {
}

interface IOpeningStockResponse {
  id: number;
  rawMaterial: {
    id: number;
    name: string;
  };
  date: Date;
  quantity: number;
  createdBy: string;
  createdAt: Date;
}

export { ICreateOpeningStock, IOpeningStock, IOpeningStockRequest, IOpeningStockResponse, IUpdateOpeningStockRequest }
