import { NextFunction, Request, Response } from 'express'
import { RepoProvider } from '../../../core/RepoProvider'
import { get } from 'lodash'
import { IOpeningStockRequest, IUpdateOpeningStockRequest } from '../models/IOpeningStock'
import { HelperMethods } from '../../../core/HelperMethods'
import { sequelizeInit } from '../../../sequelize_init'

export class OpeningStockController {
  static async create(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const userId = get(req, 'user_id')

      const requestBody = req.body as IOpeningStockRequest;

      const payload: IOpeningStockRequest = {
        date: requestBody.date,
        data: requestBody.data,
        createdById: Number(userId!),
      };

      const result = await RepoProvider.openingStockRepo.create(payload, T)

      if (!result.success) {
        throw new Error(result.message)
      }
      await T.commit()
      res.status(200).send(result)
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }

  static async update(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const userId = get(req, 'user_id')

      const requestBody = req.body

      const payload: IUpdateOpeningStockRequest = {
        data: requestBody.data,
        updatedById: Number(userId!),
      };

      const result = await RepoProvider.openingStockRepo.update(payload, T)

      if (!result.success) {
        throw new Error(result.message)
      }
      await T.commit()
      res.status(200).send(result)
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }

  }

  static async getAll(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const page = Number(get(req.query, 'page'));
      const pageSize = Number(get(req.query, 'pageSize'));
      const text = get(req.query, 'text') as string | undefined;

      const startDate = (get(req.query, "startDate") === "undefined" ? undefined : get(req.query, "startDate") ?? undefined) as Date | undefined;
      const endDate = (get(req.query, "endDate") === "undefined" ? undefined : get(req.query, "endDate") ?? undefined) as Date | undefined;

      const result = await RepoProvider.openingStockRepo.getAll(page, pageSize, T, text, startDate, endDate)
      if (!result.success) {
        throw new Error(result.message)
      }
      await T.commit()
      res.status(200).send(result)
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }

  static async getById(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const id = Number(req.params.id)

      const result = await RepoProvider.openingStockRepo.getById(id, T)
      if (!result.success) {
        throw new Error(result.message)
      }
      await T.commit()
      res.status(200).send(result)
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }
}
