import * as express from 'express'
import { OpeningStockController } from '../controller/OpeningStockController'
import { permissionsMiddleware } from '../../../middlewares/permissionsMiddleware';
import { AppPermissions } from '../../users/sub_feaures/user_permissions/AppPermissions';
import { OpeningStockValidations } from '../validations/OpeninfStockValidations';

const apiInitialPath = '/opening-stocks';
const openingStockRouter = express.Router();

openingStockRouter.post(
  apiInitialPath + '/create',
  permissionsMiddleware(AppPermissions.STOCK_ADJUSTMENT.CREATE),
  OpeningStockValidations.validateCreate,
  OpeningStockController.create
);

openingStockRouter.put(
  apiInitialPath + '/update',
  permissionsMiddleware(AppPermissions.STOCK_ADJUSTMENT.UPDATE),
  OpeningStockValidations.validateUpdate,
  OpeningStockController.update
);

openingStockRouter.get(apiInitialPath + '/',
  permissionsMiddleware(AppPermissions.STOCK_ADJUSTMENT.READ),
  OpeningStockValidations.validateGetAll,
  OpeningStockController.getAll);

openingStockRouter.get(apiInitialPath + '/:id',
  permissionsMiddleware(AppPermissions.STOCK_ADJUSTMENT.READ),
  OpeningStockController.getById);

export { openingStockRouter }
