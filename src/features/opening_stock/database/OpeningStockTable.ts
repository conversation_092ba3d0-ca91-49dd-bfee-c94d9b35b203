import { DataTypes, Model } from 'sequelize'
import { sequelizeInit } from '../../../sequelize_init'
import { RepoProvider } from '../../../core/RepoProvider'
import { IOpeningStock, ICreateOpeningStock } from '../models/IOpeningStock'
import { RawMaterialVariationTable } from '../../raw_material/database/RawMaterialVariationTable';
import { CoreUserTable } from '../../users/core/database/CoreUserTable';

class OpeningStockTable extends Model<IOpeningStock, ICreateOpeningStock> {

  declare rawMaterial: RawMaterialVariationTable;
  declare createdBy: CoreUserTable;

}

OpeningStockTable.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      get() {
        const value = this.dataValues.id
        if (value) {
          return Number(value.toString())
        }
      },
    },
    rawMaterialId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      get() {
        const value = this.dataValues.rawMaterialId
        if (value) {
          return Number(value.toString())
        }
      },
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    createdById: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedById: {
      type: DataTypes.INTEGER,
    },
    updatedAt: {
      type: DataTypes.DATE,
    },
    deletedById: {
      type: DataTypes.INTEGER,
    },
    deletedAt: {
      type: DataTypes.DATE,
    },
  },
  {
    sequelize: sequelizeInit,
    tableName: 'opening_stocks',
    timestamps: true,
    paranoid: true,
  }
)

OpeningStockTable.addHook('afterCreate', async (instance, options) => {
  await RepoProvider.logRepo.logModelAction(
    'create',
    'opening_stocks',
    instance,
    options
  )
})

OpeningStockTable.addHook('afterUpdate', async (instance, options) => {
  await RepoProvider.logRepo.logModelAction(
    'update',
    'opening_stocks',
    instance,
    options
  )
})

OpeningStockTable.addHook('afterDestroy', async (instance, options) => {
  await RepoProvider.logRepo.logModelAction(
    'delete',
    'opening_stocks',
    instance,
    options
  )
})

export { OpeningStockTable }
