import { Transaction } from 'sequelize'
import {
  APIBaseResponse,
  PaginatedBaseResponse,
} from '../../../core/CoreInterfaces'
import { IOpeningStockRequest, IOpeningStockResponse, IUpdateOpeningStockRequest } from '../models/IOpeningStock'

export interface IOpeningStockRepo {
  create(
    payload: IOpeningStockRequest
  ,transaction:Transaction): Promise<APIBaseResponse<null>>

  update(
      payload: IUpdateOpeningStockRequest
  ,transaction:Transaction): Promise<APIBaseResponse<null>>

  getAll(
      page: number,
      pageSize: number,
      transaction:Transaction,
      text?: string,
      startDate?: Date,
      endDate?: Date): Promise<APIBaseResponse<PaginatedBaseResponse<IOpeningStockResponse> | null>>

  getById(id: number,transaction:Transaction): Promise<APIBaseResponse<IOpeningStockResponse | null>>

}
