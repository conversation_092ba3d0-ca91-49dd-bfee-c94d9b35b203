import { z } from "zod";
import { STORAGE_LOCATION_TYPE } from "../models/StorageLocationMisc";

export class StorageLocationSchema {
    static createSchema =
        z.object({
            name: z.string().min(3, "Name must be at least 3 characters long").max(50, "Name must be less than 50 characters long"),
            description: z.string().optional().nullable(),
            type: z.enum([STORAGE_LOCATION_TYPE.RACK, STORAGE_LOCATION_TYPE.SHELF,
            STORAGE_LOCATION_TYPE.FLOOR,

            ], {
                invalid_type_error: "Invalid type",
                required_error: "Type is required"
            }),
        });

    static searchByText =
        z.object({
            text: z.string().min(3, "Invalid request"),
        });

}