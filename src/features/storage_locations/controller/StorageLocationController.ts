import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { STORAGE_LOCATION_STAUS } from "../models/StorageLocationMisc";
import { ICreateStorageLocation } from "../models/IStorageLocation";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";

export class StorageLocationController {

    static async create(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id",);

            const payload = pick(req.body, ["name", "description", "type",]) as ICreateStorageLocation;
            payload.status = STORAGE_LOCATION_STAUS.ACTIVE;
            payload.createdById = Number(userId!);
            payload.name = payload.name.trim().toLowerCase();

            const result = await RepoProvider.storageLocationRepo.create(payload, T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async update(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = Number(get(req.params, "id"));

            const userId = get(req, "user_id",);
            const payload = {
                ...req.body,
                updatedById: userId,
            };

            const result = await RepoProvider.storageLocationRepo.update(id, payload, T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async delete(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const ids: any = pick(req.body, "ids");
            const userId = get(req, "user_id",);

            const result = await RepoProvider.storageLocationRepo.delete(ids, Number(userId), T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not deleted"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
            const result = await RepoProvider.storageLocationRepo.getAll(page, pageSize, T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async searchByText(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const text = String(get(req.query, "text"));
            const result = await RepoProvider.storageLocationRepo.searchByText(text, T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.storageLocationRepo.getById(Number(id), T);
            if (!result.success) {
             throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
}