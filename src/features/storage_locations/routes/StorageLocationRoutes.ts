import * as express from "express";
import { StorageLocationValidations } from "../validations/StorageLocationValidations";
import { StorageLocationController } from "../controller/StorageLocationController";
import { permissionsMiddleware } from "../../../middlewares/permissionsMiddleware";
import { AppPermissions } from "../../users/sub_feaures/user_permissions/AppPermissions";

const apiInitialPath = "/storage-locations";
const storageLocationRouter = express.Router();

storageLocationRouter.post(apiInitialPath + "/create",
    permissionsMiddleware(AppPermissions.STORAGE_LOCATION.CREATE),
    StorageLocationValidations.validateCreate, StorageLocationController.create);

storageLocationRouter.put(apiInitialPath + "/update/:id",
    permissionsMiddleware(AppPermissions.STORAGE_LOCATION.UPDATE),
    StorageLocationValidations.validateUpdate, StorageLocationController.update);

storageLocationRouter.delete(apiInitialPath + "/delete", StorageLocationValidations.validateDelete, StorageLocationController.delete);

storageLocationRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.STORAGE_LOCATION.READ),
    StorageLocationValidations.validateGetAll, StorageLocationController.getAll);

storageLocationRouter.get(apiInitialPath + "/searchByText",
    permissionsMiddleware(AppPermissions.STORAGE_LOCATION.READ),
    StorageLocationValidations.validateSearchByText, StorageLocationController.searchByText);

storageLocationRouter.get(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.STORAGE_LOCATION.READ),
    StorageLocationValidations.validateGetById, StorageLocationController.getById);

export { storageLocationRouter };