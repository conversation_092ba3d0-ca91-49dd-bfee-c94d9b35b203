import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreateStorageLocation, IStorageLocation } from '../models/IStorageLocation';
import { STORAGE_LOCATION_STAUS, STORAGE_LOCATION_TYPE } from '../models/StorageLocationMisc';
import { RepoProvider } from '../../../core/RepoProvider';


class StorageLocationTable extends Model<IStorageLocation, ICreateStorageLocation> { }

StorageLocationTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
        },
        description: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        type: {
            type: DataTypes.ENUM(...Object.values(STORAGE_LOCATION_TYPE)),
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM(...Object.values(STORAGE_LOCATION_STAUS)),
            allowNull: false,
        },

        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'storage_locations',
        timestamps: true,
        paranoid: true,
    },
);


StorageLocationTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "StorageLocation",
        instance,
        options
    );
});

StorageLocationTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "StorageLocation",
        instance,
        options
    );
});

StorageLocationTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "StorageLocation",
        instance,
        options
    );
});
export { StorageLocationTable };