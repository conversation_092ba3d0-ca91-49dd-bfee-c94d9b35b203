import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { STORAGE_LOCATION_STAUS, STORAGE_LOCATION_TYPE } from "./StorageLocationMisc";



interface ICreateStorageLocation {
    name: string;
    description: string;
    type: STORAGE_LOCATION_TYPE;
    status: STORAGE_LOCATION_STAUS,
    createdById: number;
}


interface IStorageLocation extends ICreateStorageLocation, InterfaceMetaData {

}


export { IStorageLocation, ICreateStorageLocation };