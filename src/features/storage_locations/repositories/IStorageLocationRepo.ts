import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { StorageLocationTable } from "../database/StorageLocationTable";
import { ICreateStorageLocation } from "../models/IStorageLocation";

export interface IStorageLocationRepo {
    create(vendor: ICreateStorageLocation,transaction:Transaction): Promise<APIBaseResponse<StorageLocationTable | null>>;

    update(id: number, Vendor: ICreateStorageLocation,transaction:Transaction): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<StorageLocationTable> | null>>;

    searchByText(text: String,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<StorageLocationTable> | null>>;

    getById(id: number,transaction:Transaction): Promise<APIBaseResponse<StorageLocationTable | null>>;

    delete(ids: number[], deletedById: number,transaction:Transaction): Promise<APIBaseResponse<null>>;
}