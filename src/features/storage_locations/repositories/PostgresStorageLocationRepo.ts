import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";
import { StorageLocationTable } from "../database/StorageLocationTable";
import { ICreateStorageLocation, } from "../models/IStorageLocation";
import { STORAGE_LOCATION_STAUS } from "../models/StorageLocationMisc";
import { IStorageLocationRepo } from "./IStorageLocationRepo";
import { Op, Transaction, UniqueConstraintError } from "sequelize";

export class PostgresStorageLocationRepo implements IStorageLocationRepo {
    delete(ids: number[], deletedById: number, transaction: Transaction): Promise<APIBaseResponse<null>> {
        throw new Error("Method not implemented.");
    }

    async create(vendor: ICreateStorageLocation, transaction: Transaction): Promise<APIBaseResponse<StorageLocationTable | null>> {
        try {
            const result = await StorageLocationTable.create(vendor, {
                userId: vendor.createdById,
                transaction
            });
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Name already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(id: number, storageLocation: ICreateStorageLocation, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            const deletionUpdates = {
                deletedAt: new Date(),
                //@ts-ignore
                deletedById: storageLocation.updatedById,
            };
            if (storageLocation.status === STORAGE_LOCATION_STAUS.DELETED) {
                Object.assign(storageLocation, deletionUpdates);
            }
            await StorageLocationTable.update(storageLocation, {
                where: {
                    id: id
                },
                userId: storageLocation.createdById,
                individualHooks: true,
                transaction
            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Name already exists');

            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<StorageLocationTable> | null>> {
        try {
            const offset = (page - 1) * pageSize;
            const { count, rows } = await StorageLocationTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: {
                    deletedAt: null,
                }, transaction
            });

            const totalPages = Math.ceil(count / pageSize);

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchByText(text: string, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<StorageLocationTable> | null>> {
        try {
            const { count, rows } = await StorageLocationTable.findAndCountAll({
                limit: 10,
                order: [['createdAt', 'DESC']],
                transaction,
                where: {
                    name: {
                        [Op.iLike]: `%${text}%`
                    },
                    status: STORAGE_LOCATION_STAUS.ACTIVE
                },
            });

            const totalPages = 1;

            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: count,
                totalPages: totalPages,
                data: rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number, transaction: Transaction): Promise<APIBaseResponse<StorageLocationTable | null>> {
        try {
            const result = await StorageLocationTable.findByPk(id, { transaction });
            if (!result) {
                return HelperMethods.getErrorResponse('Data not exists')
            }
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }


}