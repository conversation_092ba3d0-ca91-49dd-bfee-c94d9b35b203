import * as express from "express";
import TaxRateController from '../controllers/TaxRateController';
import { TaxRateValidation } from '../validations/TaxRateValidation';

const apiInitialPath = "/tax-rate";
const taxRouter = express.Router();

// taxRouter.post(apiInitialPath + "/create", TaxRateValidation.validateCreate, TaxRateController.create);
// taxRouter.get(apiInitialPath + "/list", TaxRateValidation.validateGetAll, TaxRateController.getAll);
// taxRouter.get(apiInitialPath + "/get/:id", TaxRateValidation.validateGetAll, TaxRateController.getById);
// taxRouter.put(apiInitialPath + "/update/:id", TaxRateValidation.validateUpdate, TaxRateController.update);
// taxRouter.delete(apiInitialPath + "/delete/:id", TaxRateValidation.validateUpdate, TaxRateController.delete);

taxRouter.post(apiInitialPath + "/create", TaxRateValidation.validateCreate, TaxRateController.create);
taxRouter.get(apiInitialPath + "/list", TaxRateValidation.validateGetAll, TaxRateController.getAll);
taxRouter.get(apiInitialPath + "/get/:id", TaxRateController.getById);
taxRouter.put(apiInitialPath + "/update/:id", TaxRateValidation.validateUpdate, TaxRateController.update);
taxRouter.delete(apiInitialPath + "/delete/:id", TaxRateValidation.validateDelete, TaxRateController.delete);

export { taxRouter };