import { Request, Response } from "express";
import { get } from "lodash";
import TaxRateRepo from "../repos/TaxRateRepo";
import { TaxRateModel } from "../models/TaxRateTable";
import { HelperMethods } from "../../../core/HelperMethods";
import { isString } from "../../../core/common-utils";
import { sequelizeInit } from "../../../sequelize_init";
import { ERROR_MESSAGE } from "../../../core/constants";

export default class TaxRateController {

    static async create(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const user_id = parseInt(get(req, 'user_id')!);

            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER);
            }

            const payload = { ...req.body, createdBy: user_id };
            const title = payload.title.trim();

            const isAlreadyExist = await TaxRateModel.findOne({
                where: {
                    title,
                },
                transaction
            });
            if (isAlreadyExist) throw new Error(`this(${title}) title already exists.`);

            const taxRate = await new TaxRateRepo().create(payload, user_id, transaction);
            if (!taxRate.success) {
                throw new Error(taxRate.message)
            }

            await transaction.commit();
            res.status(200).send(taxRate);
        } catch (error: any) {
            await transaction.rollback();
            HelperMethods.handleError(error);
            res.status(500).send(HelperMethods.getErrorResponse(error.message || ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    static async update(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {

            const id = parseInt(get(req.params, 'id'));
            if (!id || isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER);
            }

            const user_id = parseInt(get(req, 'user_id')!);
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER);
            }

            const payload = { ...req.body, updatedBy: user_id };
            const updatedTaxRate = await new TaxRateRepo().update(id, payload, transaction);

            if (!updatedTaxRate.success) throw new Error(updatedTaxRate.message)

            await transaction.commit();
            res.status(200).send(updatedTaxRate);
        } catch (error: any) {
            await transaction.rollback();
            HelperMethods.handleError(error);
            res.status(500).send(HelperMethods.getErrorResponse(error.message || ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    static async getById(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {

            const id = parseInt(get(req.params, 'id'));
            if (!id || isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER);
            }
            const taxRateData = await new TaxRateRepo().getById(id, transaction);

            if (!taxRateData.success) throw new Error(taxRateData.message)


            await transaction.commit();
            res.status(200).send(taxRateData);
        } catch (error) {
            await transaction.rollback();
            res.status(500).send(
                HelperMethods.getErrorResponse(
                    (error instanceof Error ? error.message : "An error occurred while fetching final goods."),
                )
            )
        }
    }

    static async getAll(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {

            const size = !isNaN(Number(get(req.query, "pageSize"))) ? Number(get(req.query, "pageSize")) : 10;
            const page = !isNaN(Number(get(req.query, "page"))) ? Number(get(req.query, "page")) : 1
            let search = get(req.query, "search");

            // Can be string | string[] | ParsedQs | ParsedQs[]
            let [sortField, sortOrder] = get(req.query, "sorting", "id DESC").toString().split(" ");

            const taxRateData = await new TaxRateRepo().getAll({ page, limit: size, search: isString(search) ? search : "", sorting: [sortField, sortOrder] }, transaction)
            if (!taxRateData.success) throw new Error(taxRateData.message)


            await transaction.commit();
            res.status(200).send(taxRateData);
        } catch (err: any) {
            await transaction.rollback();
            HelperMethods.handleError(err);
            res.status(500).send({
                message: err.message || ERROR_MESSAGE.INTERNAL_SERVER_ERROR,
            });
        }

    }

    static async delete(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {

            const id = Number(get(req.params, "id"));
            const user_id = parseInt(get(req, 'user_id')!);


            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER);
            }

            if (isNaN(id)) {
                throw new Error(ERROR_MESSAGE.ID_SHOULD_BE_NUMBER);
            }

            const deletedCount = await new TaxRateRepo().delete(id, (user_id), transaction);

            if (!deletedCount.success) throw new Error(deletedCount.message)

            await transaction.commit();
            res.status(200).send(deletedCount);
        } catch (err: any) {
            await transaction.rollback();
            HelperMethods.handleError(err);
            res.status(500).send(
                HelperMethods.getErrorResponse(err.message || ERROR_MESSAGE.INTERNAL_SERVER_ERROR)
            );
        }
    }
}