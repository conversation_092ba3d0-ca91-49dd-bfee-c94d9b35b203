import { z } from "zod";

class TaxRateValidationSchema {

    // Create Tax Rate Schema
    static createTaxRateSchema = z.object({
        title: z.string()
            .min(3, `title should have a minimum length of 3.`)
            .max(100, `title should have a maximum length of 100.`)
            .nonempty(`title cannot be empty.`)
            .transform(val => val.toLowerCase())
            .describe("title is required."),

        value: z.number({
            required_error: `value is required.`,
            invalid_type_error: `value should be a number.`,
        }),
    });


    // List Tax Rates Schema
    static listTaxRateSchema = z.object({
        page: z.string().optional()
            .refine(val => val && val.trim() !== "", {
                message: "Page parameter is required",
            }).transform(Number).refine(val => !isNaN(val) && val > 0, {
                message: "Page must be a positive number",
            })
        ,
        pageSize: z.string().optional()
            .refine(val => val && val.trim() !== "", {
                message: "Page Size parameter is required",
            }).transform(Number).refine(val => !isNaN(val) && val > 0, {
                message: "Page size must be a positive number",
            }),
        search: z.string().optional(),
        sorting: z.string().optional(),
    });


    // Get Tax Rate by ID Schema
    static getTaxRateSchema = z.object({
        id: z.number({
            required_error: `id is required.`,
            invalid_type_error: `id should be a number.`,
        }),
    });


    // Update Tax Rate Schema
    static updateTaxRateSchema = z.object({
        params: z.object({
            id: z.string({
                required_error: `id is required.`,
                invalid_type_error: `id should be a string.`,
            }).nonempty(`id cannot be empty.`),
        }),
        body: z.object({
            title: z.string().min(3).max(100).optional().transform(val => {
                if (val) return val.toLowerCase()
            }),
            value: z.number().positive().optional(),
        }),
    });


    // Delete Tax Rate Schema
    static deleteTaxRateSchema = z.object({
        params: z.object({
            id: z.string({
                required_error: `"id" is required.`,
                invalid_type_error: `"id" should be a string.`,
            }).nonempty(`"id" cannot be empty.`),
        }),
    });

}

export default TaxRateValidationSchema
