
import { Request, Response, NextFunction } from "express";
import { HelperMethods } from "../../../core/HelperMethods";
import { get } from "lodash";
import TaxRateValidationSchema from "./TaxRateValidationSchema";

export class TaxRateValidation {


    static validateCreate = (req: Request, res: Response, next: NextFunction) => {

        const result = TaxRateValidationSchema.createTaxRateSchema.safeParse(req.body);

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {
        const result = TaxRateValidationSchema.updateTaxRateSchema.safeParse({ params: req.params, body: req.body });

        let errorMessage = "";
        if (!result.success) {
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }

    static validateDelete = (req: Request, res: Response, next: NextFunction) => {

        const result = TaxRateValidationSchema.deleteTaxRateSchema.safeParse({ params: req.params, body: req.body });

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }

    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {
        const result = TaxRateValidationSchema.listTaxRateSchema.safeParse(req.query);
        
        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();

    }

    static validateGetById = (req: Request, res: Response, next: NextFunction) => {

        const result = TaxRateValidationSchema.getTaxRateSchema.safeParse(req.params);

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }

    static validateSearchByText = (req: Request, res: Response, next: NextFunction) => {

        const text = get(req.query, "text");
        if (!text) {
            res.status(400).send(HelperMethods.getErrorResponse("Invalid request"));
            return;
        }

        return next();
    }

}