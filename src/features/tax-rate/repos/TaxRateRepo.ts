import { ParsedTaxRate, TaxFilterList, ICreationTaxRatePayload, IUpdateTaxRatePayload } from "../interface/taxRate";
import { TaxRateModel } from "../models/TaxRateTable";
import { parseTaxRate } from "../parser/taxRateParser";
import { ITaxRateRepo } from "./ITaxRateRepo";
import { Op, Sequelize, Transaction, UniqueConstraintError } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { getUserName } from "../../../core/common-utils";
import { HelperMethods } from "../../../core/HelperMethods";
import { PaginationProvider } from "../../pagination/PaginationProvider";

export default class TaxRateRepo implements ITaxRateRepo {
    async create(payload: ICreationTaxRatePayload, user_id: number, transaction: Transaction): Promise<APIBaseResponse<ParsedTaxRate | null>> {
        try {

            const user = await CoreUserTable.findByPk(user_id, { transaction });
            if (!user) {
                throw new Error("User not found");
            }
            const USER_DATA = user.toJSON()
            const taxRateCreated = await TaxRateModel.create({ ...payload }, {
                userId: USER_DATA.id,
                userName: getUserName(USER_DATA),
                transaction
            });
            if (!taxRateCreated) {
                throw new Error("Tax Rate not created");
            }

            const taxRateCreatedData = await TaxRateModel.findOne({
                where: {
                    id: taxRateCreated.toJSON().id,
                },
                include: TaxRateModel.getInclude(),
                transaction
            });
            return HelperMethods.getSuccessResponse(parseTaxRate(taxRateCreatedData))
        } catch (error: any) {
            HelperMethods.handleError(error)
            return HelperMethods.getErrorResponse(error.message)
        }
    }

    async update(id: number, payload: IUpdateTaxRatePayload, transaction: Transaction): Promise<APIBaseResponse<ParsedTaxRate | null>> {
        try {
            const existingTaxRate = await TaxRateModel.findOne({
                where: {
                    id: id
                }, transaction,
            });

            if (!existingTaxRate) {
                throw new Error(`Tax Rate with ID ${id} not found`);
            }

            await TaxRateModel.update(payload, {
                where: {
                    id: id
                },
                transaction,
                userId: (payload.updatedBy),
            });

            const updatedData = await TaxRateModel.findByPk(id, {
                include: TaxRateModel.getInclude(),
            })

            return HelperMethods.getSuccessResponse(parseTaxRate(updatedData))

        } catch (error: any) {
            HelperMethods.handleError(error)
            if (error instanceof UniqueConstraintError && error.errors[0].path === 'title') {
                return HelperMethods.getErrorResponse('title already exists');
            } else {
                return HelperMethods.getErrorResponse(error.message)
            }
        }
    }

    async getById(id: number, transaction: Transaction): Promise<APIBaseResponse<ParsedTaxRate | null>> {
        try {
            const existingTaxRate = await TaxRateModel.findOne({
                where: {
                    id: id
                }, transaction,
                include: TaxRateModel.getInclude()
            });

            if (!existingTaxRate) {
                throw new Error(`Tax rate not found of this (${id}) id`);
            }
            return HelperMethods.getSuccessResponse(parseTaxRate(existingTaxRate))
        } catch (error: any) {
            HelperMethods.handleError(error)
            return HelperMethods.getErrorResponse(error.message)
        }
    }

    async getAll(filters: TaxFilterList, transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<ParsedTaxRate> | null>> {
        try {
            const where: any = {}
            if (filters?.search) {
                where[Op.or] = [
                    { title: { [Op.iLike]: `%${filters.search}%` } },
                    Sequelize.literal(`CAST("TaxRateModel"."value" AS TEXT) ILIKE '%${filters.search}%'`),
                ];
            }

            const response = await new PaginationProvider<
                any,
                ParsedTaxRate
            >().getPaginatedRecords(TaxRateModel, { include:TaxRateModel.getInclude(), limit: filters.limit, page: filters.page, where: where, dateColumn: "createdAt", }, transaction, parseTaxRate);

            return HelperMethods.getSuccessResponse({
                data: response.rows,
                currentPage: response.currentPage,
                totalPages: response.totalPages,
                totalData: response.total,
            })
        } catch (error: any) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse(error.message)
        }
    }



    async delete(id: number, deletedById: number, transaction: Transaction): Promise<APIBaseResponse<null | number>> {
        try {
            const isExists=await this.getById(id,transaction)

            if(!isExists.success) throw new Error(isExists.message)

            const response = await TaxRateModel.destroy({
                where: {
                    id: id,
                },
                transaction,
                userId: deletedById
            });

            if (response === 0) {
                return HelperMethods.getErrorResponse("data not deleted.")
            }
            return HelperMethods.getSuccessResponse(response, "data successfully delete")
        } catch (error: any) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse(error.message)
        }
    }
}
