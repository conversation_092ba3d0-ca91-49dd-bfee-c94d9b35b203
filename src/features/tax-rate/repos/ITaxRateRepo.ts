import { APIBaseResponse,PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { ICreationTaxRatePayload, IUpdateTaxRatePayload, ParsedTaxRate, TaxFilterList, TaxRatePayload } from "../interface/taxRate";
import { Transaction } from "sequelize";


export interface ITaxRateRepo {
    create(payload: ICreationTaxRatePayload, user_id: number, transaction: Transaction):Promise<APIBaseResponse<ParsedTaxRate | null>>;

    update(id: number, payload: IUpdateTaxRatePayload,transaction: Transaction):Promise<APIBaseResponse<ParsedTaxRate | null>>;

    getById(id: number,transaction: Transaction): Promise<APIBaseResponse<ParsedTaxRate | null>>;

    getAll(filters: TaxFilterList,transaction: Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<ParsedTaxRate> | null> |null>

    delete(id: number,deletedById:number,transaction: Transaction): Promise<APIBaseResponse<null | number> |null>
}
