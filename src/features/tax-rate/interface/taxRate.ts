import { OrderItem } from "sequelize";
import { <PERSON><PERSON><PERSON>, Base<PERSON>etaUsers, MetaUser } from "../../../core/CoreInterfaces";

interface TaxRatePayload {
    title: string;
    value: number;
}
export interface TaxFilterList {
    page: number,
    limit: number,
    sorting?: OrderItem,
    search: string
}
interface TaxRateTable extends TaxRatePayload, BaseMeta, BaseMetaUsers { }

interface ICreationTaxRatePayload extends TaxRatePayload{
    createdBy:number
}



interface IUpdateTaxRatePayload extends TaxRatePayload{
    updatedBy:number
}


interface ParsedTaxRate {
    id: number;
    title: string;
    value: number
    createdBy: MetaUser;
    updatedBy: MetaUser | null;
    deletedBy: MetaUser | null;
    createdAt: Date;
    updatedAt: Date | null;
    deletedAt: Date | null;
}

export {
    TaxRatePayload,
    TaxRateTable,
    ICreationTaxRatePayload,
    ParsedTaxRate,
    IUpdateTaxRatePayload
}
