import { CreationOptional, DataTypes, IncludeOptions, InferAttributes, InferCreationAttributes, Model } from "sequelize";
import { ICreationTaxRatePayload, TaxRateTable } from "../interface/taxRate";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from "../../../core/RepoProvider";

class TaxRateModel extends Model<TaxRateTable,ICreationTaxRatePayload> {

    static getInclude(): IncludeOptions[] {
        const taxRateInclude = [
            {
                model: CoreUserTable,
                as: "createdByUser",
            },
            {
                model: CoreUserTable,
                as: "updatedByUser",
            },
            {
                model: CoreUserTable,
                as: "deletedByUser",
            }

        ]

        return taxRateInclude
    }

}


TaxRateModel.init({
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
    },
    title: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true
    },
    value: {
        type: DataTypes.DOUBLE,
        allowNull: false,
    },
    createdBy: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    updatedBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    deletedBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
        field: "updated_at",
    },
    deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: null,
        field: "deleted_at",
    },
}, {
    sequelize: sequelizeInit,
    tableName: 'tax_rate',
    timestamps: true,
    paranoid: true,
})


TaxRateModel.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "Tax Rate",
        instance,
        options
    );
});

TaxRateModel.addHook("afterUpdate", async (instance, options) => {
    // Now call logModelAction as before
    await RepoProvider.logRepo.logModelAction(
        "update",
        "Tax Rate",
        instance,
        options
    );
});

TaxRateModel.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "Tax Rate",
        instance,
        options
    );
});

export { TaxRateModel }
