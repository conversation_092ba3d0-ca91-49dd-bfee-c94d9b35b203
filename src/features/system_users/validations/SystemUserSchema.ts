import { z } from "zod";
import { SYSTEM_USER_TYPE } from "../models/SystemUserMisc";

export class SystemUserSchema {
    static createSchema = z.object({
        type: z.nativeEnum(SYSTEM_USER_TYPE, {
            errorMap: () => ({
                message: "Type is required and must be a valid third party system user type",
            })
        }),
        token: z.string({
            errorMap: () => ({
                message: "Token is required",
            })
        }).min(1, "Token cannot be empty").max(500, "Token must be less than 500 characters"),
        userId: z.number({
            errorMap: () => ({
                message: "User ID is required",
            })
        }).int().positive("User ID must be a positive number"),
    });

    static updateSchema = SystemUserSchema.createSchema.partial().extend({
        id: z.number({
            errorMap: () => ({
                message: "ID is required",
            })
        }).int().positive("ID must be a positive number"),
    });
}
