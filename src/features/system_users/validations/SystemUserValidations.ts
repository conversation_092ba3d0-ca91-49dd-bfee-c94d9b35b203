import { NextFunction, Request, Response } from "express";
import { z } from "zod";
import { HelperMethods } from "../../../core/HelperMethods";
import { CoreSchemas } from "../../../core/CoreSchemas";
import { SystemUserSchema } from "./SystemUserSchema";

export class SystemUserValidations {
    static validateCreate = (req: Request, res: Response, next: NextFunction) => {
        const result = SystemUserSchema.createSchema.safeParse(req.body);
        
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        
        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {
        let result: any = CoreSchemas.updateByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        result = SystemUserSchema.updateSchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        
        return next();
    }

    static validateGetById = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.getByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        return next();
    }

    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        return next();
    }

    static validateDelete = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.getByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        return next();
    }

    static validateGetByUserId = (req: Request, res: Response, next: NextFunction) => {
        const result = z.object({
            userId: z.string().transform((val) => parseInt(val, 10)).refine((val) => !isNaN(val) && val > 0, {
                message: "User ID must be a positive number"
            })
        }).safeParse(req.params);
        
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        return next();
    }
}
