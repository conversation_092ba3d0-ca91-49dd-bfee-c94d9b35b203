import { InterfaceMetaData } from "../../../core/CoreInterfaces";
import { SYSTEM_USER_TYPE } from "./SystemUserMisc";

interface ISystemUser extends InterfaceMetaData {
    id: number;
    type: SYSTEM_USER_TYPE;
    token: string;
    userId: number;
}

interface ICreateSystemUser {
    type: SYSTEM_USER_TYPE;
    token: string;
    userId: number;
    createdById: number;
}

interface IUpdateSystemUser extends Omit<ICreateSystemUser, "createdById"> {
    id: number;
    updatedById: number;
}

interface ISystemUserResponse {
    id: number;
    type: SYSTEM_USER_TYPE;
    token: string;
    userId: number;
    user?: {
        id: number;
        firstName: string;
        lastName: string;
        email: string;
    };
    createdBy: string;
    createdAt: Date;
}

export { 
    ISystemUser, 
    ICreateSystemUser, 
    IUpdateSystemUser, 
    ISystemUserResponse 
};
