import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from '../../../sequelize_init';
import { RepoProvider } from '../../../core/RepoProvider';
import { CoreUserTable } from '../../users/core/database/CoreUserTable';
import { ICreateSystemUser, ISystemUser } from '../models/ISystemUser';
import { SYSTEM_USER_TYPE } from '../models/SystemUserMisc';

class SystemUserTable extends Model<ISystemUser, ICreateSystemUser> {
    declare user: CoreUserTable;
    declare createdBy: CoreUserTable;
}

SystemUserTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        type: {
            type: DataTypes.ENUM(...Object.values(SYSTEM_USER_TYPE)),
            allowNull: false,
        },
        token: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.userId;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        },
    },
    {
        sequelize: sequelizeInit,
        modelName: 'SystemUserTable',
        tableName: 'system_users',
        timestamps: true,
        underscored: true,
        paranoid: true,
    }
);

SystemUserTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "system_users",
        instance,
        options
    );
});

SystemUserTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "system_users",
        instance,
        options
    );
});

SystemUserTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "system_users",
        instance,
        options
    );
});

export { SystemUserTable };
