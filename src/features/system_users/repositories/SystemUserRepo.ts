import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { SYSTEM_USER_TYPE } from "../models/SystemUserMisc";
import { ISystemUser, ICreateSystemUser, IUpdateSystemUser, ISystemUserResponse } from "../models/ISystemUser";
import { SystemUserTable } from "../database/SystemUserTable";

export interface ISystemUserRepo {
    getSystemUser(type: SYSTEM_USER_TYPE, transaction?: Transaction): Promise<APIBaseResponse<SystemUserTable | null>>;
    create(payload: ICreateSystemUser, transaction: Transaction): Promise<APIBaseResponse<ISystemUser | null>>;
    update(payload: IUpdateSystemUser, transaction: Transaction): Promise<APIBaseResponse<null>>;
    getById(id: number, transaction: Transaction): Promise<APIBaseResponse<ISystemUserResponse | null>>;
    getAll(page: number, limit: number, transaction: Transaction): Promise<PaginatedBaseResponse<ISystemUserResponse>>;
    getByUserId(userId: number, transaction: Transaction): Promise<APIBaseResponse<ISystemUserResponse[]>>;
    delete(id: number, deletedById: number, transaction: Transaction): Promise<APIBaseResponse<null>>;
}
