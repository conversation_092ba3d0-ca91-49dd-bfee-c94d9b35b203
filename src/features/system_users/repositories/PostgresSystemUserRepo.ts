import { Transaction, UniqueConstraintError } from "sequelize";
import { ISystemUserRepo } from "./SystemUserRepo";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { SYSTEM_USER_TYPE } from "../models/SystemUserMisc";
import { ISystemUser, ICreateSystemUser, IUpdateSystemUser, ISystemUserResponse } from "../models/ISystemUser";
import { SystemUserTable } from "../database/SystemUserTable";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { HelperMethods } from "../../../core/HelperMethods";

export class PostgresSystemUserRepo implements ISystemUserRepo {
    
    async getSystemUser(type: SYSTEM_USER_TYPE, transaction?: Transaction): Promise<APIBaseResponse<SystemUserTable | null>> {
        try {
            const result = await SystemUserTable.findOne({
                where: {
                    type: type,
                    deletedAt: null
                },
                transaction
            });

            if (!result) {
                return HelperMethods.getSuccessResponse(null);
            }

            return HelperMethods.getSuccessResponse(result);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async create(payload: ICreateSystemUser, transaction: Transaction): Promise<APIBaseResponse<ISystemUser | null>> {
        try {
            const result = await SystemUserTable.create(payload, {
                transaction,
                userId: payload.createdById,
                individualHooks: true
            });

            return HelperMethods.getSuccessResponse(result.dataValues);

        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Third party system user already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(payload: IUpdateSystemUser, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            await SystemUserTable.update(payload, {
                where: {
                    id: payload.id
                },
                userId: payload.updatedById,
                individualHooks: true,
                transaction
            });

            return HelperMethods.getSuccessResponse(null);

        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Third party system user already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number, transaction: Transaction): Promise<APIBaseResponse<ISystemUserResponse | null>> {
        try {
            const result = await SystemUserTable.findByPk(id, {
                include: [
                    {
                        model: CoreUserTable,
                        as: 'user',
                        attributes: ['id', 'firstName', 'lastName', 'email']
                    },
                    {
                        model: CoreUserTable,
                        as: 'createdBy',
                        attributes: ['firstName', 'lastName']
                    }
                ],
                transaction
            });

            if (!result) {
                return HelperMethods.getSuccessResponse(null);
            }

            const response: ISystemUserResponse = {
                id: result.dataValues.id,
                type: result.dataValues.type,
                token: result.dataValues.token,
                userId: result.dataValues.userId,
                user: result.user ? {
                    id: result.user.dataValues.id,
                    firstName: result.user.dataValues.firstName,
                    lastName: result.user.dataValues.lastName,
                    email: result.user.dataValues.email
                } : undefined,
                createdBy: result.createdBy ?
                    `${result.createdBy.dataValues.firstName} ${result.createdBy.dataValues.lastName}` : '',
                createdAt: result.dataValues.createdAt
            };

            return HelperMethods.getSuccessResponse(response);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, limit: number, transaction: Transaction): Promise<PaginatedBaseResponse<ISystemUserResponse>> {
        try {
            const offset = (page - 1) * limit;

            const { count, rows } = await SystemUserTable.findAndCountAll({
                include: [
                    {
                        model: CoreUserTable,
                        as: 'user',
                        attributes: ['id', 'firstName', 'lastName', 'email']
                    },
                    {
                        model: CoreUserTable,
                        as: 'createdBy',
                        attributes: ['firstName', 'lastName']
                    }
                ],
                limit,
                offset,
                order: [['createdAt', 'DESC']],
                transaction
            });

            const totalPages = Math.ceil(count / limit);

            const data: ISystemUserResponse[] = rows.map(row => ({
                id: row.dataValues.id,
                type: row.dataValues.type,
                token: row.dataValues.token,
                userId: row.dataValues.userId,
                user: row.user ? {
                    id: row.user.dataValues.id,
                    firstName: row.user.dataValues.firstName,
                    lastName: row.user.dataValues.lastName,
                    email: row.user.dataValues.email
                } : undefined,
                createdBy: row.createdBy ?
                    `${row.createdBy.dataValues.firstName} ${row.createdBy.dataValues.lastName}` : '',
                createdAt: row.dataValues.createdAt
            }));

            return {
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            };

        } catch (error) {
            HelperMethods.handleError(error);
            return {
                currentPage: page,
                totalData: 0,
                totalPages: 0,
                data: [],
            };
        }
    }

    async getByUserId(userId: number, transaction: Transaction): Promise<APIBaseResponse<ISystemUserResponse[]>> {
        try {
            const results = await SystemUserTable.findAll({
                where: {
                    userId: userId,
                    deletedAt: null
                },
                include: [
                    {
                        model: CoreUserTable,
                        as: 'user',
                        attributes: ['id', 'firstName', 'lastName', 'email']
                    },
                    {
                        model: CoreUserTable,
                        as: 'createdBy',
                        attributes: ['firstName', 'lastName']
                    }
                ],
                order: [['createdAt', 'DESC']],
                transaction
            });

            const data: ISystemUserResponse[] = results.map(row => ({
                id: row.dataValues.id,
                type: row.dataValues.type,
                token: row.dataValues.token,
                userId: row.dataValues.userId,
                user: row.user ? {
                    id: row.user.dataValues.id,
                    firstName: row.user.dataValues.firstName,
                    lastName: row.user.dataValues.lastName,
                    email: row.user.dataValues.email
                } : undefined,
                createdBy: row.createdBy ?
                    `${row.createdBy.dataValues.firstName} ${row.createdBy.dataValues.lastName}` : '',
                createdAt: row.dataValues.createdAt
            }));

            return HelperMethods.getSuccessResponse(data);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getSuccessResponse([]);
        }
    }

    async delete(id: number, deletedById: number, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            await SystemUserTable.update(
                {
                    deletedAt: new Date(),
                    deletedById: deletedById
                },
                {
                    where: { id },
                    userId: deletedById,
                    individualHooks: true,
                    transaction
                }
            );

            return HelperMethods.getSuccessResponse(null);

        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
}
