import { Request, Response, NextFunction } from "express";
import { ItemAttributeValueSchema } from "./ItemAttributeValueSchema";
import { HelperMethods } from "../../../core/HelperMethods";
import { CoreSchemas } from "../../../core/CoreSchemas";

export class ItemAttributeValueValidations {
    static validateCreate = (req: Request, res: Response, next: NextFunction) => {
        const result = ItemAttributeValueSchema.create.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {
        let result: any = CoreSchemas.updateByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        result = ItemAttributeValueSchema.update.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateGetById = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.getByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateIdArray = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.getIdArraySchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
            }
            return next();
        }
}