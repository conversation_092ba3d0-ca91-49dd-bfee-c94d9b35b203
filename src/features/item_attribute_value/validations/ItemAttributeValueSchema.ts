import { title } from "process";
import { z } from "zod";
import { ITEM_ATTRIBUTES_VALUE_STATUS } from "../models/ItemAttributeValueMisc";

export class ItemAttributeValueSchema {
    static create =
        z.object({
            itemAttributeId: z.number().int().positive("Item attribute id must be a positive number"),
            itemAttributeValues: z.array(
                z.object({
                    title: z.string()
                        .min(1, "Title must be at least 1 character long")
                        .max(100, "Title must be up to 100 characters long"),
                    value: z.string()
                        .min(1, "Value must be at least 1 character long")
                        .max(100, "Value must be up to 100 characters long"),
                })
            ).min(1, "At least one item attribute value is required")

        });

    static update =
        z.object({
            value: z.string().min(1, "Value must be at least 1 character long").max(100, "Value must be up to 100 characters long"),
            itemAttributeId: z.number().int().positive("Item attribute id must be a positive number"),
            status: z.nativeEnum(ITEM_ATTRIBUTES_VALUE_STATUS).refine((status) => Object.values(ITEM_ATTRIBUTES_VALUE_STATUS).includes(status), {
                message: "Invalid status",
            }),
            title: z.string().min(1, "Title must be at least 1 character long").max(100, "Title must be up to 100 characters long"),
        });

}