import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get, pick } from "lodash";
import { ITEM_ATTRIBUTES_VALUE_STATUS } from "../models/ItemAttributeValueMisc";
import { CreateItemAttributeValue, ItemAttributeValuePayload, UpdateItemAttributeValue } from "../models/IItemAttributeValue";
import { sequelizeInit } from "../../../sequelize_init";
import { HelperMethods } from "../../../core/HelperMethods";

export class ItemAttributeValueController {

    static async create(req: Request, res: Response, next: NextFunction) {

        const T = await sequelizeInit.transaction()
        try {
            const user_id = get(req, "user_id",);
    
            const requestBody = req.body as ItemAttributeValuePayload;
    
            const payload: CreateItemAttributeValue[] = requestBody.itemAttributeValues.map(data => {
                return {
                    title: data.title,
                    value: data.value,
                    itemAttributeId: requestBody.itemAttributeId,
                    status: ITEM_ATTRIBUTES_VALUE_STATUS.ACTIVE,
                    createdById: Number(user_id!),
                }
            })
            const result = await RepoProvider.itemAttributeValueRepo.create(payload, user_id!,T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async update(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = Number(get(req.params, "id"));
    
            const user_id = get(req, "user_id",);
    
            const requestBody = req.body as UpdateItemAttributeValue;
            const payload: UpdateItemAttributeValue = {
                id: id,
                title: requestBody.title,
                value: requestBody.value,
                itemAttributeId: requestBody.itemAttributeId,
                status: requestBody.status,
                updatedById: Number(user_id!),
            };
    
            const result = await RepoProvider.itemAttributeValueRepo.update(payload,T);
            if (!result.success) {
                throw new Error(result.message)
            }
            res.status(200).send(result);
            await T.commit()
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
            const text = get(req.query, "text") as string;
            const result = await RepoProvider.itemAttributeValueRepo.getAll(page, pageSize, T,text);
            if (!result.success) {
                throw new Error(result.message)
            }
            res.status(200).send(result);
            await T.commit()
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.itemAttributeValueRepo.getById(Number(id), T);
            if (!result.success) {
                throw new Error('Data not exists.')
            }
            T.commit()
            res.status(200).send(result);
        } catch (error) {
            T.rollback()
            res.status(200).send(error instanceof Error ? error.message : `Something went wrong : Data not exists.`);
        }
    }
}