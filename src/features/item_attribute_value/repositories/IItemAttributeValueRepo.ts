import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { ItemAttributeValueTable } from "../database/ItemAttributeValueTable";
import { CreateItemAttributeValue, UpdateItemAttributeValue } from "../models/IItemAttributeValue";

export interface IItemAttributeValueRepo {
    create(payload: CreateItemAttributeValue[], userId: Number, transaction: Transaction): Promise<APIBaseResponse<ItemAttributeValueTable[] | null>>;

    update(payload: UpdateItemAttributeValue, transaction: Transaction): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number, transaction: Transaction, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<ItemAttributeValueTable> | null>>;

    getById(id: Number, transaction: Transaction): Promise<APIBaseResponse<ItemAttributeValueTable | null>>;

    delete(ids: Number[], deletedById: Number, transaction: Transaction): Promise<APIBaseResponse<null>>;
}