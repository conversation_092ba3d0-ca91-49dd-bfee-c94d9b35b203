import * as express from "express";
import { ItemAttributeValueValidations } from "../validations/ItemAttributeValueValidations";
import { ItemAttributeValueController } from "../controller/ItemAttributeValueController";

const apiInitialPath = "/item-attributes-value";
const itemAttributeValueRouter = express.Router();

itemAttributeValueRouter.post(apiInitialPath + "/", ItemAttributeValueValidations.validateCreate, ItemAttributeValueController.create);

itemAttributeValueRouter.put(apiInitialPath + "/:id", ItemAttributeValueValidations.validateUpdate, ItemAttributeValueController.update);

itemAttributeValueRouter.get(apiInitialPath + "/", ItemAttributeValueValidations.validateGetAll, ItemAttributeValueController.getAll);

itemAttributeValueRouter.get(apiInitialPath + "/:id", ItemAttributeValueValidations.validateGetById, ItemAttributeValueController.getById);

// itemAttributeValueRouter.delete(apiInitialPath + "/delete" , ItemAttributeValueValidations.validateIdArray, ItemAttributeValueController.delete);

export { itemAttributeValueRouter };