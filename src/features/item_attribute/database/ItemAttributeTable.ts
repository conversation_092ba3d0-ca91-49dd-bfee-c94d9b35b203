import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { CreateItemAttribute, IItemAttribute } from '../models/IItemAttribute';
import { ITEM_ATTRIBUTES_STATUS } from '../models/ItemAttributeMisc';
import { RepoProvider } from '../../../core/RepoProvider';


class ItemAttributeTable extends Model<IItemAttribute, CreateItemAttribute> { }

ItemAttributeTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value.toString());
                }
            }
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM(...Object.values(ITEM_ATTRIBUTES_STATUS)),
            allowNull: false,
        },

        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'item_attributes',
        timestamps: true,
        paranoid: true,
    },
);

ItemAttributeTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "item_attributes",
        instance,
        options
    );
});

ItemAttributeTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "item_attributes",
        instance,
        options
    );
});

ItemAttributeTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "item_attributes",
        instance,
        options
    );
});

export { ItemAttributeTable };