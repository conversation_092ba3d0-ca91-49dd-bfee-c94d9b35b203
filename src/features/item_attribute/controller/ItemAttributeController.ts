import { NextFunction, Request, Response } from 'express'
import { RepoProvider } from '../../../core/RepoProvider'
import { get } from 'lodash'
import { ITEM_ATTRIBUTES_STATUS } from '../models/ItemAttributeMisc'
import {
  CreateItemAttribute,
  UpdateItemAttribute,
} from '../models/IItemAttribute'
import { HelperMethods } from '../../../core/HelperMethods'
import { sequelizeInit } from '../../../sequelize_init'

export class ItemAttributeController {

  static async create(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const user_id = get(req, "user_id");

      const payload: CreateItemAttribute = {
        name: req.body.name,
        status: ITEM_ATTRIBUTES_STATUS.ACTIVE,
        createdById: Number(user_id!),
      };

      const result = await RepoProvider.itemAttributeRepo.create(payload, T)
      if (!result.success) {
        res.status(500).send(result)
        return
      }
      await T.commit()
      res.status(200).send(result)
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not created"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }

  static async update(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const id = Number(get(req.params, "id"));
      const user_id = get(req, "user_id",);

      const payload: UpdateItemAttribute = {
        id: id,
        name: req.body.name,
        status: req.body.status,
        updatedById: Number(user_id!),
      };

      const result = await RepoProvider.itemAttributeRepo.update(payload, T)
      if (!result.success) {
        res.status(500).send(result)
        return
      }
      await T.commit()
      res.status(200).send(result)
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }



  static async getAll(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const page = Number(get(req.query, 'page'))
      const pageSize = Number(get(req.query, 'pageSize'))
      const text = get(req.query, 'text') as string
      const result = await RepoProvider.itemAttributeRepo.getAll(
        page,
        pageSize,
        T,
        text
      )
      if (!result.success) {
        res.status(500).send(result)
        return
      }
      await T.commit()
      res.status(200).send(result)
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }

  static async getById(req: Request, res: Response, next: NextFunction) {
    const T = await sequelizeInit.transaction()
    try {
      const id = get(req.params, "id");
      const result = await RepoProvider.itemAttributeRepo.getById(Number(id), T);
      if (!result.success) {
        res.status(500).send(result);
        return;
      }
      await T.commit()
      res.status(200).send(result);
    } catch (error) {
      await T.rollback()
      const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
      res.status(500).send(HelperMethods.getErrorResponse(msg))
    }
  }
}
