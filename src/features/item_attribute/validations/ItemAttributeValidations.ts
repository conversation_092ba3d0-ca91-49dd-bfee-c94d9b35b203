import { Request, Response, NextFunction } from "express";
import { ItemAttributeSchema } from "./ItemAttributeSchema";
import { HelperMethods } from "../../../core/HelperMethods";
import { CoreSchemas } from "../../../core/CoreSchemas";

export class ItemAttributeValidations {

    static validateCreate = (req: Request, res: Response, next: NextFunction) => {
        const result = ItemAttributeSchema.create.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {
        
        let result: any = CoreSchemas.updateByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }

        result = ItemAttributeSchema.update.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateGetAll = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.paginationSchema.safeParse(req.query);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateId = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.getByIdSchema.safeParse(req.params);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
        }
        return next();
    }

    static validateIdArray = (req: Request, res: Response, next: NextFunction) => {
        const result = CoreSchemas.getIdArraySchema.safeParse(req.body);
        if (!result.success) {
            res.status(400).send(HelperMethods.getErrorResponse(result.error.errors[0].message));
            return;
            }
            return next();
        }
}