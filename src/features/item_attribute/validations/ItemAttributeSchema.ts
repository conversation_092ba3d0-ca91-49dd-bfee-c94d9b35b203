import { z } from "zod";
import { ITEM_ATTRIBUTES_STATUS } from "../models/ItemAttributeMisc";

export class ItemAttributeSchema {
    static create =
        z.object({
            name: z.string().min(3, "Name must be at least 3 characters long").max(100, "Name must be up to 100 characters long"),
        });

    static update = 
        z.object({
            name: z.string().min(3, "Name must be at least 3 characters long").max(100, "Name must be up to 100 characters long"),
            status: z.nativeEnum(ITEM_ATTRIBUTES_STATUS).refine(val => Object.values(ITEM_ATTRIBUTES_STATUS).includes(val), {
                message: "Invalid status",
        }),
    });

}