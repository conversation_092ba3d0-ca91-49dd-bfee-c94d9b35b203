import * as express from 'express'
import { ItemAttributeValidations } from '../validations/ItemAttributeValidations'
import { ItemAttributeController } from '../controller/ItemAttributeController'

const apiInitialPath = '/item-attributes'
const itemAttributeRouter = express.Router()

itemAttributeRouter.post(
  apiInitialPath + '/',
  ItemAttributeValidations.validateCreate,
  ItemAttributeController.create
)

itemAttributeRouter.put(
  apiInitialPath + '/:id',
  ItemAttributeValidations.validateUpdate,
  ItemAttributeController.update
)

itemAttributeRouter.get(
  apiInitialPath + '/',
  ItemAttributeValidations.validateGetAll,
  ItemAttributeController.getAll
)

itemAttributeRouter.get(
  apiInitialPath + '/:id',
  ItemAttributeValidations.validateId,
  ItemAttributeController.getById
)

// itemAttributeRouter.delete(apiInitialPath + "/delete" , ItemAttributeValidations.validateIdArray, ItemAttributeController.delete);

export { itemAttributeRouter }
