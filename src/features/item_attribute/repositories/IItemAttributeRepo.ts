import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { ItemAttributeTable } from "../database/ItemAttributeTable";
import { CreateItemAttribute, UpdateItemAttribute, } from "../models/IItemAttribute";

export interface IItemAttributeRepo {
    create(payload: CreateItemAttribute, transaction: Transaction): Promise<APIBaseResponse<ItemAttributeTable | null>>;

    update(payload: UpdateItemAttribute, transaction: Transaction): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number, transaction: Transaction, text?: string): Promise<APIBaseResponse<PaginatedBaseResponse<ItemAttributeTable> | null>>;

    getById(id: Number, transaction: Transaction): Promise<APIBaseResponse<ItemAttributeTable | null>>;

    delete(ids: Number[], deletedById: Number, transaction: Transaction): Promise<APIBaseResponse<null>>;
}