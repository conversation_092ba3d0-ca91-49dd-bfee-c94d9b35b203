import { APIBaseResponse } from "../../../core/CoreInterfaces"
interface PresignedUrlOptions {
  key?: string; // e.g. "uploads/myfile.png"
  contentType?: string; // e.g. "image/png"
  expiresInSeconds?: number; // default: 600 (10 mins)
}

interface ICloudImageUploadRepo {
  generatePreSignedUrl(options: PresignedUrlOptions): Promise<APIBaseResponse<string | null>>;
  
  updateMetaData(options: {
    key: string;
    metaData: Record<string, string>;
    contentType?: string;
  }): Promise<APIBaseResponse<any>>;

  deleteFiles(options: {
    key: string[]; // e.g. "uploads/myfile.png"
    // versionId?: string; // optional, for versioned buckets
  }): Promise<APIBaseResponse<any>>;
}



export {ICloudImageUploadRepo};