import { APIBaseResponse } from "../../../core/CoreInterfaces";
import { ICloudImageUploadRepo } from "./ICloudImageUploadRepo";
import { 
  S3Client, 
  PutObjectCommand, 
  CopyObjectCommand, 
  DeleteObjectCommand,
  DeleteObjectsCommandInput,
  DeleteObjectsCommand
 } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

class CloudImageUploadRepo implements ICloudImageUploadRepo {
  private aws_client: S3Client | null = null;

  private  connectClient(): void {
    if (this.aws_client) return;

    try {
      this.aws_client = new S3Client({
        region: process.env.AWS_REGION || "ap-south-1",
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        },
      });
      console.log("✅ AWS S3 client connected");
    } catch (error) {
      console.error("❌ S3 client init failed", error);
      throw error;
    }
  }

  constructor(){
    this.connectClient();
   }
  async generatePreSignedUrl({
    key,
    contentType = "image/*",
    expiresInSeconds = 600,
  }: {
    key: string;
    contentType?: string;
    expiresInSeconds?: number;
  }): Promise<APIBaseResponse<string | null>> {
    try {
    //   await this.connectClient();x

      const command = new PutObjectCommand({
        Bucket: process.env.AWS_BUCKET!,
        Key: "public/"+key,
        ContentType: contentType,
      });

      const url = await getSignedUrl(this.aws_client!, command, { expiresIn: expiresInSeconds });
      // console.log(url)
      return {
        success: true,
        message: "Presigned URL generated",
        data: url,
      };
    } catch (error) {
      console.error("❌ Presigned URL error:", error);
      return {
        success: false,
        message: "Failed to generate pre-signed URL",
        data: null,
      };
    }
  }

  async updateMetaData({
    key,
    metaData,
    contentType = "image/png",
  }: {
    key: string;
    metaData: Record<string, string>;
    contentType?: string;
  }): Promise<APIBaseResponse<any>> {
    try {
      // await this.connectClient();

      const command = new CopyObjectCommand({
        Bucket: process.env.AWS_BUCKET!,
        CopySource: `${process.env.AWS_BUCKET}/${key}`,
        Key: key,
        MetadataDirective: "REPLACE",
        Metadata: metaData,
        ContentType: contentType,
      });

      const response = await this.aws_client!.send(command);

      return {
        success: true,
        message: "Metadata updated successfully",
        data: response,
      };
    } catch (error) {
      console.error("❌ Metadata update failed:", error);
      return {
        success: false,
        message: "Failed to update metadata",
        data: null,
      };
    }
  }

  async deleteFiles(options: { key: string[] }): Promise<APIBaseResponse<any>> {
    try {
      if (!options.key.length) {
        return {
          success: false,
          message: "No keys provided",
          data: null,
        };
      }

      const deleteParams: DeleteObjectsCommandInput = {
        Bucket: process.env.AWS_BUCKET!,
        Delete: {
          Objects: options.key.map((key) => ({ Key: key })),
          Quiet: false,
        },
      };

      const response = await this.aws_client!.send(
        new DeleteObjectsCommand(deleteParams)
      );
      // console.log(response)
      return {
        success: true,
        message: "Files deleted successfully",
        data: response,
      };
    } catch (error) {
      console.error("❌ File deletion failed:", error);
      return {
        success: false,
        message: "Failed to delete files",
        data: error,
      };
    }
  }
  

}

  
export { CloudImageUploadRepo };

