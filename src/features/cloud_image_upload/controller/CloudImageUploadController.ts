import {Request,Response} from 'express';
import { RepoProvider } from '../../../core/RepoProvider';
import { v4 as uuidv4 } from 'uuid';
export class CloudImageUploadController{
    static async generatePresignedUrls(req: Request, res: Response) {
  try {
    const payload: any = req?.body;
    const { fileNames, moduleName }: { fileNames: string[]; moduleName: string } = payload;

    // Generate UUID-based filenames, preserving file extensions if needed
    const fileKeys = fileNames.map((originalName) => {
      const extension = originalName.split('.').pop(); // get file extension
      const uniqueName = `${uuidv4()}.${extension}`;
      return `${moduleName}/${uniqueName}`;
    });

    const urls = await Promise.all(
      fileKeys.map(async (key) => {
        const url = await RepoProvider.cloudImageUploadRepo.generatePreSignedUrl({
          key,
          contentType: 'image/*',
          expiresInSeconds: 600,
        });
        return url.data;
      })
    );

    return res.status(200).send({
      success: true,
      message: 'Presigned URLs generated successfully',
      data: urls,
    });

  } catch (error) {
    console.error('❌ Error generating presigned URLs:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate presigned URLs',
      data: null,
    });
  }
}

    static async updateMetaData(req: Request, res: Response) {
        try {
            const { key, metaData, contentType = "image/png" }: { key: string; metaData: Record<string, string>; contentType?: string } = req.body;
            const response = await RepoProvider.cloudImageUploadRepo.updateMetaData({ key, metaData, contentType });
            return res.status(200).json(response);
        } catch (error) {
            console.error("❌ Error updating metadata:", error);
            return res.status(500).json({
                success: false,
                message: "Failed to update metadata",
                data: null
            });
        }
    }

    static async deleteFiles(req: Request, res: Response) {
        try {
            const { keys }: { keys: string[] } = req.body;
            const response = await RepoProvider.cloudImageUploadRepo.deleteFiles({ key: keys });
            return res.status(200).json(response);
        } catch (error) {
            console.error("❌ Error deleting files:", error);
            return res.status(500).json({
                success: false,
                message: "Failed to delete files",
                data: null
            });
        }
    }
    
}