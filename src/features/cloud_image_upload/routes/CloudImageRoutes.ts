const express = require('express');
const CloudImageRoutes = express.Router();
import { CloudImageUploadController } from '../controller/CloudImageUploadController';
import { CloudUploadValidations } from '../validations/CloudUploadValidations';
let apiInitialPath = '/Cloud'
CloudImageRoutes.post(apiInitialPath+"/get-Presigned-links",CloudUploadValidations.validateGeneratePresignedUrls, CloudImageUploadController.generatePresignedUrls);
// router.post(apiInitialPath+"/update-metadata", CloudImageUploadController.updateMetaData);
CloudImageRoutes.delete(apiInitialPath+"/delete-images", CloudUploadValidations.validateDeleteKeys, CloudImageUploadController.deleteFiles);
export {CloudImageRoutes};