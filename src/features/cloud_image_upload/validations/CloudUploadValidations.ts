import { CloudUploadSchema } from "../validations/CloudUploadSchema";
import { Request, Response, NextFunction } from "express";
export class CloudUploadValidations {
    static validateGeneratePresignedUrls = (req:Request, res:Response, next:NextFunction) => {
        const result = CloudUploadSchema.generatePresignedUrls.safeParse(req.body);

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            } else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send({ success: false, message: errorMessage });
            return;
        }

        return next();
    }
    static validateDeleteKeys = (req:Request, res:Response, next:NextFunction) => {
        const result = CloudUploadSchema.deleteFiles.safeParse(req.body);

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            } else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send({ success: false, message: errorMessage });
            return;
        }

        return next();
    }
}