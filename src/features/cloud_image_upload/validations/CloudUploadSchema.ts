import {z} from "zod";
export class CloudUploadSchema {
    static generatePresignedUrls = z.object({
        fileNames: z.array(z.string().min(1, "File name cannot be empty")),
        moduleName: z.string().min(1, "Module name cannot be empty"),
    });
    static deleteFiles = z.object({
        keys: z.array(z.string().min(1, "Key cannot be empty")),})
    // Add more schemas as needed for other operations
}