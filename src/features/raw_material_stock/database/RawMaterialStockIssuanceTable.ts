import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreateRawMaterialStockIssuance, IRawMaterialStockIssuance } from '../models/IRawMaterialStockIssuance';
import { RawMaterialVariationTable } from '../../raw_material/database/RawMaterialVariationTable';
import { RepoProvider } from '../../../core/RepoProvider';
import { CoreUserTable } from '../../users/core/database/CoreUserTable';
import { NormalUserTable } from '../../users/sub_feaures/normal_user/database/NormalUserTable';


class RawMaterialStockIssuanceTable extends Model<IRawMaterialStockIssuance, ICreateRawMaterialStockIssuance> {
    declare rawMaterial: RawMaterialVariationTable;
    declare issuedBy: CoreUserTable;
    declare issuedToUser : CoreUserTable|null;
}

RawMaterialStockIssuanceTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        rawMaterialId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return Number(value);
                }
            },
        },
        qty: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },

        soNumber: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        entryId: {
            type: DataTypes.STRING,
            allowNull: false,
            field: 'entry_id',
        },
        issuedTo: {
            type: DataTypes.INTEGER,
            allowNull: true,
            get() {
                const value = this.dataValues.issuedTo;
                if (value) {
                    return Number(value);
                }
            },
        },
        notes:{
            type:DataTypes.STRING,
            allowNull: true,
        },
        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw-material-stock-issuance',
        timestamps: true,
        paranoid: true,
    },
);



RawMaterialStockIssuanceTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "RawMaterialStockIssuance",
        instance,
        options
    );
});

RawMaterialStockIssuanceTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "RawMaterialStockIssuance",
        instance,
        options
    );
});

RawMaterialStockIssuanceTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "RawMaterialStockIssuance",
        instance,
        options
    );
});

export { RawMaterialStockIssuanceTable };