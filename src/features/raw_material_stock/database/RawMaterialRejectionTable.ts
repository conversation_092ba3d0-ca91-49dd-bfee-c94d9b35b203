import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreateRawMaterialRejection, IRawMaterialRejection } from '../models/IRawMaterialRejection';
import { RepoProvider } from '../../../core/RepoProvider';


class RawMaterialRejectionTable extends Model<IRawMaterialRejection, ICreateRawMaterialRejection> { }

RawMaterialRejectionTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        rawMaterialId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return Number(value);
                }
            },
        },
        totalQty: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        rejectedQty: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        rejectionReason: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        rejectedById: {
            type: DataTypes.INTEGER,
            allowNull: true,
            get() {
                const value = this.dataValues.rejectedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        purchaseInvoiceId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.purchaseInvoiceId;
                if (value) {
                    return Number(value);
                }
            },
        },


        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw-materials-rejections',
        timestamps: true,
        paranoid: true,
    },
);


RawMaterialRejectionTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "RawMaterialRejection",
        instance,
        options
    );
});

RawMaterialRejectionTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "RawMaterialRejection",
        instance,
        options
    );
});

RawMaterialRejectionTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "RawMaterialRejection",
        instance,
        options
    );
});


export { RawMaterialRejectionTable };