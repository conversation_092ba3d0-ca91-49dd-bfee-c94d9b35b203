import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreateRawMaterialHold, IRawMaterialHold } from '../models/IRawMaterialHold';
import { z } from 'zod';
import { RepoProvider } from '../../../core/RepoProvider';


class RawMaterialHoldTable extends Model<IRawMaterialHold, ICreateRawMaterialHold> { }

RawMaterialHoldTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        rawMaterialId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return Number(value);
                }
            },
        },
        totalQty: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        holdQty: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        holdReason: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        holdById: {
            type: DataTypes.INTEGER,
            allowNull: true,
            get() {
                const value = this.dataValues.holdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        purchaseInvoiceId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.purchaseInvoiceId;
                if (value) {
                    return Number(value);
                }
            },
        },

        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw-materials-holds',
        timestamps: true,
        paranoid: true,
    },
); z

RawMaterialHoldTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "RawMaterialHold",
        instance,
        options
    );
});

RawMaterialHoldTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "RawMaterialHold",
        instance,
        options
    );
});

RawMaterialHoldTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "RawMaterialHold",
        instance,
        options
    );
});



export { RawMaterialHoldTable };