import { DataTypes, Model } from 'sequelize';
import { sequelizeInit } from "../../../sequelize_init";
import { ICreateRawMaterialStockIn, IRawMaterialStockIn } from '../models/IRawMaterialStockIn';
import { RawMaterialVariationTable } from '../../raw_material/database/RawMaterialVariationTable';
import { SupplierTable } from '../../supplier/database/SupplierTable';
import { FactoryGateTable } from '../../factory_gates/database/FactoryGateTable';
import { StorageLocationTable } from '../../storage_locations/database/StorageLocationTable';
import { PurchaseInvoiceTable } from '../../purchase_invoice/database/PurchaseInvoiceTable';
import { RepoProvider } from '../../../core/RepoProvider';


class RawMaterialStockInTable extends Model<IRawMaterialStockIn, ICreateRawMaterialStockIn> {

    declare rawMaterialVariation: RawMaterialVariationTable;
    declare supplier: SupplierTable;
    declare factoryGate: FactoryGateTable;
    declare storageLocation?: StorageLocationTable;
    declare purchaseInvoice: PurchaseInvoiceTable;

}

RawMaterialStockInTable.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            get() {
                const value = this.dataValues.id;
                if (value) {
                    return Number(value);
                }
            },
        },
        rawMaterialId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.rawMaterialId;
                if (value) {
                    return Number(value);
                }
            },
        },
        supplierId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.supplierId;
                if (value) {
                    return Number(value);
                }
            },
        },

        purchaseInvoiceId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.purchaseInvoiceId;
                if (value) {
                    return Number(value);
                }
            },
        },

        price: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        qty: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
        },
        factoryGateId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.factoryGateId;
                if (value) {
                    return Number(value);
                }
            },
        },
        storageLocationId: {
            type: DataTypes.INTEGER,
            allowNull: true,
            get() {
                const value = this.dataValues.storageLocationId;
                if (value) {
                    return Number(value);
                }
            },
        },

        createdById: {
            type: DataTypes.INTEGER,
            allowNull: false,
            get() {
                const value = this.dataValues.createdById;
                if (value) {
                    return Number(value);
                }
            },
        },
        createdAt: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        updatedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.updatedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        updatedAt: {
            type: DataTypes.DATE,
        },
        deletedById: {
            type: DataTypes.INTEGER,
            get() {
                const value = this.dataValues.deletedById;
                if (value) {
                    return Number(value);
                }
            },
        },
        deletedAt: {
            type: DataTypes.DATE,
        }
    },
    {
        sequelize: sequelizeInit,
        tableName: 'raw-material-stock-in',
        timestamps: true,
        paranoid: true,
    },
);



RawMaterialStockInTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "RawMaterialStockIn",
        instance,
        options
    );
});

RawMaterialStockInTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "RawMaterialStockIn",
        instance,
        options
    );
});

RawMaterialStockInTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "RawMaterialStockIn",
        instance,
        options
    );
});

export { RawMaterialStockInTable };