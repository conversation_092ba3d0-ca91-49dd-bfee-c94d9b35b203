import { z } from "zod";

export class RawMaterialStockSchema {
    static receiveStock =
        z.object({
            rawMaterialId: z.number().int().positive(),
            qty: z.number().positive(),
            storageLocationId: z.number().int().positive().nullable(),
            factoryGateId: z.number().int().positive(),
            supplierId: z.number().int().positive(),
            price: z.number().positive(),
        });

    static updateStock =
        z.object({
            qty: z.number().positive(),
        });

    static assignStorageToStock =
        z.object({
            id: z.number().int().positive(),
            storageLocationId: z.number().int().positive(),
        });


    static issueStock = z.object({

        soNumber: z.string().min(3, "SO number must be at least 3 characters long").max(50, "SO number must be up to 50 characters long").nonempty("SO number is required"),
        issuedToUserId: z.number().int().positive("Invalid user ID").or(z.string().nonempty("Issued to user id is required")),
        notes: z.string().nullable(),
        rawMaterials: z.array(z.object({
            rawMaterialId: z.number().int().positive("Invalid raw material ID").or(z.string().nonempty("Raw material ID is required")),
            qty: z.number().positive("Quantity must be a positive number").or(z.string().nonempty("Quantity is required")),
        })).nonempty("At least one raw material is required")
    });

    static searchByRawMaterial =
        z.object({
            rawMaterialName: z.string().min(3, "Invalid raw material name"),
        });



    static searchInStockByText =
        z.object({
            text: z.string().min(3, "Invalid request"),
        });


    static exportByCategory =
        z.object({
            categoryId: z.string().transform((val) => Number(val)).refine((val) => Number.isInteger(val) && val > 0, "Invalid category id"),
        });

}
