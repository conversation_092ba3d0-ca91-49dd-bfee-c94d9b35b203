import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { IReceiveRawMaterialStock, IRawMaterialStockDetails, IRawMaterialStockInDetails, IRawMaterialStockUpdateRequest, IAssignStorageToStockRequest, IRawMaterialStockDetailsWithPrice } from "../models/IRawMaterialStock";
import { ICreateRawMaterialStockIssuance, IRawMaterialStockIssuanceResponse } from "../models/IRawMaterialStockIssuance";

export interface IRawMaterialStockRepo {

    receiveStock(payload: IReceiveRawMaterialStock,transaction:Transaction): Promise<APIBaseResponse<null>>;

    getAll(page: number, pageSize: number,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockDetails> | null>>;

    getById(id: number,transaction:Transaction): Promise<APIBaseResponse<IRawMaterialStockDetails | null>>;

    getByRawMaterialId(id: number,transaction:Transaction): Promise<APIBaseResponse<IRawMaterialStockDetails | null>>;

    getStockIn(page: number, pageSize: number, transaction:Transaction,text?: string, startDate?: Date, endDate?: Date): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>>;

    searchInStockByText(text: string, storageLocationNotAssigned: boolean,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>>;

    getStockInWithoutStorage(page: number, pageSize: number,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>>;

    getStockInById(id: number,transaction:Transaction): Promise<APIBaseResponse<IRawMaterialStockInDetails | null>>;

    updateStock(request: IRawMaterialStockUpdateRequest,transaction:Transaction): Promise<APIBaseResponse<null>>;

    assignStorageToStock(request: IAssignStorageToStockRequest,transaction:Transaction): Promise<APIBaseResponse<null>>;

    issueStock(request: ICreateRawMaterialStockIssuance[],transaction:Transaction): Promise<APIBaseResponse<null>>;

    getAllStockIssuance(page: number, pageSize: number,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse> | null>>;

    getStockIssuanceById(entryId: string,transaction:Transaction): Promise<APIBaseResponse<IRawMaterialStockIssuanceResponse | null>>;

    searchStockIssuanceByText(text: string,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockIssuanceResponse> | null>>;

    getStockInByDateRange(startDate: Date, endDate: Date, page: number, pageSize: number,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockInDetails> | null>>;

    searchByRawMaterial(rawMaterialName: string,transaction:Transaction): Promise<APIBaseResponse<PaginatedBaseResponse<IRawMaterialStockDetails> | null>>;

    exportByCategory(categoryId: number,transaction:Transaction): Promise<APIBaseResponse<IRawMaterialStockDetails[]| null>>;

    exportByCategoryWithPrice(categoryId: number,transaction:Transaction): Promise<APIBaseResponse<IRawMaterialStockDetailsWithPrice[]| null>>;

    // Debug method for local testing - finds stocks with inconsistent or negative values
    getProblematicStocks(transaction:Transaction): Promise<APIBaseResponse<IRawMaterialStockDetails[] | null>>;
}