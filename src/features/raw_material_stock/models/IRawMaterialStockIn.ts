import { InterfaceMetaData } from "../../../core/CoreInterfaces";



interface ICreateRawMaterialStockIn {
    rawMaterialId: number;
    qty: number;
    price: number;
    storageLocationId: Number | null;
    factoryGateId: number;
    supplierId: number;
    purchaseInvoiceId: number;
    createdById: number;
}

interface IRawMaterialStockIn extends ICreateRawMaterialStockIn, InterfaceMetaData {

}


export { IRawMaterialStockIn, ICreateRawMaterialStockIn };