import { InterfaceMetaData } from "../../../core/CoreInterfaces";

interface ICreateRawMaterialStock {
    rawMaterialId: number;
    totalStock: number;
    assignedStock: number;
    usableStock: number;
    createdById: number;
}

interface IRawMaterialStock extends ICreateRawMaterialStock, InterfaceMetaData {
    id: number;
}

interface IRawMaterialStockDetails extends IRawMaterialStock {
    rawMaterialName: string;
    sku:string;
    categoryName: string;
    msq: number;
    unassignedStock:number;
}

interface IRawMaterialStockDetailsWithPrice extends IRawMaterialStockDetails {
    price: number;
}

interface IRawMaterialStockUpdateRequest {
    rawMaterialId: number;
    qty: number;
    updatedById: number;
}

interface IReceiveRawMaterialStock extends InterfaceMetaData {
    rawMaterialId: number;
    qty: number;
    supplierId: number;
    price: number;
    storageLocationId: number | null;
    factoryGateId: number;
    rawMaterials: IRawMaterialReceivedItem[];

}

interface IRawMaterialReceivedItem {

}

interface IRawMaterialStockInDetails extends InterfaceMetaData {
    rawMaterialId: number;
    rawMaterial: string;
    categoryName: string;
    rawMaterialUnit: string;
    totalQty: number;
    excessQty:number,
    replaceableQty: number;
    rejectedQty: number;
    holdQty: number;
    supplierId: number;
    supplier: string;
    price: number;
    storageLocationId: Number | null;
    storageLocation: string | null;
    factoryGateId: number;
    factoryGate: string;
    purchaseInvoiceNumber: string;
    poNumber: string;
    purchasedBy: string;
    receivedAt: Date;
    createdAt: Date;
}


interface IAssignStorageToStockRequest {
    id: number;
    storageLocationId: number;
    updatedById: number;
}


export { IRawMaterialStock, ICreateRawMaterialStock, IReceiveRawMaterialStock, IRawMaterialStockDetails, IRawMaterialStockInDetails, IRawMaterialStockUpdateRequest, IAssignStorageToStockRequest, IRawMaterialStockDetailsWithPrice };