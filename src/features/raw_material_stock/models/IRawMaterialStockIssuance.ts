import { AuditData, InterfaceMetaData } from "../../../core/CoreInterfaces";
import { IRawMaterialDetails, ISingleRawMaterialDetails } from "../../raw_material/models/IRawMaterial";
import { NormalUserTable } from "../../users/sub_feaures/normal_user/database/NormalUserTable";
import { INormalUser, INormalUserResponse } from "../../users/sub_feaures/normal_user/models/INormalUser";



interface ICreateRawMaterialStockIssuance {
    rawMaterialId: number;
    qty: number;
    soNumber: string;
    issuedTo: number | null;
    notes: string | null;
    createdById: number;
    entryId:string;
}

interface IRawMaterialStockIssuance extends ICreateRawMaterialStockIssuance, InterfaceMetaData {

}

interface IRawMaterialStockIssuanceResponse   {
    entryId: string;
    soNumber: string;
    notes:string|null;
    issuedTo: INormalUserResponse|null;
    issuedBy: INormalUserResponse;
    rawMaterials: {
        rawMaterial: IRawMaterialDetails;
        qty: number;
    }[];
    issuedAt: Date;
}


export { IRawMaterialStockIssuance, ICreateRawMaterialStockIssuance, IRawMaterialStockIssuanceResponse };