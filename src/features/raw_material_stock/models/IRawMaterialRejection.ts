import { InterfaceMetaData } from "../../../core/CoreInterfaces";

interface ICreateRawMaterialRejection {
    rawMaterialId: number;
    purchaseInvoiceId: number;
    totalQty: number;
    rejectedQty: number;
    rejectionReason: string | null;
    rejectedById: Number | null;
    createdById: number;
}

interface IRawMaterialRejection extends ICreateRawMaterialRejection, InterfaceMetaData {

}

interface IRawMaterialRejectionResponse extends IRawMaterialRejection {
    rawMaterial: string;
    invoiceNumber: string;
    rejectedBy: string;
}



export { IRawMaterialRejection, ICreateRawMaterialRejection, IRawMaterialRejectionResponse };