import { InterfaceMetaData } from "../../../core/CoreInterfaces";

interface ICreateRawMaterialHold {
    rawMaterialId: number;
    purchaseInvoiceId: number;
    totalQty: number;
    holdQty: number;
    holdReason: string | null;
    holdById: Number | null;
    createdById: number;
}

interface IRawMaterialHold extends ICreateRawMaterialHold, InterfaceMetaData {

}

interface IRawMaterialHoldResponse extends IRawMaterialHold {
    rawMaterial: string;
    invoiceNumber: string;
    holdBy: string;
}



export { IRawMaterialHold, ICreateRawMaterialHold, IRawMaterialHoldResponse };