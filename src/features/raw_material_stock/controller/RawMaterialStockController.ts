import { NextFunction, Request, Response } from "express";
import { RepoProvider } from "../../../core/RepoProvider";
import { get } from "lodash";
import { IAssignStorageToStockRequest, IRawMaterialStockUpdateRequest } from "../models/IRawMaterialStock";
import { ICreateRawMaterialStockIssuance } from "../models/IRawMaterialStockIssuance";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";

export class RawMaterialStockController {


    static async getAll(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
            const result = await RepoProvider.rawMaterialStockRepo.getAll(page, pageSize, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }



    static async getById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.rawMaterialStockRepo.getById(Number(id), T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getByRawMaterialId(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.rawMaterialStockRepo.getByRawMaterialId(Number(id), T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getStockIn(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            let startDate: Date | undefined;
            let endDate: Date | undefined;

            const startDateString = get(req.query, "startDate") as string | undefined;
            const endDateString = get(req.query, "endDate") as string | undefined;

            if (startDateString && endDateString) {

                startDate = new Date(startDateString);
                endDate = new Date(endDateString);

                // const startDateData = startDateString.split("-");
                // const endDateData = endDateString.split("-");

                // startDate = new Date(Number(startDateData[0]), Number(startDateData[1]) - 1, Number(startDateData[2]));

                // endDate = new Date(Number(endDateData[0]), Number(endDateData[1]) - 1, Number(endDateData[2]));
            }



            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
            const text = get(req.query, "text") as string | undefined;

            const result = await RepoProvider.rawMaterialStockRepo.getStockIn(page, pageSize, T, text, startDate, endDate);

            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }

    }


    static async getStockInWithoutStorage(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));

            const result = await RepoProvider.rawMaterialStockRepo.getStockInWithoutStorage(page, pageSize, T);

            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }


    static async updateStock(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const id = Number(req.params.id);
            const userId = get(req, "user_id",);

            const payload: IRawMaterialStockUpdateRequest = {
                rawMaterialId: id,
                qty: Number(req.body.qty),
                updatedById: Number(userId!),
            };

            const result = await RepoProvider.rawMaterialStockRepo.updateStock(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not updated"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async assignStorageToStock(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const id = Number(req.params.id);
            const userId = get(req, "user_id",);

            const payload: IAssignStorageToStockRequest = {
                id: id,
                storageLocationId: Number(req.body.storageLocationId),
                updatedById: Number(userId!),
            };

            const result = await RepoProvider.rawMaterialStockRepo.assignStorageToStock(payload, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : stock not assign"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getStockInById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.rawMaterialStockRepo.getStockInById(Number(id), T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async issueStock(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const userId = get(req, "user_id");

            const stockIssuances = req.body;

            const soNumber = req.body.soNumber as string;
            const notes = req.body.notes as string | null;
            const issuedToUserId = req.body.issuedToUserId as number;


            const payload: ICreateRawMaterialStockIssuance[] = stockIssuances.rawMaterials.map((item: any) => ({
                soNumber: soNumber,
                issuedTo: issuedToUserId,
                notes: notes,
                rawMaterialId: Number(item.rawMaterialId),
                qty: Number(item.qty),
                entryId: "",
                createdById: Number(userId!),
            }));

            const result = await RepoProvider.rawMaterialStockRepo.issueStock(payload, T);
            if (!result.success) {
                res.status(500).send(result);
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not issue"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }


    static async getAllStockIssuance(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));
            const result = await RepoProvider.rawMaterialStockRepo.getAllStockIssuance(page, pageSize, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async getStockInByDateRange(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const startDateString = get(req.query, "startDate") as string;
            const endDateString = get(req.query, "endDate") as string;

            const startDateData = startDateString.split("-");
            const endDateData = endDateString.split("-");

            const startDate = new Date(Number(startDateData[0]), Number(startDateData[1]) - 1, Number(startDateData[2]));
            const endDate = new Date(Number(endDateData[0]), Number(endDateData[1]) - 1, Number(endDateData[2]));

            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));


            const result = await RepoProvider.rawMaterialStockRepo.getStockInByDateRange(
                startDate,
                endDate,
                page, pageSize, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async searchByRawMaterial(req: Request, res: Response) {
        const T = await sequelizeInit.transaction()
        try {
            const result = await RepoProvider.rawMaterialStockRepo.searchByRawMaterial(
                (req.query.rawMaterialName as string).toLowerCase(), T
            );
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async searchInStockByText(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const result = await RepoProvider.rawMaterialStockRepo.searchInStockByText(
                (req.query.text as string).toLowerCase(),
                true,
                T
            );
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async searchInStockByTextWithoutStorage(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const result = await RepoProvider.rawMaterialStockRepo.searchInStockByText(
                (req.query.text as string).toLowerCase(),
                false,
                T
            );
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async searchStockIssuanceByText(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const result = await RepoProvider.rawMaterialStockRepo.searchStockIssuanceByText(
                (req.query.text as string).toLowerCase(), T
            );
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async exportByCategory(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const categoryId = Number(get(req.params, "categoryId"));
            const result = await RepoProvider.rawMaterialStockRepo.exportByCategory(categoryId, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not export "
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    static async exportByCategoryWithPrice(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {

            const categoryId = Number(get(req.params, "categoryId"));
            const result = await RepoProvider.rawMaterialStockRepo.exportByCategoryWithPrice(categoryId, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not export "
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }


    static async getStockIssuanceById(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const id = get(req.params, "id");
            const result = await RepoProvider.rawMaterialStockRepo.getStockIssuanceById(id, T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }

    /**
     * Debug method for local testing & bug fixing
     * Returns stocks with inconsistent or negative values
     */
    static async getProblematicStocks(req: Request, res: Response, next: NextFunction) {
        const T = await sequelizeInit.transaction()
        try {
            const result = await RepoProvider.rawMaterialStockRepo.getProblematicStocks(T);
            if (!result.success) {
                throw new Error(result.message)
            }
            await T.commit()
            res.status(200).send(result);
        } catch (error) {
            await T.rollback()
            const msg = HelperMethods.isError(error) ? error.message : "Something went wrong : data not fetched"
            res.status(500).send(HelperMethods.getErrorResponse(msg))
        }
    }
}