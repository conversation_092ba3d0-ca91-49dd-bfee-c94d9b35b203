import * as express from "express";
import { RawMaterialStockValidations } from "../validations/RawMaterialStockValidations";
import { RawMaterialStockController } from "../controller/RawMaterialStockController";
import { permissionsMiddleware } from "../../../middlewares/permissionsMiddleware";
import { AppPermissions } from "../../users/sub_feaures/user_permissions/AppPermissions";
import { WorkingENV } from "../../../sequelize_init";

enum APP_ENV {
    dev = "dev",
    test = "test",
    prod = "prod",
}

const apiInitialPath = "/raw-materials-stock";
const rawMaterialStockRouter = express.Router();



rawMaterialStockRouter.put(apiInitialPath + "/update/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.UPDATE_CURRENT_STOCK),
    RawMaterialStockValidations.validateUpdate, RawMaterialStockController.updateStock);

rawMaterialStockRouter.get(apiInitialPath + "/stock-in",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_STOCK_IN),
    RawMaterialStockValidations.validateGetAll, RawMaterialStockController.getStockIn);

rawMaterialStockRouter.get(apiInitialPath + "/stock-in-not-assigned",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.ASSIGN_STORAGE),
    RawMaterialStockValidations.validateGetAll, RawMaterialStockController.getStockInWithoutStorage);

rawMaterialStockRouter.get(apiInitialPath + "/stock-in-date-range",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_STOCK_IN),
    RawMaterialStockValidations.validateGetByDateRange, RawMaterialStockController.getStockInByDateRange);


rawMaterialStockRouter.get(apiInitialPath + "/stock-in/searchByTextWithoutStorage",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.ASSIGN_STORAGE),
    RawMaterialStockValidations.validateSearchInStockByText, RawMaterialStockController.searchInStockByTextWithoutStorage);


rawMaterialStockRouter.get(apiInitialPath + "/stock-in/searchByText",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_STOCK_IN),
    RawMaterialStockValidations.validateSearchInStockByText, RawMaterialStockController.searchInStockByText);


rawMaterialStockRouter.get(apiInitialPath + "/stock-in/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.ASSIGN_STORAGE),
    RawMaterialStockValidations.validateGetStockInEntryById, RawMaterialStockController.getStockInById);

rawMaterialStockRouter.put(apiInitialPath + "/stock-in/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.ASSIGN_STORAGE),
    RawMaterialStockValidations.validateAssignStorageToStock, RawMaterialStockController.assignStorageToStock);

rawMaterialStockRouter.get(apiInitialPath + "/",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_CURRENT_STOCK),
    RawMaterialStockValidations.validateGetAll, RawMaterialStockController.getAll);

rawMaterialStockRouter.get(apiInitialPath + "/export-by-category/:categoryId",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_CURRENT_STOCK),
    RawMaterialStockValidations.validateExportByCategory, RawMaterialStockController.exportByCategory);

rawMaterialStockRouter.get(apiInitialPath + "/export-by-category/with-price/:categoryId",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_CURRENT_STOCK),
    RawMaterialStockValidations.validateExportByCategory, RawMaterialStockController.exportByCategoryWithPrice);

rawMaterialStockRouter.post(apiInitialPath + "/stock/issue",
    // permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.CREATE_ISSUANCE),
    RawMaterialStockValidations.validateIssueStock, RawMaterialStockController.issueStock);


rawMaterialStockRouter.get(apiInitialPath + "/stock/by-raw-material-id/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_CURRENT_STOCK),
    RawMaterialStockValidations.validateGetById, RawMaterialStockController.getByRawMaterialId);

rawMaterialStockRouter.get(apiInitialPath + "/stock/issuance",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_ISSUANCE),
    RawMaterialStockValidations.validateGetAll, RawMaterialStockController.getAllStockIssuance);

rawMaterialStockRouter.get(apiInitialPath + "/stock/issuance/searchByText",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_ISSUANCE),
    RawMaterialStockValidations.validateSearchInStockByText, RawMaterialStockController.searchStockIssuanceByText);

rawMaterialStockRouter.get(apiInitialPath + "/stock/search",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_CURRENT_STOCK),
    RawMaterialStockValidations.validateSearchByRawMaterial, RawMaterialStockController.searchByRawMaterial);


rawMaterialStockRouter.get(apiInitialPath + "/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_CURRENT_STOCK),
    RawMaterialStockValidations.validateGetById, RawMaterialStockController.getById);

rawMaterialStockRouter.get(apiInitialPath + "/stock/issuance/:id",
    permissionsMiddleware(AppPermissions.RAW_MATERIAL_STOCK.READ_ISSUANCE),
    RawMaterialStockValidations.validateGetById, RawMaterialStockController.getStockIssuanceById);

/**
 * Debug endpoint for local testing & bug fixing
 * Finds all stocks where:
 * - usableStock != totalStock (inconsistent values)
 * - totalStock < 0 (negative total stock)
 * - usableStock < 0 (negative usable stock)
 * 
 * Only works in development environment for security
 */
rawMaterialStockRouter.get(apiInitialPath + "/debug/problematic-stocks", RawMaterialStockController.getProblematicStocks);

export { rawMaterialStockRouter };