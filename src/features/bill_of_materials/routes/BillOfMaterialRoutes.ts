import * as express from "express";
import { BillOfMaterialController } from "../controller/BillOfMaterialController";
import BillOfMaterialValidations from "../validations/BillOfMaterialValidations";

const billOfMaterialRouter = express.Router();
const base_path = '/bom';

billOfMaterialRouter.post(`${base_path}`, BillOfMaterialValidations.validateCreate, BillOfMaterialController.create);
billOfMaterialRouter.put(`${base_path}/:id`, BillOfMaterialValidations.validateUpdate, BillOfMaterialController.update);
billOfMaterialRouter.delete(`${base_path}/delete/:id`, BillOfMaterialValidations.validateDelete, BillOfMaterialController.delete);
billOfMaterialRouter.get(`${base_path}/:id`, BillOfMaterialValidations.validateGetById, BillOfMaterialController.getById);

billOfMaterialRouter.get(`${base_path}`, BillOfMaterialValidations.validateList, BillOfMaterialController.getAll);

export default billOfMaterialRouter;
