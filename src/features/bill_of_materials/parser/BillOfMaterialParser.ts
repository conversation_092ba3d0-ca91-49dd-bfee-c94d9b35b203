// import { finalGoodsParser } from "../../final_goods/parser/finalGoodsParser";
import { parseUserToMetaUser } from "../../users/core/parser/core_user_parser";
import { ParsedBillOfMaterial ,BomList} from "../models/BillOfMaterial";
import { billOfRawMaterialParser } from "./BillOfRawMaterialParser";

const billOfMaterialParser = (bom: any): ParsedBillOfMaterial => {
    console.log(bom)
    const data: ParsedBillOfMaterial = {
        id: bom.id,
        finalGoodsVariation: bom.finalGoodsVariation,
        // rawMaterials: bom.rawMaterials.map((rawMaterial:any) => billOfRawMaterialParser(rawMaterial)),
        rawMaterialVariation: bom.rawMaterialVariation.map((variation: any) => {
            const obj = {
                id: variation.rawMaterialVariation.id,
                name: variation.rawMaterialVariation.id,
                qty: variation.qty,
            }
            return obj
        }),
        createdBy: parseUserToMetaUser(bom.createdByUser),
        updatedBy: bom.updatedByUser ? parseUserToMetaUser(bom.updatedByUser) : null,
        deletedBy: bom.deletedByUser ? parseUserToMetaUser(bom.deletedByUser) : null,
        createdAt: bom.createdAt,
        updatedAt: bom.updatedAt,
        deletedAt: bom.deletedAt
    }
    return data;
}


const billOfMaterialListParser = (bom: any): BomList => {
    console.log(bom)
    const data: BomList = {
        id: bom.id,
        finalGoodsVariation: bom.finalGoodsVariation,
        createdBy: parseUserToMetaUser(bom.createdByUser),
        createdAt: bom.createdAt,
        
    }
    return data;
}
export { billOfMaterialParser, billOfMaterialListParser };