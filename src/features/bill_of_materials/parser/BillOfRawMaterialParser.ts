import { parseUserToMetaUser } from "../../users/core/parser/core_user_parser";
import { ParsedBillOfMaterialRawMaterial } from "../models/BillOfMaterial";

const billOfRawMaterialParser = (rawMaterial: any): ParsedBillOfMaterialRawMaterial => {
    const data: ParsedBillOfMaterialRawMaterial = {
        id: rawMaterial.id,
        bomId: rawMaterial.bomId,
        rawMaterialVariationId: rawMaterial.rawMaterialId,
        qty: rawMaterial.qty,
        createdBy: parseUserToMetaUser(rawMaterial.createdByUser),
        updatedBy: rawMaterial.updatedByUser ? parseUserToMetaUser(rawMaterial.updatedByUser) : null,
        deletedBy: rawMaterial.deletedByUser ? parseUserToMetaUser(rawMaterial.deletedByUser) : null,
        createdAt: rawMaterial.createdAt,
        updatedAt: rawMaterial.updatedAt,
        deletedAt: rawMaterial.deletedAt
    }
    return data;
}

export { billOfRawMaterialParser };