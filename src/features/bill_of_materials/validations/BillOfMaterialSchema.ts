import { z } from "zod";

// Raw Material Payload Schema with custom messages
export const BillOfMaterialRawMaterialPayloadSchema = z.object({
    rawMaterialVariationId: z
        .number({ required_error: "rawMaterialVariationId is required", invalid_type_error: "rawMaterialVariationId must be a number" })
        .int("rawMaterialVariationId must be an integer")
        .positive("rawMaterialVariationId must be a positive number"),

    qty: z
        .number({ required_error: "qty is required", invalid_type_error: "qty must be a number" })
        .positive("qty must be a positive number"),
});


class BillOfMaterialValidationSchema {

    static create = z.object({
        finalGoodsVariationId: z
            .number({ required_error: "finalGoodsVariationId is required", invalid_type_error: "finalGoodsVariationId must be a number" })
            .int("finalGoodsVariationId must be an integer")
            .positive("finalGoodsVariationId must be a positive number"),

        rawMaterials: z
            .array(BillOfMaterialRawMaterialPayloadSchema, {
                required_error: "rawMaterials is required",
                invalid_type_error: "rawMaterials must be an array",
            })
            .min(1, "At least one raw material is required"),
    });

    static update = z.object({
        id: z.string().transform(Number).refine(val => !isNaN(val) && val > 0, {
            message: "Param value must be a positive number",
        }),
        rawMaterials: z
            .array(BillOfMaterialRawMaterialPayloadSchema, {
                required_error: "rawMaterials is required",
                invalid_type_error: "rawMaterials must be an array",
            })
            .min(1, "At least one raw material is required"),
    });

    static getById = z.object({
        id: z
            .number({ required_error: "id is required", invalid_type_error: "id must be a number" })
            .int("id must be an integer")
            .positive("id must be a positive number"),
    });

    static delete = z.object({
        id: z.string().transform(Number).refine(val => !isNaN(val) && val > 0, {
            message: "Param value must be a positive number",
        }),
    });

    static getAll = z.object({
        page: z
            .string()
            .transform(val => Number(val || "1"))
            .refine(val => !isNaN(val) && val > 0, {
                message: "Page must be a positive number"
            }),

        pageSize: z
            .string()
            .transform(val => Number(val || "10"))
            .refine(val => !isNaN(val) && val > 0, {
                message: "Page size must be a positive number"
            }),
        search:z.string({
            required_error: "search is required",
            invalid_type_error: "search must be a string"
        }).optional().default(""),
    });
}



export {
    BillOfMaterialValidationSchema,

}
