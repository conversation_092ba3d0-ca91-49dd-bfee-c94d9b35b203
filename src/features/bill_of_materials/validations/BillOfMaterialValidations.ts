import { NextFunction, Request, Response } from "express";
import { HelperMethods } from "../../../core/HelperMethods";
import { BillOfMaterialValidationSchema } from "./BillOfMaterialSchema";
import { CoreSchemas } from "../../../core/CoreSchemas";

class BillOfMaterialValidations {
    static validateCreate = (req: Request, res: Response, next: NextFunction) => {

        const result = BillOfMaterialValidationSchema.create.safeParse(req.body);

        if (!result.success) {

            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0].toString() + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }
        return next();
    }
    static validateGetById = (req: Request, res: Response, next: NextFunction) => {

        const result = CoreSchemas.getByIdSchema.safeParse(req.params);

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }

    static validateDelete = (req: Request, res: Response, next: NextFunction) => {

        const result = BillOfMaterialValidationSchema.delete.safeParse( req.params );

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }


    static validateList = (req: Request, res: Response, next: NextFunction) => {

        console.log(req.query);
        const result = BillOfMaterialValidationSchema.getAll.safeParse({ ...req.query, ...req.body, });

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }
    


    static validateUpdate = (req: Request, res: Response, next: NextFunction) => {
        const result = BillOfMaterialValidationSchema.update.safeParse({ ...req.params, ...req.body,...req.query });

        if (!result.success) {
            let errorMessage = "";
            if (result.error.errors[0].message === "Required") {
                errorMessage = result.error.errors[0].path[0] + " is required";
            }
            else {
                errorMessage = result.error.errors[0].message;
            }

            res.status(400).send(HelperMethods.getErrorResponse(errorMessage));
            return;
        }

        return next();
    }
}

export default BillOfMaterialValidations