import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { BillOfMaterialAttributes, BillOfMaterialPayload, IBomGetSingleResponse, ParsedBillOfMaterial } from "../models/BillOfMaterial";

interface IBillOfMaterialRepo {

    create(payload: BillOfMaterialPayload, userId: number, transaction: Transaction): Promise<APIBaseResponse<null>>

    update(id: number, payload: BillOfMaterialPayload, userId: number, transaction?: Transaction): Promise<APIBaseResponse<null>>

    getById(id: number, transaction?: Transaction): Promise<APIBaseResponse<IBomGetSingleResponse | null>>

    getAll(page: number, pageSize: number, transaction?: Transaction,search?:string): Promise<APIBaseResponse<PaginatedBaseResponse<ParsedBillOfMaterial>|null>>

    delete(id: number, userId: number, transaction?: Transaction): Promise<APIBaseResponse<ParsedBillOfMaterial | null>>
}

export { IBillOfMaterialRepo }