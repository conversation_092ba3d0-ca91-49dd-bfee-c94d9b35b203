import { Transaction, UniqueConstraintError } from "sequelize";
import { BillOfMaterialPayload, BillOfMaterialUpdatePayload, IBomGetSingleResponse, ParsedBillOfMaterial } from "../models/BillOfMaterial";
import { IBillOfMaterialRepo } from "./IBillOfMaterialRepo";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { BillOfMaterialTable } from "../database/BillOfMaterialTable";
import { BillOfMaterialRawMaterialTable } from "../database/BillOfMaterialRawMaterialTable";
import { RawMaterialVariationTable } from "../../raw_material/database/RawMaterialVariationTable";
import { PaginationProvider } from "../../pagination/PaginationProvider";
import { billOfMaterialParser, billOfMaterialListParser } from "../parser/BillOfMaterialParser";
import { Op } from "sequelize";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { FinalGoodsVariationTable } from "../../final_goods/model/finalGoodsVariationTable";

class BillOfMaterialRepo implements IBillOfMaterialRepo {

    // Implement the methods defined in the IBillOfMaterialRepo interface
    async create(payload: BillOfMaterialPayload, userId: number, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            const bom = await BillOfMaterialTable.create({
                finalGoodsVariationId: payload.finalGoodsVariationId,
                createdBy: userId,
            }, { transaction, userId: userId });
            if (!bom) {
                return HelperMethods.getErrorResponse("Failed to create Bill of Material");
            }
            const rawMaterials = payload.rawMaterials.map(rawMaterial => ({
                bomId: bom.toJSON().id,
                rawMaterialVariationId: rawMaterial.rawMaterialVariationId,
                qty: rawMaterial.qty,
                createdBy: userId,
            }));

            const createdBOMRawMaterials = await BillOfMaterialRawMaterialTable.bulkCreate(rawMaterials, { transaction, userId: (userId) });

            if (!createdBOMRawMaterials || createdBOMRawMaterials.length === 0) {
                return HelperMethods.getErrorResponse("Failed to create Bill of Material Raw Materials");
            }

            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                console.log(error);

                return HelperMethods.getErrorResponse("BOM for this Final Good is  already exists");
            } if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message)
            } else {
                return HelperMethods.getErrorResponse();
            }
        }
    }

    async update(id: number, payload: BillOfMaterialUpdatePayload, userId: number, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            const bom = await BillOfMaterialTable.findByPk(id, { transaction });

            if (!bom) {
                return HelperMethods.getErrorResponse("Bill of Material not found");
            }
            console.log("bom", bom)

            // bom.set({ updatedBy: userId });
            bom.dataValues.updatedBy = userId;
            // bom.finalGoodsVariationId = payload.finalGoodsVariationId;
            await bom.save({ transaction });

            // Delete old raw materials

            await BillOfMaterialRawMaterialTable.destroy({ where: { bomId: bom.dataValues.id }, transaction, userId: (userId) });

            // Update raw materials
            const rawMaterials = payload.rawMaterials.map(rawMaterial => ({
                bomId: id,
                rawMaterialVariationId: rawMaterial.rawMaterialVariationId,
                qty: rawMaterial.qty,
                createdBy: userId,
            }));

            await BillOfMaterialRawMaterialTable.bulkCreate(rawMaterials, { transaction, userId: (userId) });

            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);

            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse("Unique constraint error");
            } else if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message);
            } else {
                return HelperMethods.getErrorResponse();
            }
        }
    }

    async getById(id: number, transaction: Transaction): Promise<APIBaseResponse<IBomGetSingleResponse | null>> {
        try {
            const bom = await BillOfMaterialTable.findByPk(id, {
                include: [
                    {
                        model: FinalGoodsVariationTable,
                        as: 'finalGoodsVariation'
                    },
                    {
                        model: BillOfMaterialRawMaterialTable,
                        as: 'rawMaterialVariation',
                        attributes: ['qty'],
                        include: [
                            {
                                as: 'rawMaterialVariation',
                                model: RawMaterialVariationTable,
                            }
                        ]
                    }
                ],
                transaction
            });

            if (!bom) {
                return HelperMethods.getErrorResponse("Bill of Material not found");
            }
            const bomData = bom.toJSON() as any
            const newBom: IBomGetSingleResponse = {
                id: bomData.id,
                finalGoodsVariation: bomData.finalGoodsVariation,
                // finalGoods: bomData.finalGoods,
                rawMaterialVariations: bomData.rawMaterialVariation.map((variation: any) => {
                    const obj = {
                        id: Number(variation.rawMaterialVariation.id),
                        name: variation.rawMaterialVariation.name,
                        qty: variation.qty,
                        rawMaterialVariation: {
                            ...variation.rawMaterialVariation,
                            id: Number(variation.rawMaterialVariation.id),
                        },
                    }
                    return obj
                }),
                createdAt: bomData.createdAt,
            }

            return HelperMethods.getSuccessResponse(newBom);
        } catch (error) {
            HelperMethods.handleError(error);

            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message);
            } else {
                return HelperMethods.getErrorResponse();
            }
        }
    }
    async getAll(page: number, pageSize: number, transaction: Transaction, search: string): Promise<APIBaseResponse<PaginatedBaseResponse<ParsedBillOfMaterial> | null>> {
        try {
            const whereCondition = search
                ? {
                    [Op.or]: [
                        { '$finalGoodsVariation.name$': { [Op.iLike]: `%${search}%` } },
                        { '$finalGoodsVariation.sku$': { [Op.iLike]: `%${search}%` } },
                    ]
                }
                : undefined;

            const response = await new PaginationProvider<any, ParsedBillOfMaterial>().getPaginatedRecords(
                BillOfMaterialTable,
                {
                    where: whereCondition,
                    include: [
                        {
                            model: FinalGoodsVariationTable,
                            as: 'finalGoodsVariation', // eager-load final goods variation
                            attributes: ['id', 'name', 'sku'], // include necessary fields for search
                            required: search ? true : false, // Use inner join when searching to ensure association exists
                        },
                        {
                            model: CoreUserTable,
                            as: 'createdByUser',
                            attributes: ['id', 'firstName', 'lastName'],
                        }
                    ],
                    limit: pageSize,
                    page: page,
                    dateColumn: 'createdAt',
                },
                transaction,
                billOfMaterialListParser
            );

            // if (!response || response.rows.length === 0) {
                
            //     return HelperMethods.getErrorResponse("No Bill of Materials found");
            // }
            return HelperMethods.getSuccessResponse({
                totalData: response.total,
                currentPage: response.currentPage,
                totalPages: response.totalPages,
                data: response.rows,
            });
        } catch (error) {
            HelperMethods.handleError(error);

            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message);
            } else {
                return HelperMethods.getErrorResponse();
            }
        }
    }

    async delete(id: number, userId: number, transaction: Transaction): Promise<APIBaseResponse<ParsedBillOfMaterial | null>> {
        try {
            const bom = await BillOfMaterialTable.findByPk(id, {
                transaction
            });
            console.log("bom", bom)
            if (!bom) {
                return HelperMethods.getErrorResponse("Bill of Material not found");
            }

            await BillOfMaterialTable.destroy({
                where: {
                    id: id,
                },
                transaction,
                userId: userId,
            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);

            if (error instanceof Error) {
                return HelperMethods.getErrorResponse(error.message);
            } else {
                return HelperMethods.getErrorResponse();
            }
        }
    }
}

export { BillOfMaterialRepo };