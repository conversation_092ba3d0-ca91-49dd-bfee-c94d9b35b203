import { DataTypes, Model } from "sequelize";
import { BillOfMaterialRawMaterialTableAttributes, ICreateBillOfMaterialRawMaterialTableAttributes } from "../models/BillOfMaterial";
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from "../../../core/RepoProvider";

class BillOfMaterialRawMaterialTable extends Model<BillOfMaterialRawMaterialTableAttributes, ICreateBillOfMaterialRawMaterialTableAttributes>{}

BillOfMaterialRawMaterialTable.init({
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    },
    bomId: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    rawMaterialVariationId: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    qty: {
        type: DataTypes.FLOAT,
        allowNull: false,
    },
    createdBy: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    updatedBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    deletedBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: "updated_at",
    },
    deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: null,
        field: "deleted_at",
    },
}, {
    sequelize: sequelizeInit,
    tableName: 'bill_of_material_raw_materials',
    paranoid: true,
});

BillOfMaterialRawMaterialTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "BillOfMaterialRawMaterial",
        instance,
        options
    );
});

BillOfMaterialRawMaterialTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "BillOfMaterialRawMaterial",
        instance,
        options
    );
});

BillOfMaterialRawMaterialTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "BillOfMaterialRawMaterial",
        instance,
        options
    );
});

export { BillOfMaterialRawMaterialTable };