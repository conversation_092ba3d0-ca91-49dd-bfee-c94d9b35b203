import { DataTypes, Model } from "sequelize";
import { BillOfMaterialAttributes, ICreateBillOfMaterialAttributes } from "../models/BillOfMaterial";
import { sequelizeInit } from "../../../sequelize_init";
import { RepoProvider } from "../../../core/RepoProvider";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { FinalGoodsTable  } from "../../final_goods/model/finalGoodsTable";
import {FinalGoodsVariationTable} from "../../final_goods/model/finalGoodsVariationTable";

class BillOfMaterialTable extends Model<BillOfMaterialAttributes, ICreateBillOfMaterialAttributes>{
    public static getInclude() {
        return [
            {   model: FinalGoodsVariationTable,
                as: "finalGoodsVariation",
                include: [
                    { as: 'createdByUser', model: CoreUserTable },
                    { as: 'updatedByUser', model: CoreUserTable },
                    { as: 'deletedByUser', model: CoreUserTable },
                ]
            }
        ];
    }
}

BillOfMaterialTable.init({
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    },
    finalGoodsVariationId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique:true,
    },
    createdBy: {
            type: DataTypes.INTEGER,
            allowNull: false,
    },
    updatedBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    deletedBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: "updated_at",
    },
    deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: null,
        field: "deleted_at",
    },
},{
    sequelize: sequelizeInit,
    tableName: 'bill_of_materials',
    timestamps: true,
    paranoid: true,
    // underscored: true,
})

BillOfMaterialTable.addHook("afterCreate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "create",
        "BillOfMaterial",
        instance,
        options
    );
});

BillOfMaterialTable.addHook("afterUpdate", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "update",
        "BillOfMaterial",
        instance,
        options
    );
});

BillOfMaterialTable.addHook("afterDestroy", async (instance, options) => {
    await RepoProvider.logRepo.logModelAction(
        "delete",
        "BillOfMaterial",
        instance,
        options
    );
});

export { BillOfMaterialTable }