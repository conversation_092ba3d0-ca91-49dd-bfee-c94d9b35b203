import { Transaction } from "sequelize";
import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { IExcelProcessorRequest, IExcelProcessorResponse } from "../models/IExcelProcessor";

export interface IExcelProcessorRepo {
  processExcel(excelProcessor: IExcelProcessorRequest, transaction: Transaction): Promise<APIBaseResponse<IExcelProcessorResponse | null>>;
  getAll(page: number, pageSize: number,transaction: Transaction, text?: string, startDate?: Date, endDate?: Date,): Promise<APIBaseResponse<PaginatedBaseResponse<IExcelProcessorResponse> | null>>;
  getById(id: number, transaction: Transaction): Promise<APIBaseResponse<IExcelProcessorResponse | null>>;
}
