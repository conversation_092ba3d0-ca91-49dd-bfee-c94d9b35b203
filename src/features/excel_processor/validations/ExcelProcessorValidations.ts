import { NextFunction, Request, Response } from "express";
import { z } from "zod";
import { HelperMethods } from "../../../core/HelperMethods";

export class ExcelProcessorValidations {
  static validateProcessExcel(req: Request, res: Response, next: NextFunction) {
    next();
  }

  // static validateGetAll(req: Request, res: Response, next: NextFunction) {
  //   try {
  //     const schema = z.object({
  //       page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  //       pageSize: z.string().optional().transform(val => val ? parseInt(val) : 10),
  //       text: z.string().optional(),
  //       startDate: z.string().optional(),
  //       endDate: z.string().optional(),
  //     });

  //     const result = schema.safeParse(req.query);

  //     if (!result.success) {
  //       res.status(400).send(HelperMethods.getErrorResponse("Invalid query parameters"));
  //       return;
  //     }

  //     next();
  //   } catch (error) {
  //     res.status(400).send(HelperMethods.getErrorResponse("Invalid query parameters"));
  //   }
  // }

  // static validateId(req: Request, res: Response, next: NextFunction) {
  //   try {
  //     const schema = z.object({
  //       id: z.string().transform(val => parseInt(val)),
  //     });

  //     const result = schema.safeParse(req.params);

  //     if (!result.success) {
  //       res.status(400).send(HelperMethods.getErrorResponse("Invalid ID parameter"));
  //       return;
  //     }

  //     next();
  //   } catch (error) {
  //     res.status(400).send(HelperMethods.getErrorResponse("Invalid ID parameter"));
  //   }
  // }
}
