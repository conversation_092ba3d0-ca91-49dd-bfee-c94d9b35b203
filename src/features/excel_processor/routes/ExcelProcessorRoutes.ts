import * as express from 'express';
import { ExcelProcessorValidations } from '../validations/ExcelProcessorValidations';
import { ExcelProcessorController } from '../controller/ExcelProcessorController';
import os from 'os';
import multer from 'multer';
import * as path from 'path';

const apiInitialPath = '/excel-processor';
const excelProcessorRouter = express.Router();

const storage = multer.diskStorage({
  destination: function (_req, _file, cb) {
    cb(null, path.join(os.tmpdir()));
  },
  filename: function (_req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const fileFilter = (_req: Express.Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  
  if (
    file.mimetype === 'application/vnd.ms-excel' ||
    file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ) {
    cb(null, true);
  } else {
    cb(null, false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 1024 * 1024 * 5 
  }
});


excelProcessorRouter.post(
  apiInitialPath + '/process',
  // permissionsMiddleware(AppPermissions.EXCEL_PROCESSOR.PROCESS),
  upload.single('excelFile'),
  (req, res, next) => {
    if (!req.file) {
      console.log('No files were uploaded.');
    }
    console.log("File received:", req.file);
    next();
  },
  ExcelProcessorValidations.validateProcessExcel,
  ExcelProcessorController.processExcel
);

// excelProcessorRouter.get(
//   apiInitialPath + '/',
//   permissionsMiddleware(AppPermissions.EXCEL_PROCESSOR.READ),
//   ExcelProcessorValidations.validateGetAll,
//   ExcelProcessorController.getAll
// );

// excelProcessorRouter.get(
//   apiInitialPath + '/:id',
//   permissionsMiddleware(AppPermissions.EXCEL_PROCESSOR.READ),
//   ExcelProcessorValidations.validateId,
//   ExcelProcessorController.getById
// );

export { excelProcessorRouter };
