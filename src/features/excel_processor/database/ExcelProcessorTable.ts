import { DataTypes, Model } from "sequelize";
import { sequelizeInit } from "../../../sequelize_init";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";

export class ExcelProcessorTable extends Model {
  declare id: Number;
  declare fileName: string;
  declare processedAt: Date;
  declare createdById: Number;
  declare createdAt: Date;
  declare updatedAt: Date;
  declare createdBy: CoreUserTable;
}

ExcelProcessorTable.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    processedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    createdById: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize: sequelizeInit,
    tableName: "excel_processors",
    timestamps: true,
  }
);
