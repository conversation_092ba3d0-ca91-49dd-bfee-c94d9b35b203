import { NextFunction, Request, Response } from "express";
import { z } from 'zod';
import { HelperMethods } from "../../../core/HelperMethods";
import { extractAllZodErrors } from "../../../core/utils";
import { CoreSchemas } from "../../../core/CoreSchemas";

// Create Brand Schema
export const validateCreateBrandSchema = z.object({
  title: z.string().min(2, "Title must be at least 2 characters").max(50, "Title must be at most 50 characters"),
  description: z.string().max(255, "Description must be at most 255 characters"),
  logo: z.string().min(2, "Logo must be at least 2 characters"),
  whiteListedDomains: z
    .array(
      z.string()
        .min(1, "Domain must not be empty")
        .regex(
          /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
          "Invalid domain format"
        )
    )
    .min(1, "At least one domain is required"),
});

// Update Brand Schema
export const validateUpdateBrandSchema = z.object({
  title: z.string().min(2).max(50).optional(),
  description: z.string().max(255).optional(),
  logo: z.string().min(2).optional(),
}).refine((data) => Object.keys(data).length > 0, {
  message: "At least one field must be provided for update",
});

// Delete Brand Schema
export const validateDeleteBrandSchema = z.object({
  id: z.string().min(1, "ID is required"),
});

// Get Brand Schema
export const validateBrandIdSchema = z.object({
  id: z.string().min(1, "ID is required"),
});

export const validateCreateBrand = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const result = validateCreateBrandSchema.safeParse(req.body);

  if (!result.success) {
    res.status(400).send(HelperMethods.ValidationError(extractAllZodErrors(result.error)));
    return;
  }
  return next();
}

export const validateBrandId = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const result = validateBrandIdSchema.safeParse(req.params);

  if (!result.success) {
    res.status(400).send(HelperMethods.ValidationError(extractAllZodErrors(result.error)));
    return;
  }
  return next();
}

export const validateUpdateBrand = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const idRes = CoreSchemas.updateByIdSchema.safeParse(req.params)

  if (!idRes.success) {
    res.status(400).send(HelperMethods.ValidationError(extractAllZodErrors(idRes.error)));
    return;
  }


  const result = validateUpdateBrandSchema.safeParse(req.body);
  if (!result.success) {
    res.status(400).send(HelperMethods.ValidationError(extractAllZodErrors(result.error)));
    return;
  }
  return next();
}

export const validateDeleteBrand = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const result = validateBrandIdSchema.safeParse(req.params);

  if (!result.success) {
    res.status(400).send(HelperMethods.ValidationError(extractAllZodErrors(result.error)));
    return;
  }
  return next();
};
