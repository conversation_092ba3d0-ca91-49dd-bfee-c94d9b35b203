import { ERROR_MESSAGE } from "../../../core/constants";
import { RepoProvider } from "../../../core/RepoProvider";
import { BrandPayload } from "../interface/brand";
import { sequelizeInit } from "../../../sequelize_init";
import { Request, Response } from "express";
import { HelperMethods } from "../../../core/HelperMethods";
import { get } from "lodash";

export class BrandController {

    async create(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const user_id =get(req, "user_id")!;
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }
            const payload: BrandPayload = req.body;
            const brand = await RepoProvider.brandRepo.createBrand(payload, user_id, transaction);
            console.log(brand)
            if (!brand.success) {
                throw new Error(brand.message)
            }
            await transaction.commit();
            res.status(201).send(brand);
        } catch (error) {
            await transaction.rollback();

            HelperMethods.handleError(error)
            res.status(500).send(HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    async get(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const brandId = req.params.id;
            const brand = await RepoProvider.brandRepo.getBrand(+brandId, transaction);
            if (!brand.success) {
                throw new Error(brand.message)
            }
            await transaction.commit();
            res.status(200).send(brand);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    async getAll(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const brands = await RepoProvider.brandRepo.getAllBrands(transaction);
            if (!brands.success) {
                throw new Error(brands.message)
            }
            await transaction.commit();
            res.status(200).send(brands);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    async update(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const user_id = get(req, "user_id")!;
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }
            const brandId = req.params.id;
            const payload: BrandPayload = req.body;
            const updatedBrand = await RepoProvider.brandRepo.updateBrand(+brandId, payload, user_id, transaction);
            if (!updatedBrand.success) {
                throw new Error(updatedBrand.message)
            }
            await transaction.commit();
            res.status(200).send(updatedBrand);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }

    async delete(req: Request, res: Response) {
        const transaction = await sequelizeInit.transaction();
        try {
            const user_id =get(req, "user_id")!;
            if (!user_id || isNaN(user_id)) {
                throw new Error(ERROR_MESSAGE.USER_ID_SHOULD_BE_NUMBER)
            }
            const brandId = req.params.id;
            const deleted = await RepoProvider.brandRepo.deleteBrand(+brandId, user_id, transaction);
            if (!deleted.success) {
                throw new Error(deleted.message)
            }
            await transaction.commit();
            res.status(200).send(deleted);
        } catch (error) {
            await transaction.rollback();
            HelperMethods.handleError(error)
            res.status(500).send(HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : ERROR_MESSAGE.INTERNAL_SERVER_ERROR));
        }
    }


}

