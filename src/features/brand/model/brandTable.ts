import { Optional, Model, DataTypes } from "sequelize";
import { RepoProvider } from "../../../core/RepoProvider";
import { IBrandCreationAttributes, IBrandTable } from "../interface/brand";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { sequelizeInit } from "../../../sequelize_init";




class BrandTableModel extends Model<IBrandTable, IBrandCreationAttributes>  {
    public id!: number;
    public title!: string;
    public description!: string;
    public logo!: string;
    public whiteListedDomains!: string[];
    public createdById!: number;
    public updatedById!: number | null;
    public deletedById!: number | null;
    public createdAt!: Date;
    public updatedAt!: Date | null;
    public deletedAt!: Date | null;

    public static associate() {
        BrandTableModel.belongsTo(CoreUserTable,
            { as: 'createdByUser', foreignKey: 'createdById' })
        BrandTableModel.belongsTo(CoreUserTable,
            { as: 'updatedByUser', foreignKey: 'updatedById' })
        BrandTableModel.belongsTo(CoreUserTable,
            { as: 'deletedByUser', foreignKey: 'deletedById' })
    }

    public static initModel() {
        BrandTableModel.init(
            {
                id: {
                    type: DataTypes.INTEGER,
                    autoIncrement: true,
                    primaryKey: true,
                },
                title: {
                    type: DataTypes.STRING,
                    allowNull: false,
                },
                description: {
                    type: DataTypes.STRING,
                    allowNull: false,
                },
                logo: {
                    type: DataTypes.STRING,
                    allowNull: true,
                },
                whiteListedDomains: {
                    type: DataTypes.JSONB,
                    allowNull: false,
                },
                createdById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                updatedById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                deletedById: {
                    type: DataTypes.INTEGER,
                    allowNull: true,
                },
                createdAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                    defaultValue: DataTypes.NOW,
                    field: "created_at",
                },
                updatedAt: {
                    type: DataTypes.DATE,
                    allowNull: false,
                    defaultValue: DataTypes.NOW,
                    field: "updated_at",
                },
                deletedAt: {
                    type: DataTypes.DATE,
                    allowNull: true,
                    defaultValue: null,
                    field: "deleted_at",
                },
            },
            {
                sequelize: sequelizeInit,
                modelName: "Brand",
                tableName: "brands",
                underscored: true,
                timestamps: true,
            }
        );
        return BrandTableModel;
    }

    public static hook() {
        BrandTableModel.addHook("afterCreate", async (instance, options) => {
            try {
                await RepoProvider.logRepo.logModelAction(
                    "create",
                    "Brand",
                    instance,
                    { ...options, transaction: options.transaction }
                );
            } catch (error) {
                console.error("Error in Brand afterCreate hook:", error);
                // Don't throw the error to prevent transaction rollback
            }
        });

        // After Update Hook - Log the updated fields of the Brand
        BrandTableModel.addHook("afterUpdate", async (instance, options) => {
            try {
                await RepoProvider.logRepo.logModelAction(
                    "update",
                    "Brand",
                    instance,
                    { ...options, transaction: options.transaction }
                );
            } catch (error) {
                console.error("Error in Brand afterUpdate hook:", error);
                // Don't throw the error to prevent transaction rollback
            }
        });

        // After Destroy Hook - Log the deletion of the Brand
        BrandTableModel.addHook("afterDestroy", async (instance, options) => {
            try {
                await RepoProvider.logRepo.logModelAction(
                    "delete",
                    "Brand",
                    instance,
                    { ...options, transaction: options.transaction }
                );
            } catch (error) {
                console.error("Error in Brand afterDestroy hook:", error);
                // Don't throw the error to prevent transaction rollback
            }
        });
    }

}
export {
    BrandTableModel
}

