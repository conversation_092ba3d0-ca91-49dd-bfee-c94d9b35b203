import { APIBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";
import { BrandPayload, ParsedBrand } from "../interface/brand";
import { BrandTableModel } from "../model/brandTable";
import parsedBrand from "../parser/brandParser";
import { IBrandRepo } from "./IBrandRepo";
import { Transaction } from "sequelize";

export class BrandRepo implements IBrandRepo {
    async createBrand(brand: BrandPayload, userId: number, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            
            const isBrandExists = await BrandTableModel.findOne({
                where: {
                    title: brand.title,
                },
                transaction
            });
            if (isBrandExists) {
                throw new Error(`Brand already exists of this (${brand.title}) name `)
            }

            await BrandTableModel.create({
                title: brand.title,
                description: brand.description,
                logo: brand.logo,
                whiteListedDomains: brand.whiteListedDomains,
                createdById: userId,
            }, { transaction, userId: userId });

            return HelperMethods.getSuccessResponse(null, 'Brand successfully created.')
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not created.')
        }
    }

    async getAllBrands(transaction: Transaction): Promise<APIBaseResponse<ParsedBrand[] | null>> {
        try {
            const allBrands = await BrandTableModel.findAll({
                include: [
                    {
                        model: CoreUserTable,
                        as: "createdByUser",
                        attributes: ["id", "firstName", "lastName", "email"],
                    },
                    {
                        model: CoreUserTable,
                        as: "updatedByUser",
                        attributes: ["id", "firstName", "lastName", "email"],
                    },
                    {
                        model: CoreUserTable,
                        as: "deletedByUser",
                        attributes: ["id", "firstName", "lastName", "email"],
                    },
                ],
                transaction
            })
            if (allBrands.length === 0) {
                throw new Error('Data not exists')
            }
            return HelperMethods.getSuccessResponse(allBrands.map((brand) => parsedBrand(brand.toJSON())), 'Data fetched successfully')
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not fetched.')
        }
    }

    async getBrand(id: number, transaction: Transaction): Promise<APIBaseResponse<ParsedBrand | null>> {
        try {
            const allBrands = await BrandTableModel.findOne({
                where: {
                    id: id,
                },
                include: [
                    {
                        model: CoreUserTable,
                        as: "createdByUser",
                        attributes: ["id", "firstName", "lastName", "email"],
                    },
                    {
                        model: CoreUserTable,
                        as: "updatedByUser",
                        attributes: ["id", "firstName", "lastName", "email"],
                    },
                    {
                        model: CoreUserTable,
                        as: "deletedByUser",
                        attributes: ["id", "firstName", "lastName", "email"],
                    },
                ],
                transaction
            })

            if (!allBrands) {
                throw new Error('Data not exists')
            }
            return HelperMethods.getSuccessResponse( parsedBrand(allBrands.toJSON()), 'Data fetched successfully')
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not fetched.')
        }
    }

    async updateBrand(id: number, brand: BrandPayload, userId: number, transaction: Transaction): Promise<APIBaseResponse<null>> {
        try {
            const isBrandExists = await BrandTableModel.findByPk(id, { transaction });
            if (!isBrandExists) {
                throw new Error("Brand not found");
            }
            const updatedBrand = await BrandTableModel.update({
                title: brand.title,
                description: brand.description,
                logo: brand.logo,
                updatedById: userId
            }, {
                where: {
                    id: id,
                },
                returning: true,
                transaction,
                userId
            });
            return HelperMethods.getSuccessResponse(null)
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not update.')
        }
    }

    async deleteBrand(id: number, userId: number, transaction:Transaction): Promise<APIBaseResponse<null>> {
        try {
            const isBrandExists = await BrandTableModel.findByPk(id, { transaction });
            if (!isBrandExists) {
                throw new Error("Brand not found");
            }
            const deletedBrand = await BrandTableModel.destroy({
                where: {
                    id: id,
                },
                transaction,
                userId: userId
            });
            if (deletedBrand === 0) {
                throw new Error(`Data not deleted of this id (${id})`)
            }
            return HelperMethods.getSuccessResponse(null, `Data successfully deleted of this id (${id})`)
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not delete.')
        }
    }

    async getBrandData(arrayIds: number[], currentBrandId: number ,transaction:Transaction): Promise<APIBaseResponse<ParsedBrand | null>> {
        try {
            const currentBrand = arrayIds.find((id) => id === currentBrandId);

            const brandData = await BrandTableModel.findOne({ where: { id: currentBrand },transaction })
            if (!brandData) {
                throw new Error('Data not exists')
            }
            return HelperMethods.getSuccessResponse(parsedBrand(brandData), 'Data fetched successfully')
        } catch (error) {
            return HelperMethods.getErrorResponse(HelperMethods.isError(error) ? error.message : 'Somthing went wrong : brand not fetched.')
        }

    }
}
