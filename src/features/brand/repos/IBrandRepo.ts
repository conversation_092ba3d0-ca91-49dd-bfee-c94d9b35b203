import { Transaction } from "sequelize";
import { BrandPayload, ParsedBrand } from "../interface/brand";
import { APIBaseResponse } from "../../../core/CoreInterfaces";

export interface IBrandRepo {
    getAllBrands(transaction: Transaction): Promise<APIBaseResponse<ParsedBrand[] | null>> ;
    getBrand(id: number, transaction: Transaction): Promise<APIBaseResponse<ParsedBrand | null>> ;
    createBrand(payload:BrandPayload, user_id:number, transaction: Transaction):Promise<APIBaseResponse<null>>;
    updateBrand(id: number, payload: BrandPayload, userId:number, transaction: Transaction):Promise<APIBaseResponse<null>> ;
    deleteBrand(id: number,userId:number, transaction: Transaction): Promise<APIBaseResponse<null>> ;
    getBrandData(arrayIds: number[], currentBrandId: number,transaction:Transaction):Promise<APIBaseResponse<ParsedBrand | null>>
}