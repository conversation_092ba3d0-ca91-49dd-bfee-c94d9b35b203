import { parseUserToMetaUser } from "../../users/core/parser/core_user_parser";
import { ParsedBrand } from "../interface/brand";




const parsedBrand = (brand:any):ParsedBrand=>{
    return {
        id:brand.id,
        title:brand.title,
        description:brand.description,
        logo:brand.logo,
        createdAt: brand.createdAt,
        whiteListedDomains: brand.whiteListedDomains,
        createdById: brand.createdByUser? parseUserToMetaUser(brand.createdByUser) : brand.createdBy,
        updatedById: brand.updatedByUser ? parseUserToMetaUser(brand.updatedByUser) : brand.updatedBy,
        deletedById: brand.deletedByUser ? parseUserToMetaUser(brand.deletedByUser) : brand.deletedBy,   
        updatedAt: brand.updatedAt,
        deletedAt: brand.deletedAt,
    }
}


export default parsedBrand;