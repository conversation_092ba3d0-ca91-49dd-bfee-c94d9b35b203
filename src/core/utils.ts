import { ZodError, ZodIssue } from "zod";

/**
 * Represents a flattened Zod validation error
 * @typedef {Object} FlatZodError
 * @property {string} key - The path to the field that caused the error
 * @property {string} message - The error message describing the validation failure
 */
type FlatZodError = {
  key: string;
  message: string;
};

/**
 * Extracts and flattens all validation errors from a ZodError object
 * @param {ZodError} error - The Zod validation error object
 * @returns {FlatZodError[]} Array of flattened validation errors with keys and messages
 */
const extractAllZodErrors = (error: ZodError): FlatZodError[] => {
  const errors: FlatZodError[] = [];

  error.errors.forEach((issue: ZodIssue) => {
    const path = issue.path
      .map((segment) => (typeof segment === "number" ? `[${segment}]` : `.${segment}`))
      .join("")
      .replace(/^\./, "");

    if (issue.code === "unrecognized_keys") {
      const keys = (issue.keys || []).join(", ");
      errors.push({
        key: path || "root",
        message: `Unrecognized key(s): ${keys}`,
      });
    } else if (issue.code === "invalid_enum_value") {
      const expected = Array.isArray(issue.options) ? issue.options.join(", ") : "";
      errors.push({
        key: path || "root",
        message: `Invalid value. Expected one of: ${expected}`,
      });
    } else {
      errors.push({
        key: path || "root",
        message: issue.message,
      });
    }
  });

  return errors;
};

export {
    extractAllZodErrors
}