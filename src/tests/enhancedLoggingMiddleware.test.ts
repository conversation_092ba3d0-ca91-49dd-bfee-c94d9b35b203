import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import fs from 'fs';
import path from 'path';

// Mock the enhanced logging middleware
const mockLogsDir = path.join(process.cwd(), 'test-logs');

// Enhanced Logging Middleware Test Class
class EnhancedLoggingMiddlewareTest {
  private MAX_FILE_SIZE: number;
  private MAX_FILES_PER_DAY: number;
  private LOGS_RETENTION_DAYS: number;
  private logsDir: string;

  constructor(
    maxFileSize = 10* 1024 * 1024, // 10MB
    maxFilesPerDay = 10,
    logsRetentionDays = 30
  ) {
    this.MAX_FILE_SIZE = maxFileSize;
    this.MAX_FILES_PER_DAY = maxFilesPerDay;
    this.LOGS_RETENTION_DAYS = logsRetentionDays;
    this.logsDir = mockLogsDir;
    
    // Ensure test logs directory exists
    if (!fs.existsSync(this.logsDir)) {
      fs.mkdirSync(this.logsDir, { recursive: true });
    }
  }

  /**
   * Generate log file path for the current date with rotation support
   */
  getLogFilePath(date?: Date): string {
    const now = date || new Date();
    const day = String(now.getDate()).padStart(2, '0');
    const month = now.toLocaleDateString('en', { month: 'short' }).toLowerCase();
    const year = String(now.getFullYear()).slice(-2);
    const baseFilename = `${day}-${month}-${year}-api`;
    
    // Find the current rotation number
    let rotationNumber = 0;
    let logFilePath = path.join(this.logsDir, `${baseFilename}.log`);
    
    // Check if current file exists and is too large
    while (fs.existsSync(logFilePath)) {
      const stats = fs.statSync(logFilePath);
      if (stats.size < this.MAX_FILE_SIZE) {
        // Current file is under size limit, use it
        break;
      }
      
      // File is too large, try next rotation
      rotationNumber++;
      if (rotationNumber >= this.MAX_FILES_PER_DAY) {
        // Use the last allowed file (will continue growing)
        rotationNumber = this.MAX_FILES_PER_DAY - 1;
        logFilePath = path.join(this.logsDir, `${baseFilename}-${rotationNumber}.log`);
        break;
      }
      
      logFilePath = path.join(this.logsDir, `${baseFilename}-${rotationNumber}.log`);
    }
    
    return logFilePath;
  }

  /**
   * Write a log entry to the appropriate file
   */
  writeLogEntry(entry: any, date?: Date): string {
    const logFilePath = this.getLogFilePath(date);
    const logLine = JSON.stringify(entry) + '\n';
    fs.appendFileSync(logFilePath, logLine);
    return logFilePath;
  }

  /**
   * Clean up old log files
   */
  cleanupOldLogs(): string[] {
    const deletedFiles: string[] = [];
    try {
      const files = fs.readdirSync(this.logsDir);
      const now = new Date();
      const cutoffDate = new Date(now.getTime() - (this.LOGS_RETENTION_DAYS * 24 * 60 * 60 * 1000));
      
      for (const file of files) {
        if (file.endsWith('-api.log') || file.match(/-api-\d+\.log$/)) {
          const filePath = path.join(this.logsDir, file);
          const stats = fs.statSync(filePath);
          
          if (stats.mtime < cutoffDate) {
            fs.unlinkSync(filePath);
            deletedFiles.push(file);
          }
        }
      }
    } catch (error) {
      console.error('Error cleaning up old logs:', error);
    }
    return deletedFiles;
  }

  /**
   * Get all log files for a specific date
   */
  getLogFilesForDate(date: Date): string[] {
    const day = String(date.getDate()).padStart(2, '0');
    const month = date.toLocaleDateString('en', { month: 'short' }).toLowerCase();
    const year = String(date.getFullYear()).slice(-2);
    const basePattern = `${day}-${month}-${year}-api`;
    
    const files = fs.readdirSync(this.logsDir);
    return files.filter(file => file.startsWith(basePattern)).sort();
  }

  /**
   * Get file statistics
   */
  getFileStats(): {
    totalFiles: number;
    totalSizeMB: string;
    files: Array<{ name: string; sizeMB: string; sizeBytes: number; isLarge: boolean }>;
    warnings: string[];
  } {
    const files = fs.readdirSync(this.logsDir);
    const logFiles = files.filter(file => file.endsWith('-api.log') || file.match(/-api-\d+\.log$/));
    
    let totalSize = 0;
    const fileDetails: Array<{
      name: string;
      sizeMB: string;
      sizeBytes: number;
      isLarge: boolean;
    }> = [];
    
    for (const file of logFiles) {
      const filePath = path.join(this.logsDir, file);
      const stats = fs.statSync(filePath);
      totalSize += stats.size;
      
      fileDetails.push({
        name: file,
        sizeMB: (stats.size / (1024 * 1024)).toFixed(2),
        sizeBytes: stats.size,
        isLarge: stats.size > this.MAX_FILE_SIZE
      });
    }
    
    const warnings: string[] = [];
    const largeFiles = fileDetails.filter(f => f.isLarge);
    if (largeFiles.length > 0) {
      warnings.push(`${largeFiles.length} file(s) exceed size limit`);
    }
    
    return {
      totalFiles: logFiles.length,
      totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2),
      files: fileDetails,
      warnings
    };
  }

  /**
   * Clean up test directory
   */
  cleanup(): void {
    if (fs.existsSync(this.logsDir)) {
      const files = fs.readdirSync(this.logsDir);
      for (const file of files) {
        fs.unlinkSync(path.join(this.logsDir, file));
      }
      fs.rmdirSync(this.logsDir);
    }
  }
}

describe('Enhanced Logging Middleware', () => {
  let middleware: EnhancedLoggingMiddlewareTest;

  beforeEach(() => {
    middleware = new EnhancedLoggingMiddlewareTest();
  });

  afterEach(() => {
    middleware.cleanup();
  });

  describe('Basic File Creation', () => {
    test('should create initial log file with correct naming', () => {
      const testDate = new Date('2025-06-17');
      const entry = { 
        message: 'test log entry', 
        timestamp: new Date().toISOString(),
        level: 'info'
      };
      
      const filePath = middleware.writeLogEntry(entry, testDate);
      
      expect(fs.existsSync(filePath)).toBe(true);
      expect(path.basename(filePath)).toBe('17-jun-25-api.log');
      
      const content = fs.readFileSync(filePath, 'utf8');
      const parsedEntry = JSON.parse(content.trim());
      expect(parsedEntry.message).toBe('test log entry');
    });

    test('should append to existing file when under size limit', () => {
      const testDate = new Date('2025-06-17');
      const entry1 = { message: 'first entry', timestamp: new Date().toISOString() };
      const entry2 = { message: 'second entry', timestamp: new Date().toISOString() };
      
      const filePath1 = middleware.writeLogEntry(entry1, testDate);
      const filePath2 = middleware.writeLogEntry(entry2, testDate);
      
      expect(filePath1).toBe(filePath2);
      
      const content = fs.readFileSync(filePath1, 'utf8');
      const lines = content.trim().split('\n');
      expect(lines).toHaveLength(2);
      
      const parsedEntry1 = JSON.parse(lines[0]);
      const parsedEntry2 = JSON.parse(lines[1]);
      expect(parsedEntry1.message).toBe('first entry');
      expect(parsedEntry2.message).toBe('second entry');
    });
  });

  describe('Size-Based Rotation', () => {
    test('should create rotated file when size limit exceeded', () => {
      // Use small file size for testing (100 bytes)
      middleware = new EnhancedLoggingMiddlewareTest(100);
      const testDate = new Date('2025-06-17');
      
      // Create a large entry that exceeds the limit
      const largeEntry = {
        message: 'x'.repeat(200), // 200+ characters
        timestamp: new Date().toISOString(),
        data: { large: 'payload', more: 'data' }
      };
      
      // Write first entry (creates main file)
      const filePath1 = middleware.writeLogEntry(largeEntry, testDate);
      expect(path.basename(filePath1)).toBe('17-jun-25-api.log');
      
      // Write second entry (should create rotated file)
      const filePath2 = middleware.writeLogEntry(largeEntry, testDate);
      expect(path.basename(filePath2)).toBe('17-jun-25-api-1.log');
      
      // Verify both files exist
      expect(fs.existsSync(filePath1)).toBe(true);
      expect(fs.existsSync(filePath2)).toBe(true);
      
      // Verify file sizes
      const stats1 = fs.statSync(filePath1);
      const stats2 = fs.statSync(filePath2);
      expect(stats1.size).toBeGreaterThan(100);
      expect(stats2.size).toBeGreaterThan(0);
    });

    test('should continue rotation sequence correctly', () => {
      middleware = new EnhancedLoggingMiddlewareTest(50); // 50 bytes
      const testDate = new Date('2025-06-17');
      const largeEntry = {
        message: 'x'.repeat(100),
        timestamp: new Date().toISOString(),
        requestId: 'test-123'
      };
      
      const filePaths: string[] = [];
      
      // Create multiple files to test rotation sequence
      for (let i = 0; i < 5; i++) {
        const filePath = middleware.writeLogEntry({
          ...largeEntry,
          sequenceNumber: i
        }, testDate);
        filePaths.push(filePath);
      }
      
      // Verify rotation sequence
      expect(path.basename(filePaths[0])).toBe('17-jun-25-api.log');
      expect(path.basename(filePaths[1])).toBe('17-jun-25-api-1.log');
      expect(path.basename(filePaths[2])).toBe('17-jun-25-api-2.log');
      expect(path.basename(filePaths[3])).toBe('17-jun-25-api-3.log');
      expect(path.basename(filePaths[4])).toBe('17-jun-25-api-4.log');
    });

    test('should handle exact size limit boundary', () => {
      middleware = new EnhancedLoggingMiddlewareTest(100); // Exactly 100 bytes
      const testDate = new Date('2025-06-17');
      
      // Create entry that's exactly at the limit
      const exactEntry = {
        msg: 'x'.repeat(85) // Adjust to make JSON exactly ~100 bytes
      };
      
      const filePath1 = middleware.writeLogEntry(exactEntry, testDate);
      const stats1 = fs.statSync(filePath1);
      
      // Add one more character to exceed limit
      const exceedingEntry = {
        msg: 'x'.repeat(86)
      };
      
      const filePath2 = middleware.writeLogEntry(exceedingEntry, testDate);
      
      // Should create new file when limit is exceeded
      if (stats1.size >= 100) {
        expect(path.basename(filePath2)).toBe('17-jun-25-api-1.log');
      } else {
        expect(filePath1).toBe(filePath2);
      }
    });
  });

  describe('File Limit Enforcement', () => {
    test('should stop creating new files after reaching limit', () => {
      middleware = new EnhancedLoggingMiddlewareTest(50, 3); // 50 bytes, max 3 files
      const testDate = new Date('2025-06-17');
      const largeEntry = {
        message: 'x'.repeat(100),
        timestamp: new Date().toISOString()
      };
      
      const filePaths: string[] = [];
      
      // Try to create more files than the limit
      for (let i = 0; i < 6; i++) {
        const filePath = middleware.writeLogEntry({
          ...largeEntry,
          index: i
        }, testDate);
        filePaths.push(filePath);
      }
      
      // Should only have 3 unique file paths
      const uniquePaths = [...new Set(filePaths)];
      expect(uniquePaths).toHaveLength(3);
      
      // Last few entries should all go to the same file (the limit file)
      expect(filePaths[3]).toBe(filePaths[4]);
      expect(filePaths[4]).toBe(filePaths[5]);
      expect(path.basename(filePaths[5])).toBe('17-jun-25-api-2.log');
    });

    test('should allow limit file to grow beyond size limit', () => {
      middleware = new EnhancedLoggingMiddlewareTest(50, 2); // 50 bytes, max 2 files
      const testDate = new Date('2025-06-17');
      const largeEntry = {
        message: 'x'.repeat(100),
        timestamp: new Date().toISOString()
      };
      
      // Fill up to the limit
      middleware.writeLogEntry({ ...largeEntry, seq: 1 }, testDate); // File 0
      middleware.writeLogEntry({ ...largeEntry, seq: 2 }, testDate); // File 1
      
      // These should all go to file 1 (the limit file)
      const filePath1 = middleware.writeLogEntry({ ...largeEntry, seq: 3 }, testDate);
      const filePath2 = middleware.writeLogEntry({ ...largeEntry, seq: 4 }, testDate);
      const filePath3 = middleware.writeLogEntry({ ...largeEntry, seq: 5 }, testDate);
      
      expect(filePath1).toBe(filePath2);
      expect(filePath2).toBe(filePath3);
      expect(path.basename(filePath3)).toBe('17-jun-25-api-1.log');
      
      // Verify the limit file has grown large
      const stats = fs.statSync(filePath3);
      expect(stats.size).toBeGreaterThan(200); // Much larger than 50 byte limit
    });

    test('should report warnings for files exceeding size limit', () => {
      middleware = new EnhancedLoggingMiddlewareTest(100, 2);
      const testDate = new Date('2025-06-17');
      const largeEntry = { message: 'x'.repeat(200) };
      
      // Create files that exceed the size limit
      middleware.writeLogEntry(largeEntry, testDate);
      middleware.writeLogEntry(largeEntry, testDate);
      middleware.writeLogEntry(largeEntry, testDate);
      
      const stats = middleware.getFileStats();
      expect(stats.warnings.length).toBeGreaterThan(0);
      expect(stats.warnings[0]).toContain('exceed size limit');
    });
  });

  describe('Date-Based File Management', () => {
    test('should create separate files for different dates', () => {
      const date1 = new Date('2025-06-17');
      const date2 = new Date('2025-06-18');
      const date3 = new Date('2025-07-01'); // Different month
      
      const entry = { message: 'test', timestamp: new Date().toISOString() };
      
      const filePath1 = middleware.writeLogEntry(entry, date1);
      const filePath2 = middleware.writeLogEntry(entry, date2);
      const filePath3 = middleware.writeLogEntry(entry, date3);
      
      expect(path.basename(filePath1)).toBe('17-jun-25-api.log');
      expect(path.basename(filePath2)).toBe('18-jun-25-api.log');
      expect(path.basename(filePath3)).toBe('01-jul-25-api.log');
      
      expect(filePath1).not.toBe(filePath2);
      expect(filePath2).not.toBe(filePath3);
    });

    test('should get all log files for a specific date', () => {
      middleware = new EnhancedLoggingMiddlewareTest(50); // Small size to force rotation
      const testDate = new Date('2025-06-17');
      const otherDate = new Date('2025-06-18');
      
      const largeEntry = {
        message: 'x'.repeat(100),
        timestamp: new Date().toISOString()
      };
      
      // Create multiple files for the same date
      for (let i = 0; i < 4; i++) {
        middleware.writeLogEntry({ ...largeEntry, index: i }, testDate);
      }
      
      // Create one file for different date
      middleware.writeLogEntry(largeEntry, otherDate);
      
      const filesForDate = middleware.getLogFilesForDate(testDate);
      const filesForOtherDate = middleware.getLogFilesForDate(otherDate);
      
      // Should have created multiple files due to rotation
      expect(filesForDate.length).toBeGreaterThan(1);
      expect(filesForDate).toContain('17-jun-25-api.log');
      
      // All files should follow the correct naming pattern
      for (const file of filesForDate) {
        expect(file).toMatch(/^17-jun-25-api(-\d+)?\.log$/);
      }
      
      expect(filesForOtherDate).toEqual(['18-jun-25-api.log']);
    });

    test('should handle year boundary correctly', () => {
      const endOfYear = new Date('2025-12-31');
      const startOfYear = new Date('2026-01-01');
      
      const entry = { message: 'test', timestamp: new Date().toISOString() };
      
      const filePath1 = middleware.writeLogEntry(entry, endOfYear);
      const filePath2 = middleware.writeLogEntry(entry, startOfYear);
      
      expect(path.basename(filePath1)).toBe('31-dec-25-api.log');
      expect(path.basename(filePath2)).toBe('01-jan-26-api.log');
    });
  });

  describe('Cleanup Functionality', () => {
    test('should delete files older than retention period', () => {
      middleware = new EnhancedLoggingMiddlewareTest(500 * 1024, 10, 1); // 1 day retention
      
      // Create files with different dates
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 2); // 2 days ago
      
      const recentDate = new Date(); // Today
      
      const entry = { message: 'test', timestamp: new Date().toISOString() };
      
      const oldFilePath = middleware.writeLogEntry(entry, oldDate);
      const recentFilePath = middleware.writeLogEntry(entry, recentDate);
      
      // Manually set old file modification time
      const twoDaysAgo = new Date();
      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
      fs.utimesSync(oldFilePath, twoDaysAgo, twoDaysAgo);
      
      // Run cleanup
      const deletedFiles = middleware.cleanupOldLogs();
      
      // Old file should be deleted, recent file should remain
      expect(fs.existsSync(oldFilePath)).toBe(false);
      expect(fs.existsSync(recentFilePath)).toBe(true);
      expect(deletedFiles.length).toBeGreaterThan(0);
      expect(deletedFiles).toContain(path.basename(oldFilePath));
    });

    test('should not delete files within retention period', () => {
      middleware = new EnhancedLoggingMiddlewareTest(500 * 1024, 10, 7); // 7 day retention
      
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
      
      const entry = { message: 'test', timestamp: new Date().toISOString() };
      const filePath = middleware.writeLogEntry(entry, threeDaysAgo);
      
      // Set file modification time to 3 days ago
      fs.utimesSync(filePath, threeDaysAgo, threeDaysAgo);
      
      const deletedFiles = middleware.cleanupOldLogs();
      
      // File should still exist (within 7 day retention)
      expect(fs.existsSync(filePath)).toBe(true);
      expect(deletedFiles).not.toContain(path.basename(filePath));
    });
  });

  describe('Edge Cases', () => {
    test('should handle empty log entries', () => {
      const testDate = new Date('2025-06-17');
      const emptyEntry = {};
      
      const filePath = middleware.writeLogEntry(emptyEntry, testDate);
      expect(fs.existsSync(filePath)).toBe(true);
      
      const content = fs.readFileSync(filePath, 'utf8');
      expect(content.trim()).toBe('{}');
    });

    test('should handle null and undefined values', () => {
      const testDate = new Date('2025-06-17');
      const entry = {
        message: null,
        data: undefined,
        timestamp: new Date().toISOString()
      };
      
      const filePath = middleware.writeLogEntry(entry, testDate);
      expect(fs.existsSync(filePath)).toBe(true);
      
      const content = fs.readFileSync(filePath, 'utf8');
      const parsed = JSON.parse(content.trim());
      expect(parsed.message).toBe(null);
      expect(parsed.data).toBe(undefined);
    });

    test('should handle very large single entry', () => {
      middleware = new EnhancedLoggingMiddlewareTest(100); // 100 bytes
      const testDate = new Date('2025-06-17');
      const hugeEntry = {
        message: 'x'.repeat(1000), // Much larger than file limit
        timestamp: new Date().toISOString(),
        metadata: { type: 'huge-entry' }
      };
      
      const filePath = middleware.writeLogEntry(hugeEntry, testDate);
      expect(fs.existsSync(filePath)).toBe(true);
      
      const stats = fs.statSync(filePath);
      expect(stats.size).toBeGreaterThan(1000);
      
      // Verify content is intact
      const content = fs.readFileSync(filePath, 'utf8');
      const parsed = JSON.parse(content.trim());
      expect(parsed.message).toBe('x'.repeat(1000));
    });

    test('should handle special characters and unicode', () => {
      const testDate = new Date('2025-06-17');
      const entry = {
        message: 'Special chars: àáâãäåæçèéêë 中文 🚀 💡',
        emoji: '🎉🔥⚡',
        unicode: '\u{1F600}\u{1F601}\u{1F602}',
        timestamp: new Date().toISOString()
      };
      
      const filePath = middleware.writeLogEntry(entry, testDate);
      expect(fs.existsSync(filePath)).toBe(true);
      
      const content = fs.readFileSync(filePath, 'utf8');
      const parsed = JSON.parse(content.trim());
      expect(parsed.message).toBe('Special chars: àáâãäåæçèéêë 中文 🚀 💡');
      expect(parsed.emoji).toBe('🎉🔥⚡');
    });

    test('should handle invalid dates gracefully', () => {
      const invalidDate = new Date('invalid-date');
      const entry = { message: 'test', timestamp: new Date().toISOString() };
      
      // Should not throw and should fall back to current date
      expect(() => {
        const filePath = middleware.writeLogEntry(entry, invalidDate);
        expect(fs.existsSync(filePath)).toBe(true);
      }).not.toThrow();
    });
  });

  describe('Performance and Stress Tests', () => {
    test('should handle rapid sequential writes efficiently', () => {
      const testDate = new Date('2025-06-17');
      const startTime = Date.now();
      
      // Write 100 entries rapidly
      for (let i = 0; i < 100; i++) {
        const entry = {
          message: `Entry ${i}`,
          timestamp: new Date().toISOString(),
          index: i,
          data: { batch: 'performance-test', iteration: i }
        };
        middleware.writeLogEntry(entry, testDate);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time
      expect(duration).toBeLessThan(2000); // 2 seconds
      
      // Verify all entries were written
      const files = middleware.getLogFilesForDate(testDate);
      let totalLines = 0;
      
      for (const file of files) {
        const content = fs.readFileSync(path.join(mockLogsDir, file), 'utf8');
        totalLines += content.trim().split('\n').filter(line => line.length > 0).length;
      }
      
      expect(totalLines).toBe(100);
    });

    test('should handle concurrent-like writes without data loss', () => {
      const testDate = new Date('2025-06-17');
      const entries: Array<{
        message: string;
        timestamp: string;
        threadId: number;
        index: number;
      }> = [];
      
      // Prepare multiple entries
      for (let i = 0; i < 50; i++) {
        entries.push({
          message: `Concurrent entry ${i}`,
          timestamp: new Date().toISOString(),
          threadId: i % 5, // Simulate 5 different "threads"
          index: i
        });
      }
      
      // Write all entries
      const filePaths = entries.map(entry => middleware.writeLogEntry(entry, testDate));
      
      // Verify all writes succeeded
      expect(filePaths.length).toBe(50);
      
      // Count total written entries
      const files = middleware.getLogFilesForDate(testDate);
      let totalEntries = 0;
      
      for (const file of files) {
        const content = fs.readFileSync(path.join(mockLogsDir, file), 'utf8');
        const lines = content.trim().split('\n').filter(line => line.length > 0);
        totalEntries += lines.length;
      }
      
      expect(totalEntries).toBe(50);
    });
  });

  describe('Real-World Scenarios', () => {
    test('should simulate a high-traffic day with mixed request types', () => {
      middleware = new EnhancedLoggingMiddlewareTest(1024, 5); // 1KB files, max 5
      const testDate = new Date('2025-06-17');
      
      // Simulate different types of HTTP requests
      const requestTypes = [
        { method: 'GET', url: '/api/users', status: 200, responseTime: 45, size: 'small' },
        { method: 'POST', url: '/api/users', status: 201, responseTime: 120, size: 'medium' },
        { method: 'GET', url: '/api/logs/stats', status: 200, responseTime: 3500, size: 'large' },
        { method: 'DELETE', url: '/api/users/123', status: 404, responseTime: 25, size: 'small' },
        { method: 'PUT', url: '/api/users/456', status: 500, responseTime: 1200, size: 'medium' }
      ];
      
      // Generate 50 requests
      for (let i = 0; i < 50; i++) {
        const requestType = requestTypes[i % requestTypes.length];
        const entry = {
          timestamp: new Date(Date.now() + i * 1000).toISOString(), // Spread over time
          id: `req-${i.toString().padStart(3, '0')}`,
          ip: `192.168.1.${(i % 254) + 1}`,
          request: {
            method: requestType.method,
            url: requestType.url,
            headers: { 
              'user-agent': 'test-client/1.0',
              'content-type': 'application/json',
              'x-request-id': `req-${i}`
            },
            body: requestType.method === 'POST' ? { 
              data: 'x'.repeat(requestType.size === 'large' ? 200 : requestType.size === 'medium' ? 50 : 10)
            } : null
          },
          response: {
            status: requestType.status,
            headers: { 'content-type': 'application/json' },
            body: { 
              success: requestType.status < 400,
              data: requestType.size === 'large' ? 'x'.repeat(300) : 'response data'
            },
            responseTime: `${requestType.responseTime}ms`
          },
          error: requestType.status >= 400 ? { 
            statusCode: requestType.status,
            message: requestType.status === 404 ? 'Not Found' : 'Internal Server Error'
          } : false
        };
        
        middleware.writeLogEntry(entry, testDate);
      }
      
      const files = middleware.getLogFilesForDate(testDate);
      expect(files.length).toBeGreaterThan(1); // Should have rotated
      expect(files.length).toBeLessThanOrEqual(5); // Should not exceed limit
      
      // Verify all entries are preserved
      let totalEntries = 0;
      let errorEntries = 0;
      let successEntries = 0;
      
      for (const file of files) {
        const content = fs.readFileSync(path.join(mockLogsDir, file), 'utf8');
        const lines = content.trim().split('\n').filter(line => line.length > 0);
        
        for (const line of lines) {
          const entry = JSON.parse(line);
          totalEntries++;
          
          if (entry.error && entry.error !== false) {
            errorEntries++;
          } else {
            successEntries++;
          }
        }
      }
      
      expect(totalEntries).toBe(50);
      expect(errorEntries).toBeGreaterThan(0);
      expect(successEntries).toBeGreaterThan(0);
      expect(errorEntries + successEntries).toBe(totalEntries);
    });

    test('should handle log burst followed by quiet period', () => {
      middleware = new EnhancedLoggingMiddlewareTest(512); // 512 bytes
      const testDate = new Date('2025-06-17');
      
      // Simulate burst of activity
      const burstEntry = {
        message: 'x'.repeat(200),
        timestamp: new Date().toISOString(),
        type: 'burst'
      };
      
      // Create burst (10 large entries)
      for (let i = 0; i < 10; i++) {
        middleware.writeLogEntry({ ...burstEntry, burstIndex: i }, testDate);
      }
      
      const filesAfterBurst = middleware.getLogFilesForDate(testDate);
      const burstFileCount = filesAfterBurst.length;
      
      // Simulate quiet period (small entries)
      const quietEntry = {
        msg: 'quiet',
        timestamp: new Date().toISOString(),
        type: 'quiet'
      };
      
      for (let i = 0; i < 5; i++) {
        middleware.writeLogEntry({ ...quietEntry, quietIndex: i }, testDate);
      }
      
      const filesAfterQuiet = middleware.getLogFilesForDate(testDate);
      
      // Should have created rotation during burst, but quiet entries should fit in existing files
      expect(burstFileCount).toBeGreaterThan(1);
      expect(filesAfterQuiet.length).toBeGreaterThanOrEqual(burstFileCount);
      
      // Verify total entry count
      let totalEntries = 0;
      for (const file of filesAfterQuiet) {
        const content = fs.readFileSync(path.join(mockLogsDir, file), 'utf8');
        const lines = content.trim().split('\n').filter(line => line.length > 0);
        totalEntries += lines.length;
      }
      
      expect(totalEntries).toBe(15); // 10 burst + 5 quiet
    });
  });

  describe('File Statistics and Monitoring', () => {
    test('should provide accurate file statistics', () => {
      middleware = new EnhancedLoggingMiddlewareTest(200); // 200 bytes
      const testDate = new Date('2025-06-17');
      
      const entry = {
        message: 'x'.repeat(150),
        timestamp: new Date().toISOString(),
        data: { test: true }
      };
      
      // Create multiple files
      for (let i = 0; i < 4; i++) {
        middleware.writeLogEntry({ ...entry, index: i }, testDate);
      }
      
      const stats = middleware.getFileStats();
      
      expect(stats.totalFiles).toBe(4);
      expect(parseFloat(stats.totalSizeMB)).toBeGreaterThanOrEqual(0);
      expect(stats.files.length).toBe(4);
      
      // Check individual file stats
      for (const file of stats.files) {
        expect(file.name).toMatch(/17-jun-25-api(-\d+)?\.log/);
        expect(file.sizeBytes).toBeGreaterThan(0);
        expect(parseFloat(file.sizeMB)).toBeGreaterThanOrEqual(0);
      }
    });

    test('should detect and warn about oversized files', () => {
      middleware = new EnhancedLoggingMiddlewareTest(100, 2); // 100 bytes, max 2 files
      const testDate = new Date('2025-06-17');
      
      const largeEntry = {
        message: 'x'.repeat(200),
        timestamp: new Date().toISOString()
      };
      
      // Create files that will exceed size limits
      middleware.writeLogEntry(largeEntry, testDate);
      middleware.writeLogEntry(largeEntry, testDate);
      middleware.writeLogEntry(largeEntry, testDate); // This should make the limit file large
      
      const stats = middleware.getFileStats();
      
      expect(stats.warnings.length).toBeGreaterThan(0);
      expect(stats.warnings[0]).toContain('exceed size limit');
      
      // At least one file should be marked as large
      const largeFiles = stats.files.filter(f => f.isLarge);
      expect(largeFiles.length).toBeGreaterThan(0);
    });
  });
}); 