'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /*
    1. add new column moq, number type, default values 0, not null
    2. remove categoryId,unitId, hsn, gstPercentage columns
    */
    await queryInterface.addColumn('raw-materials', 'moq', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    });

    await queryInterface.removeColumn('raw-materials', 'categoryId');
    await queryInterface.removeColumn('raw-materials', 'unitId');
    await queryInterface.removeColumn('raw-materials', 'hsn');
    await queryInterface.removeColumn('raw-materials', 'gstPercentage');
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
