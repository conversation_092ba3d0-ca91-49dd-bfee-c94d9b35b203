'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('departments', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.BIGINT
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive', 'deleted'),
        allowNull: false,
        defaultValue: 'active'
      },
      created_by_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'core_users',
          key: 'id'
        }
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_by_id: {
        type: Sequelize.BIGINT,
        allowNull: true,
        references: {
          model: 'core_users',
          key: 'id'
        }
      },
      updated_at: {
        allowNull: true,
        type: Sequelize.DATE
      },
      deleted_by_id: {
        type: Sequelize.BIGINT,
        allowNull: true,
        references: {
          model: 'core_users',
          key: 'id'
        }
      },
      deleted_at: {
        allowNull: true,
        type: Sequelize.DATE
      }
    });

    // Add unique index for department name
    await queryInterface.addIndex('departments', ['name'], {
      unique: true,
      name: 'unique_department_name'
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('departments');
  }
};
