'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableName = 'raw-materials';
    const uniqueConstraints = await queryInterface.sequelize.query(
      `SELECT conname
       FROM pg_constraint
       WHERE conrelid = '"${tableName}"'::regclass
       AND contype = 'u';`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (uniqueConstraints.length > 0) {
      console.log(`Found ${uniqueConstraints.length} unique constraint(s). Dropping them now...`);
      for (const constraint of uniqueConstraints) {
        await queryInterface.sequelize.query(
          `ALTER TABLE "${tableName}" DROP CONSTRAINT "${constraint.conname}";`
        );
        console.log(`  - Dropped: ${constraint.conname}`);
      }
      console.log('All existing unique constraints dropped.');
    } else {
      console.log(`No existing unique constraints found on '${tableName}'.`);
    }

    console.log(`Adding new unique constraint 'unique_variation' to '${tableName}'...`);
    await queryInterface.addConstraint(tableName, {
      fields: ['name', 'parentRawMaterialId'],
      type: 'unique',
      name: 'unique_variation_name',
    });
    await queryInterface.addConstraint(tableName, {
      fields: ['sku'],
      type: 'unique',
      name: 'unique_variation_sku',
    });
    console.log('New unique constraint \'unique_variation\' added successfully.');

  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
