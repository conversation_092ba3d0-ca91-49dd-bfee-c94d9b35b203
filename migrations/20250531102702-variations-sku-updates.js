'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableName = 'raw-materials';

    console.log(`Starting data migration for '${tableName}' table...`);
    console.log('Updating "sku" column where it is NULL or empty, using trimmed lowercase "name" value.');
    const [result, metadata] = await queryInterface.sequelize.query(
      `UPDATE "${tableName}"
       SET sku = LOWER(TRIM(name))
       WHERE sku IS NULL OR sku = '';`
    );

    console.log(`Successfully updated ${metadata.rowCount} row(s) in '${tableName}'.`);

  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
