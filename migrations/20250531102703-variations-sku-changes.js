'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableName = 'raw-materials';
    const primaryKeyColumn = 'id';

    // Step 1: Update "sku" column to lowercase and trim
    console.log(`Step 1: Updating 'sku' column in '${tableName}' to lowercase and trim...`);
    await queryInterface.sequelize.query(
      `UPDATE "${tableName}" SET sku = LOWER(TRIM(sku)) WHERE sku IS NOT NULL;`
    );
    console.log('SKU column updated to lowercase and trimmed.');

    // Step 2: Handle duplicate SKUs by appending _1, _2, etc.
    console.log(`Step 2: Handling duplicate SKUs in '${tableName}'...`);

    // Get all rows that need to be updated (duplicates only, excluding the first occurrence)
    const duplicateRows = await queryInterface.sequelize.query(
      `WITH RankedRows AS (
        SELECT
          ${primaryKeyColumn},
          sku,
          ROW_NUMBER() OVER (PARTITION BY sku ORDER BY ${primaryKeyColumn}) as rn
        FROM "${tableName}"
        WHERE sku IS NOT NULL
      )
      SELECT ${primaryKeyColumn}, sku, rn
      FROM RankedRows
      WHERE rn > 1
      ORDER BY sku, ${primaryKeyColumn};`,
      {
        type: Sequelize.QueryTypes.SELECT
      }
    );

    console.log(`Found ${duplicateRows.length} duplicate rows to process.`);

    // Process duplicates in batches to avoid loops
    for (const row of duplicateRows) {
      const newSku = `${row.sku}_${row.rn - 1}`;

      await queryInterface.sequelize.query(
        `UPDATE "${tableName}" SET sku = :newSku WHERE ${primaryKeyColumn} = :id;`,
        {
          replacements: { newSku: newSku, id: row[primaryKeyColumn] }
        }
      );

      console.log(`    Updated row ID ${row[primaryKeyColumn]}: '${row.sku}' -> '${newSku}'`);
    }

    // Verify no duplicates remain
    const remainingDuplicates = await queryInterface.sequelize.query(
      `SELECT sku, COUNT(*) as count
       FROM "${tableName}"
       WHERE sku IS NOT NULL
       GROUP BY sku
       HAVING COUNT(*) > 1;`,
      {
        type: Sequelize.QueryTypes.SELECT
      }
    );

    if (remainingDuplicates.length > 0) {
      console.warn(`Warning: ${remainingDuplicates.length} SKUs still have duplicates after processing.`);
      console.log('Remaining duplicates:', remainingDuplicates);
    } else {
      console.log('All duplicates successfully resolved.');
    }

    console.log('All duplicate SKU handling complete.');

    // IMPORTANT: After ensuring uniqueness, apply the unique constraint if not already present
    try {
      await queryInterface.addConstraint(tableName, {
        fields: ['sku'],
        type: 'unique',
        name: 'unique_sku_constraint',
      });
      console.log('Unique constraint on "sku" column added successfully.');
    } catch (error) {
      if (error.name === 'SequelizeUniqueConstraintError' || (error.message && error.message.includes('unique constraint'))) {
        console.warn('Unique constraint "unique_sku_constraint" already exists or could not be added due to existing data (should be resolved by now). Please check manually.');
      } else {
        console.error('Error adding unique constraint on "sku":', error);
        throw error; // Re-throw if it's an unexpected error
      }
    }
  },

  async down(queryInterface, Sequelize) {
    const tableName = 'raw-materials';

    // Remove the unique constraint
    try {
      await queryInterface.removeConstraint(tableName, 'unique_sku_constraint');
      console.log('Unique constraint on "sku" column removed successfully.');
    } catch (error) {
      console.warn('Could not remove unique constraint "unique_sku_constraint". It may not exist.');
    }

    // Note: We cannot easily revert the SKU changes as we don't know the original values
    console.log('Note: SKU changes cannot be automatically reverted. Manual intervention may be required.');
  }
};
