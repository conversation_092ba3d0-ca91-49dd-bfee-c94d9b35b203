'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const tableName = 'raw_materials_main';
    const uniqueConstraints = await queryInterface.sequelize.query(
      `SELECT conname
       FROM pg_constraint
       WHERE conrelid = '"${tableName}"'::regclass
       AND contype = 'u';`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (uniqueConstraints.length > 0) {
      console.log(`Found ${uniqueConstraints.length} unique constraint(s). Dropping them now...`);
      for (const constraint of uniqueConstraints) {
        await queryInterface.sequelize.query(
          `ALTER TABLE "${tableName}" DROP CONSTRAINT "${constraint.conname}";`
        );
        console.log(`  - Dropped: ${constraint.conname}`);
      }
      console.log('All existing unique constraints dropped.');
    } else {
      console.log(`No existing unique constraints found on '${tableName}'.`);
    }

    console.log(`Adding new unique constraint 'unique_raw_material_name' to '${tableName}'...`);
    await queryInterface.addConstraint(tableName, {
      fields: ['name'],
      type: 'unique',
      name: 'unique_name_of_raw_material',
    });
    console.log('New unique constraint \'unique_raw_material_name\' added successfully.');
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  }
};
