'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add user_id column to third_party_system_users table
    await queryInterface.addColumn('third_party_system_users', 'user_id', {
      type: Sequelize.BIGINT,
      allowNull: false,
      references: {
        model: 'core_users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT'
    });

    // Add index for better query performance
    await queryInterface.addIndex('third_party_system_users', ['user_id'], {
      name: 'idx_third_party_system_users_user_id'
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove index first
    await queryInterface.removeIndex('third_party_system_users', 'idx_third_party_system_users_user_id');
    
    // Remove the user_id column
    await queryInterface.removeColumn('third_party_system_users', 'user_id');
  }
};
