'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Add new columns to purchase_orders table
    await queryInterface.addColumn('purchase_orders', 'from_department_id', {
      type: Sequelize.BIGINT,
      allowNull: true, // Initially allow null for existing records
      references: {
        model: 'departments',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT'
    });

    await queryInterface.addColumn('purchase_orders', 'to_department_id', {
      type: Sequelize.BIGINT,
      allowNull: true, // Initially allow null for existing records
      references: {
        model: 'departments',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT'
    });

    await queryInterface.addColumn('purchase_orders', 'supplier_contact_person', {
      type: Sequelize.STRING,
      allowNull: true
    });

    // Update existing records to set from_department_id = 1 and to_department_id = 5
    await queryInterface.sequelize.query(`
      UPDATE purchase_orders 
      SET from_department_id = 1, to_department_id = 5 
      WHERE from_department_id IS NULL OR to_department_id IS NULL
    `);

    // Now make the department columns NOT NULL since all records have values
    await queryInterface.changeColumn('purchase_orders', 'from_department_id', {
      type: Sequelize.BIGINT,
      allowNull: false,
      references: {
        model: 'departments',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT'
    });

    await queryInterface.changeColumn('purchase_orders', 'to_department_id', {
      type: Sequelize.BIGINT,
      allowNull: false,
      references: {
        model: 'departments',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT'
    });

    // Add indexes for better performance
    await queryInterface.addIndex('purchase_orders', ['from_department_id'], {
      name: 'idx_purchase_orders_from_department'
    });

    await queryInterface.addIndex('purchase_orders', ['to_department_id'], {
      name: 'idx_purchase_orders_to_department'
    });
  },

  async down (queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex('purchase_orders', 'idx_purchase_orders_from_department');
    await queryInterface.removeIndex('purchase_orders', 'idx_purchase_orders_to_department');

    // Remove columns
    await queryInterface.removeColumn('purchase_orders', 'from_department_id');
    await queryInterface.removeColumn('purchase_orders', 'to_department_id');
    await queryInterface.removeColumn('purchase_orders', 'supplier_contact_person');
  }
};
