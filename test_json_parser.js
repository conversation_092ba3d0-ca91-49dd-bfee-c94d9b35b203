const fs = require('fs');
const path = require('path');

// Import the compiled parser
const { MorganLogParser } = require('./dist/src/features/logs/utils/MorganLogParser');

async function testJsonLogParser() {
    try {
        // Find the most recent log file with new naming convention
        const logsDir = path.join(__dirname, 'logs');
        const files = fs.readdirSync(logsDir);
        const logFiles = files.filter(file => file.endsWith('-api.log')).sort().reverse();
        
        if (logFiles.length === 0) {
            console.log('No log files found with pattern *-api.log');
            return;
        }
        
        const logFilePath = path.join(logsDir, logFiles[0]);
        console.log(`Using log file: ${logFiles[0]}`);
        
        // Check if file exists
        if (!fs.existsSync(logFilePath)) {
            console.log('Log file not found:', logFilePath);
            return;
        }
        
        // Get file size
        const stats = fs.statSync(logFilePath);
        const fileSizeInMB = stats.size / (1024 * 1024);
        console.log(`Log file size: ${fileSizeInMB.toFixed(2)} MB`);
        
        // Test parsing with streaming (for large files)
        console.log('Testing JSON log parsing with streaming...');
        const logs = await MorganLogParser.parseLogStream(logFilePath);
        
        console.log(`Successfully parsed ${logs.length} log entries`);
        
        if (logs.length > 0) {
            console.log('\nSample log entry:');
            console.log(JSON.stringify(logs[0], null, 2));
            
            // Test filtering
            console.log('\nTesting filters...');
            const getRequests = logs.filter(log => log.request.method === 'GET');
            console.log(`GET requests: ${getRequests.length}`);
            
            const errorLogs = logs.filter(log => log.error !== false);
            console.log(`Error logs: ${errorLogs.length}`);
            
            const successLogs = logs.filter(log => log.response.status < 400);
            console.log(`Success logs: ${successLogs.length}`);
        }
        
        // Test pagination
        console.log('\nTesting pagination...');
        const paginatedResult = await MorganLogParser.parseLogStreamWithFilters(
            logFilePath, 
            1, // page 1
            5, // 5 entries per page
            { method: 'GET' } // only GET requests
        );
        
        console.log('Pagination result:');
        console.log(`Page: ${paginatedResult.pagination.currentPage}/${paginatedResult.pagination.totalPages}`);
        console.log(`Total entries: ${paginatedResult.pagination.totalData}`);
        console.log(`Entries on this page: ${paginatedResult.logs.length}`);
        
    } catch (error) {
        console.error('Error testing JSON log parser:', error);
    }
}

// Run the test
testJsonLogParser(); 