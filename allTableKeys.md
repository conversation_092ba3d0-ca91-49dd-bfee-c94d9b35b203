## USER ROLES

role
status
createdById

## USER PERMISSION

roleId
permissions
createdById

## USER
 
firstName
lastName
email
password
mobile
roleId
status
firebaseUID
createdById

## ADDRESS EXTENDS BY USER

street
postalCode
city
state
country
status
createdById

## SUPPLIER

name
email
phone
address:{
    street
    postalCode
    city
    state
    country
    status
}
gst
pan
status
createdById

## STORAGE LOCATION

name
description
type
status
createdById

## ITEM UNIT

name
status

## ITEM CATEGORY

name
status

## RAW MATERIAL

name
hsn
gstPercentage
categoryId
unitId
sku
msq
status
priceData


## FACTORY GATE 

name
description
status


## PURCHASE ORDER

poNumber
supplierId
expectedDate
receivedDate
createdById


## PURCHASE INVOICE

invoiceNumber
invoiceDate
poNumber
poDate
supplierId
status
purchasedById
factoryGateId
createdById


## RAW MATERIAL PRICE ('pending')

price
moq
supplierId
rawMaterialId
createdById

## RAW MATERIAL STOCKS ('pending')

rawMaterialId
totalStock
assignedStock
usableStock
createdById

## RAW MATERIAL STOCKS IN ('pending')

rawMaterialId
qty
price
storageLocationId
factoryGateId
supplierId
purchaseInvoiceId
createdById

## Raw Material Stock Issuance 

rawMaterialId
qty
soNumber
issuedTo
notes
createdById

## Raw MATERIAL REJECTION ('pending')

rawMaterialId
purchaseInvoiceId
totalQty
rejectedQty
rejectionReason
rejectedById
createdById


## RAW MATERIAL HOLD ('pending')

rawMaterialId
purchaseInvoiceId
totalQty
holdQty
holdReason
holdById
createdById


## PURCHASE ORDER ITEMS ('pending')

createdById
purchaseOrderId
rawMaterialId
qty

## PURCHASE INVOICE PURCHASE ORDER MAPPING ('pending')

purchaseInvoiceId
purchaseOrderId

## PURCHASE INVOICE ENTERY MAPPING ('pending')

purchaseInvoiceId
entryNumber

## OPEING STOCK ('pending')

rawMaterialId
quantity
date



## ITEM ATTRIBUTES ('pending')

createdById
name
status

## ITEM ATTRIBUTES ITEMS ('pending')

status
itemAttributeId
value
title
createdById

## DEBIT NOTE ('pending')

purchaseInvoiceId
rawMaterialId
qty
actualPrice
source
purchasedPrice
note
createdById
is_manual