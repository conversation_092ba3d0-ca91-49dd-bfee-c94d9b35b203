# Log Management API Contract (Updated with Daily Log Rotation)

## Overview
This document provides the complete API contract for the Log Management endpoints with **daily log rotation and pagination support**. These APIs allow fetching and analyzing server logs by date with comprehensive statistics, filtering capabilities, and efficient pagination.

## 🔄 Log File Rotation System
The system now implements **automatic daily log file rotation** with the following features:
- **Naming Convention**: `DD-MMM-YY-api.log` (e.g., `14-jun-25-api.log`)
- **Daily Rotation**: New log file created automatically each day
- **JSON Format**: Each log entry is a complete JSON object
- **Automatic File Discovery**: Existing endpoints automatically find the correct log file based on the date parameter

## Base URL
```
http://localhost:3002/api
```

## Authentication
⚠️ **Important**: These endpoints are currently **unauthenticated** for debugging purposes and only work in **production environment**.

## Endpoints

### 1. Get Available Log Dates

**Endpoint:** `GET /logs/dates`

**Description:** Returns all available dates that have log data. Now automatically discovers dates from the new DD-MMM-YY-api.log file format.

**Request:**
```http
GET /api/logs/dates
```

**Response:**
```typescript
interface AvailableLogDatesResponse {
  success: boolean;
  message: string;
  data: string[]; // Array of dates in YYYY-MM-DD format (most recent first)
}
```

**Example Response:**
```json
{
  "success": true,
  "message": "Found 3 days with log data",
  "data": [
    "2025-06-13",
    "2025-06-12",
    "2025-06-11"
  ]
}
```

---

### 2. Get Logs by Date (Paginated) ⭐ **UPDATED**

**Endpoint:** `GET /logs/by-date`

**Description:** Fetches log entries for a specific date with **pagination and advanced filtering**. This is the primary endpoint for log viewing. Now automatically finds the correct DD-MMM-YY-api.log file based on the date parameter.

**Request:**
```http
GET /api/logs/by-date?date=YYYY-MM-DD&page=1&pageSize=50&method=GET&statusCode=200&hasError=false&url=api&ip=192.168
```

**Query Parameters:**
| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `date` | string | Yes | - | Date in YYYY-MM-DD format |
| `page` | number | No | 1 | Page number (starts from 1) |
| `pageSize` | number | No | 50 | Number of logs per page (max 1000) |
| `method` | string | No | - | Filter by HTTP method (GET, POST, PUT, DELETE, etc.) |
| `statusCode` | number | No | - | Filter by HTTP status code (200, 404, 500, etc.) |
| `hasError` | boolean | No | - | Filter by error status (true/false) |
| `url` | string | No | - | Filter by URL containing this text |
| `ip` | string | No | - | Filter by IP address containing this text |

**Response:**
```typescript
interface PaginatedLogsByDateResponse {
  success: boolean;
  message: string;
  data: {
    logs: IMorganLog[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalData: number;
      pageSize: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
    filters: {
      date: string;
      method?: string;
      statusCode?: number;
      hasError?: boolean;
      url?: string;
      ip?: string;
    };
  };
}

interface IMorganLog {
  timestamp: string; // ISO 8601 timestamp
  id: string; // Unique UUID for the log entry
  ip: string; // Client IP address
  request: {
    timestamp: string;
    method: string; // HTTP method (GET, POST, etc.)
    url: string; // Request URL
    headers: Record<string, any>; // Request headers
    body: any; // Request body (if any)
  };
  response: {
    status: number; // HTTP status code
    headers: Record<string, any>; // Response headers
    body: any; // Response body
    responseTime: string; // Response time in "XXXms" format
  };
  error: false | {
    message: string;
    name?: string;
    stack?: string;
    statusCode?: number;
    timestamp?: string;
    details?: any;
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "message": "Found 150 log entries for 2025-06-13 (showing page 1 of 3)",
  "data": {
    "logs": [
      {
        "timestamp": "2025-06-13T13:24:47.806Z",
        "id": "fbe4239d-9439-4a4d-b27c-04520de3b62e",
        "ip": "::1",
        "request": {
          "timestamp": "2025-06-13T13:24:47.800Z",
          "method": "GET",
          "url": "/api/logs/dates",
          "headers": {
            "host": "localhost:3002",
            "user-agent": "curl/8.7.1",
            "accept": "*/*"
          },
          "body": {}
        },
        "response": {
          "status": 200,
          "headers": {
            "x-powered-by": "Express",
            "content-type": "application/json; charset=utf-8",
            "content-length": "77"
          },
          "body": "{\"success\":true,\"message\":\"Found 1 days with log data\",\"data\":[\"2025-06-13\"]}",
          "responseTime": "6ms"
        },
        "error": false
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalData": 150,
      "pageSize": 50,
      "hasNextPage": true,
      "hasPreviousPage": false
    },
    "filters": {
      "date": "2025-06-13",
      "method": "GET",
      "statusCode": 200,
      "hasError": false,
      "url": "api",
      "ip": null
    }
  }
}
```

---

### 3. Get All Logs by Date (Legacy)

**Endpoint:** `GET /logs/by-date/all`

**Description:** Fetches ALL log entries for a specific date without pagination. **Use with caution** for large datasets. Now automatically finds the correct DD-MMM-YY-api.log file based on the date parameter.

**Request:**
```http
GET /api/logs/by-date/all?date=YYYY-MM-DD
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `date` | string | Yes | Date in YYYY-MM-DD format |

**Response:** Same as original `IMorganLog[]` array format.

---

### 4. Get Log File Statistics

**Endpoint:** `GET /logs/file-stats`

**Description:** Returns comprehensive statistics about log files including file sizes, counts, warnings, and management recommendations. This endpoint helps monitor disk usage and file management.

**Request:**
```http
GET /api/logs/file-stats
```

**Query Parameters:** None

**Response:**
```typescript
interface LogFileStatsResponse {
  success: boolean;
  message: string;
  data: {
    summary: {
      totalFiles: number;
      totalSizeMB: string;
      totalSizeGB: string;
      largestFileMB: number;
      oldestFile: string | null;
      newestFile: string | null;
    };
    files: Array<{
      name: string;
      sizeMB: string;
      sizeBytes: number;
      created: string; // ISO 8601 timestamp
      modified: string; // ISO 8601 timestamp
      isLarge: boolean; // true if file > 50MB
    }>;
    warnings: string[]; // Array of warning messages
    recommendations: string[]; // Array of management recommendations
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "message": "Log file statistics retrieved successfully",
  "data": {
    "summary": {
      "totalFiles": 3,
      "totalSizeMB": "0.67",
      "totalSizeGB": "0.001",
      "largestFileMB": 0.4,
      "oldestFile": "14-jun-25-api.log",
      "newestFile": "17-jun-25-api.log"
    },
    "files": [
      {
        "name": "17-jun-25-api.log",
        "sizeMB": "0.40",
        "sizeBytes": 422383,
        "created": "2025-06-17T09:07:21.306Z",
        "modified": "2025-06-17T09:39:30.574Z",
        "isLarge": false
      },
      {
        "name": "15-jun-25-api.log",
        "sizeMB": "0.00",
        "sizeBytes": 2423,
        "created": "2025-06-15T12:03:34.866Z",
        "modified": "2025-06-15T12:03:34.867Z",
        "isLarge": false
      },
      {
        "name": "14-jun-25-api.log",
        "sizeMB": "0.26",
        "sizeBytes": 274663,
        "created": "2025-06-14T11:51:06.032Z",
        "modified": "2025-06-14T12:00:13.572Z",
        "isLarge": false
      }
    ],
    "warnings": [
      "2 file(s) exceed 50MB",
      "Total log size exceeds 1GB",
      "3 file(s) older than 30 days"
    ],
    "recommendations": [
      "Consider implementing log compression for files older than 7 days",
      "Archive logs older than 30 days to external storage",
      "Monitor disk space regularly",
      "Set up alerts for files exceeding 100MB"
    ]
  }
}
```

**Warning Types:**
- **Large Files**: Files exceeding 50MB
- **Total Size**: When total log size exceeds 1GB
- **Old Files**: Files older than 30 days

**Use Cases:**
- Monitor disk space usage
- Identify files that need archiving or compression
- Get recommendations for log management
- Track file growth patterns
- Plan storage capacity

---

### 5. Get Log Statistics by Date

**Endpoint:** `GET /logs/stats-by-date`

**Description:** Returns comprehensive statistics for logs on a specific date.

**Request:**
```http
GET /api/logs/stats-by-date?date=YYYY-MM-DD
```

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `date` | string | Yes | Date in YYYY-MM-DD format |

**Response:**
```typescript
interface LogStatisticsResponse {
  success: boolean;
  message: string;
  data: {
    date: string;
    totalRequests: number;
    requestsByMethod: Record<string, number>; // e.g., {"GET": 150, "POST": 25}
    requestsByStatus: Record<string, number>; // e.g., {"200": 120, "404": 5, "500": 2}
    errorCount: number;
    averageResponseTime: number; // in milliseconds (not string)
    slowestRequests: Array<{
      url: string;
      method: string;
      responseTime: number;
      timestamp: string;
    }>;
    topIPs: Array<{
      ip: string;
      count: number;
    }>;
    errorDistribution: Record<string, number>; // Error types and their counts
    timeRange: {
      start: string; // ISO 8601 timestamp
      end: string; // ISO 8601 timestamp
    };
  };
}
```

**Example Response:**
```json
{
  "success": true,
  "message": "Statistics retrieved successfully",
  "data": {
    "date": "2025-06-14",
    "totalRequests": 25,
    "requestsByMethod": {
      "GET": 20,
      "POST": 4,
      "PUT": 1
    },
    "requestsByStatus": {
      "200": 22,
      "401": 1,
      "404": 1,
      "500": 1
    },
    "errorCount": 3,
    "averageResponseTime": 3776,
    "slowestRequests": [
      {
        "url": "/api/users/getByFirebaseToken",
        "method": "GET",
        "responseTime": 5000,
        "timestamp": "2025-06-14T06:53:42.338Z"
      }
    ],
    "topIPs": [
      {
        "ip": "::ffff:127.0.0.1",
        "count": 15
      },
      {
        "ip": "*************",
        "count": 10
      }
    ],
    "errorDistribution": {
      "AuthenticationError": 1,
      "NotFoundError": 1,
      "InternalServerError": 1
    },
    "timeRange": {
      "start": "2025-06-14T00:00:00.000Z",
      "end": "2025-06-14T23:59:59.999Z"
    }
  }
}
```
```

---

## Frontend Implementation Guide

### React Hook with Pagination

```typescript
import { useState, useEffect, useCallback } from 'react';

interface LogFilters {
  method?: string;
  statusCode?: number;
  hasError?: boolean;
  url?: string;
  ip?: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalData: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface LogsHook {
  availableDates: string[];
  logs: IMorganLog[];
  pagination: PaginationInfo | null;
  statistics: LogStatisticsResponse['data'] | null;
  fileStats: LogFileStatsResponse['data'] | null;
  loading: boolean;
  error: string | null;
  filters: LogFilters;
  
  // Actions
  fetchAvailableDates: () => Promise<void>;
  fetchLogsByDate: (date: string, page?: number, pageSize?: number, filters?: LogFilters) => Promise<void>;
  fetchStatsByDate: (date: string) => Promise<void>;
  fetchFileStats: () => Promise<void>;
  setFilters: (filters: LogFilters) => void;
  nextPage: () => void;
  previousPage: () => void;
  goToPage: (page: number) => void;
}

export const useLogs = (): LogsHook => {
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [logs, setLogs] = useState<IMorganLog[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [statistics, setStatistics] = useState<LogStatisticsResponse['data'] | null>(null);
  const [fileStats, setFileStats] = useState<LogFileStatsResponse['data'] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<LogFilters>({});
  const [currentDate, setCurrentDate] = useState<string>('');

  const API_BASE = 'http://localhost:3002/api';

  const fetchAvailableDates = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE}/logs/dates`);
      const data: AvailableLogDatesResponse = await response.json();
      
      if (data.success) {
        setAvailableDates(data.data);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Failed to fetch available dates');
    } finally {
      setLoading(false);
    }
  };

  const fetchLogsByDate = async (
    date: string, 
    page: number = 1, 
    pageSize: number = 50, 
    customFilters?: LogFilters
  ) => {
    setLoading(true);
    setError(null);
    setCurrentDate(date);
    
    try {
      const activeFilters = customFilters || filters;
      const params = new URLSearchParams({
        date,
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...(activeFilters.method && { method: activeFilters.method }),
        ...(activeFilters.statusCode && { statusCode: activeFilters.statusCode.toString() }),
        ...(activeFilters.hasError !== undefined && { hasError: activeFilters.hasError.toString() }),
        ...(activeFilters.url && { url: activeFilters.url }),
        ...(activeFilters.ip && { ip: activeFilters.ip })
      });

      const response = await fetch(`${API_BASE}/logs/by-date?${params}`);
      const data: PaginatedLogsByDateResponse = await response.json();
      
      if (data.success) {
        setLogs(data.data.logs);
        setPagination(data.data.pagination);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Failed to fetch logs');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatsByDate = async (date: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE}/logs/stats-by-date?date=${date}`);
      const data: LogStatisticsResponse = await response.json();
      
      if (data.success) {
        setStatistics(data.data);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Failed to fetch statistics');
    } finally {
      setLoading(false);
    }
  };

  const fetchFileStats = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`${API_BASE}/logs/file-stats`);
      const data: LogFileStatsResponse = await response.json();
      
      if (data.success) {
        setFileStats(data.data);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Failed to fetch file statistics');
    } finally {
      setLoading(false);
    }
  };

  const nextPage = useCallback(() => {
    if (pagination?.hasNextPage && currentDate) {
      fetchLogsByDate(currentDate, pagination.currentPage + 1, pagination.pageSize);
    }
  }, [pagination, currentDate, filters]);

  const previousPage = useCallback(() => {
    if (pagination?.hasPreviousPage && currentDate) {
      fetchLogsByDate(currentDate, pagination.currentPage - 1, pagination.pageSize);
    }
  }, [pagination, currentDate, filters]);

  const goToPage = useCallback((page: number) => {
    if (currentDate && pagination && page >= 1 && page <= pagination.totalPages) {
      fetchLogsByDate(currentDate, page, pagination.pageSize);
    }
  }, [currentDate, pagination, filters]);

  const updateFilters = useCallback((newFilters: LogFilters) => {
    setFilters(newFilters);
    if (currentDate) {
      fetchLogsByDate(currentDate, 1, pagination?.pageSize || 50, newFilters);
    }
  }, [currentDate, pagination?.pageSize]);

  return {
    availableDates,
    logs,
    pagination,
    statistics,
    fileStats,
    loading,
    error,
    filters,
    fetchAvailableDates,
    fetchLogsByDate,
    fetchStatsByDate,
    fetchFileStats,
    setFilters: updateFilters,
    nextPage,
    previousPage,
    goToPage
  };
};
```

### React Component Example

```typescript
import React, { useState, useEffect } from 'react';
import { useLogs } from './hooks/useLogs';

const LogsDashboard: React.FC = () => {
  const {
    availableDates,
    logs,
    pagination,
    statistics,
    fileStats,
    loading,
    error,
    filters,
    fetchAvailableDates,
    fetchLogsByDate,
    fetchStatsByDate,
    fetchFileStats,
    setFilters,
    nextPage,
    previousPage,
    goToPage
  } = useLogs();

  const [selectedDate, setSelectedDate] = useState('');
  const [localFilters, setLocalFilters] = useState({
    method: '',
    statusCode: '',
    hasError: '',
    url: '',
    ip: ''
  });

  useEffect(() => {
    fetchAvailableDates();
    fetchFileStats(); // Load file stats on component mount
  }, []);

  const handleDateSelect = async (date: string) => {
    setSelectedDate(date);
    if (date) {
      await Promise.all([
        fetchLogsByDate(date, 1, 50),
        fetchStatsByDate(date)
      ]);
    }
  };

  const handleFilterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newFilters = {
      ...(localFilters.method && { method: localFilters.method }),
      ...(localFilters.statusCode && { statusCode: parseInt(localFilters.statusCode) }),
      ...(localFilters.hasError && { hasError: localFilters.hasError === 'true' }),
      ...(localFilters.url && { url: localFilters.url }),
      ...(localFilters.ip && { ip: localFilters.ip })
    };
    setFilters(newFilters);
  };

  const clearFilters = () => {
    setLocalFilters({
      method: '',
      statusCode: '',
      hasError: '',
      url: '',
      ip: ''
    });
    setFilters({});
  };

  return (
    <div className="logs-dashboard">
      <h1>Server Logs Dashboard</h1>
      
      {error && (
        <div className="error-banner">
          <p>Error: {error}</p>
        </div>
      )}

      {/* Date Selector */}
      <div className="date-selector">
        <label>Select Date:</label>
        <select 
          value={selectedDate} 
          onChange={(e) => handleDateSelect(e.target.value)}
          disabled={loading}
        >
          <option value="">Choose a date...</option>
          {availableDates.map(date => (
            <option key={date} value={date}>{date}</option>
          ))}
        </select>
      </div>

      {/* Filters */}
      {selectedDate && (
        <div className="filters-section">
          <h3>Filters</h3>
          <form onSubmit={handleFilterSubmit} className="filters-form">
            <div className="filter-row">
              <select
                value={localFilters.method}
                onChange={(e) => setLocalFilters({...localFilters, method: e.target.value})}
              >
                <option value="">All Methods</option>
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
                <option value="PATCH">PATCH</option>
              </select>

              <input
                type="number"
                placeholder="Status Code"
                value={localFilters.statusCode}
                onChange={(e) => setLocalFilters({...localFilters, statusCode: e.target.value})}
              />

              <select
                value={localFilters.hasError}
                onChange={(e) => setLocalFilters({...localFilters, hasError: e.target.value})}
              >
                <option value="">All Requests</option>
                <option value="true">Errors Only</option>
                <option value="false">Success Only</option>
              </select>

              <input
                type="text"
                placeholder="URL contains..."
                value={localFilters.url}
                onChange={(e) => setLocalFilters({...localFilters, url: e.target.value})}
              />

              <input
                type="text"
                placeholder="IP contains..."
                value={localFilters.ip}
                onChange={(e) => setLocalFilters({...localFilters, ip: e.target.value})}
              />
            </div>
            
            <div className="filter-actions">
              <button type="submit" disabled={loading}>Apply Filters</button>
              <button type="button" onClick={clearFilters} disabled={loading}>Clear</button>
            </div>
          </form>
        </div>
      )}

      {/* File Statistics */}
      {fileStats && (
        <div className="file-statistics">
          <h2>Log File Management</h2>
          <div className="stats-grid">
            <div className="stat-card">
              <h3>Total Files</h3>
              <p>{fileStats.summary.totalFiles}</p>
            </div>
            <div className="stat-card">
              <h3>Total Size</h3>
              <p>{fileStats.summary.totalSizeMB} MB</p>
            </div>
            <div className="stat-card">
              <h3>Largest File</h3>
              <p>{fileStats.summary.largestFileMB} MB</p>
            </div>
          </div>
          
          {fileStats.warnings.length > 0 && (
            <div className="warnings-section">
              <h3>⚠️ Warnings</h3>
              <ul>
                {fileStats.warnings.map((warning, index) => (
                  <li key={index} className="warning-item">{warning}</li>
                ))}
              </ul>
            </div>
          )}
          
          <div className="recommendations-section">
            <h3>💡 Recommendations</h3>
            <ul>
              {fileStats.recommendations.map((rec, index) => (
                <li key={index} className="recommendation-item">{rec}</li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Statistics */}
      {statistics && (
        <div className="statistics">
          <h2>Statistics for {statistics.date}</h2>
          <div className="stats-grid">
            <div className="stat-card">
              <h3>Total Requests</h3>
              <p>{statistics.totalRequests}</p>
            </div>
            <div className="stat-card">
              <h3>Error Count</h3>
              <p>{statistics.errorCount}</p>
            </div>
            <div className="stat-card">
              <h3>Avg Response Time</h3>
              <p>{statistics.averageResponseTime}ms</p>
            </div>
          </div>
        </div>
      )}

      {/* Pagination Info */}
      {pagination && (
        <div className="pagination-info">
          <p>
            Showing {((pagination.currentPage - 1) * pagination.pageSize) + 1} to{' '}
            {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalData)} of{' '}
            {pagination.totalData} entries
          </p>
        </div>
      )}

      {/* Logs Table */}
      {logs.length > 0 && (
        <div className="logs-table">
          <h2>Log Entries</h2>
          <table>
            <thead>
              <tr>
                <th>Timestamp</th>
                <th>Method</th>
                <th>URL</th>
                <th>Status</th>
                <th>Response Time</th>
                <th>IP</th>
                <th>Error</th>
              </tr>
            </thead>
            <tbody>
              {logs.map(log => (
                <tr key={log.id}>
                  <td>{new Date(log.timestamp).toLocaleString()}</td>
                  <td>
                    <span className={`method-badge method-${log.request.method.toLowerCase()}`}>
                      {log.request.method}
                    </span>
                  </td>
                  <td className="url-cell" title={log.request.url}>
                    {log.request.url.length > 50 
                      ? `${log.request.url.substring(0, 50)}...` 
                      : log.request.url}
                  </td>
                  <td>
                    <span className={`status-badge ${log.response.status >= 400 ? 'error' : 'success'}`}>
                      {log.response.status}
                    </span>
                  </td>
                  <td>{log.response.responseTime}</td>
                  <td>{log.ip}</td>
                  <td>{log.error ? '❌' : '✅'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Pagination Controls */}
      {pagination && pagination.totalPages > 1 && (
        <div className="pagination-controls">
          <button 
            onClick={previousPage} 
            disabled={!pagination.hasPreviousPage || loading}
          >
            Previous
          </button>
          
          <div className="page-numbers">
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              const pageNum = Math.max(1, pagination.currentPage - 2) + i;
              if (pageNum <= pagination.totalPages) {
                return (
                  <button
                    key={pageNum}
                    onClick={() => goToPage(pageNum)}
                    className={pageNum === pagination.currentPage ? 'active' : ''}
                    disabled={loading}
                  >
                    {pageNum}
                  </button>
                );
              }
              return null;
            })}
          </div>
          
          <button 
            onClick={nextPage} 
            disabled={!pagination.hasNextPage || loading}
          >
            Next
          </button>
          
          <span className="page-info">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
        </div>
      )}

      {loading && (
        <div className="loading-overlay">
          <p>Loading...</p>
        </div>
      )}
    </div>
  );
};

export default LogsDashboard;
```

### CSS Styles

```css
.logs-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.error-banner {
  background-color: #fee;
  border: 1px solid #fcc;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.date-selector {
  margin-bottom: 20px;
}

.date-selector select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.filters-section {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.filters-form .filter-row {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.filters-form input,
.filters-form select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 120px;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.filter-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.filter-actions button[type="submit"] {
  background-color: #007bff;
  color: white;
}

.filter-actions button[type="button"] {
  background-color: #6c757d;
  color: white;
}

.file-statistics,
.statistics {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 10px;
}

.warnings-section,
.recommendations-section {
  margin-top: 20px;
  padding: 15px;
  border-radius: 8px;
}

.warnings-section {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
}

.recommendations-section {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
}

.warnings-section h3,
.recommendations-section h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.warning-item {
  color: #856404;
  margin-bottom: 5px;
}

.recommendation-item {
  color: #0c5460;
  margin-bottom: 5px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.stat-card p {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.pagination-info {
  margin-bottom: 10px;
  color: #666;
  font-size: 14px;
}

.logs-table {
  margin-bottom: 20px;
}

.logs-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logs-table th,
.logs-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.logs-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.method-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.method-get { background-color: #d4edda; color: #155724; }
.method-post { background-color: #cce5ff; color: #004085; }
.method-put { background-color: #fff3cd; color: #856404; }
.method-delete { background-color: #f8d7da; color: #721c24; }

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status-badge.success {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.error {
  background-color: #f8d7da;
  color: #721c24;
}

.url-cell {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.pagination-controls button {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.pagination-controls button:hover:not(:disabled) {
  background-color: #f8f9fa;
}

.pagination-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-controls button.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

.page-info {
  color: #666;
  font-size: 14px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

@media (max-width: 768px) {
  .filters-form .filter-row {
    flex-direction: column;
  }
  
  .logs-table {
    overflow-x: auto;
  }
  
  .pagination-controls {
    flex-wrap: wrap;
  }
}
```

## Error Handling

### Common Error Responses

```typescript
interface ErrorResponse {
  success: false;
  message: string;
  data: null;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}
```

### Error Codes and Messages

| Status Code | Message | Description |
|-------------|---------|-------------|
| 400 | "Date parameter is required (format: YYYY-MM-DD)" | Missing date parameter |
| 400 | "Invalid date format. Use YYYY-MM-DD" | Incorrect date format |
| 400 | "Page must be a positive number" | Invalid page parameter |
| 400 | "Page size must be between 1 and 1000" | Invalid pageSize parameter |
| 400 | "Invalid status code" | Invalid statusCode filter |
| 403 | "This endpoint is only available in production environment" | Environment restriction |
| 404 | "Logs directory not found" | No logs directory exists |
| 404 | "No log files found for date: YYYY-MM-DD" | No logs for specified date |
| 500 | "Internal server error" | Server error |

## Performance Considerations

1. **Default Page Size**: 50 entries per page (configurable up to 1000)
2. **Filtering**: Applied before pagination for accurate counts
3. **Sorting**: Logs are sorted by timestamp (most recent first)
4. **Memory Usage**: Only requested page is loaded into memory
5. **File Parsing**: Log files are parsed once per request (consider caching for production)

## Rate Limiting

- **Limit**: 200 requests per minute per IP
- **Headers**: 
  - `x-ratelimit-limit`: Maximum requests allowed
  - `x-ratelimit-remaining`: Remaining requests
  - `x-ratelimit-reset`: Reset timestamp

## Best Practices

1. **Pagination**: Always use pagination for large datasets
2. **Filtering**: Apply filters to reduce data transfer
3. **Error Handling**: Implement proper error boundaries and user feedback
4. **Loading States**: Show loading indicators during API calls
5. **Caching**: Consider caching available dates and statistics
6. **Performance**: Use debouncing for rapid filter changes
7. **Accessibility**: Ensure proper ARIA labels and keyboard navigation
8. **Mobile**: Implement responsive design for mobile devices

## Testing

### cURL Examples

```bash
# Get available dates
curl "http://localhost:3002/api/logs/dates"

# Get first page of logs for specific date
curl "http://localhost:3002/api/logs/by-date?date=2025-06-13"

# Get second page with 25 entries per page
curl "http://localhost:3002/api/logs/by-date?date=2025-06-13&page=2&pageSize=25"

# Filter by GET requests only
curl "http://localhost:3002/api/logs/by-date?date=2025-06-13&method=GET"

# Filter by status code 404
curl "http://localhost:3002/api/logs/by-date?date=2025-06-13&statusCode=404"

# Filter by errors only
curl "http://localhost:3002/api/logs/by-date?date=2025-06-13&hasError=true"

# Filter by URL containing 'api'
curl "http://localhost:3002/api/logs/by-date?date=2025-06-13&url=api"

# Complex filter example
curl "http://localhost:3002/api/logs/by-date?date=2025-06-13&page=1&pageSize=20&method=POST&statusCode=500&hasError=true"

# Get statistics for specific date
curl "http://localhost:3002/api/logs/stats-by-date?date=2025-06-13"

# Get log file statistics and management info
curl "http://localhost:3002/api/logs/file-stats"

# Test error handling - invalid date format
curl "http://localhost:3002/api/logs/by-date?date=invalid-date"

# Test error handling - invalid page
curl "http://localhost:3002/api/logs/by-date?date=2025-06-13&page=0"
```

## Environment Requirements

- **Environment**: Production only (`WorkingENV === APP_ENV.prod`)
- **Server**: Node.js with Express
- **Port**: 3002
- **CORS**: Enabled for all origins (`*`)

---

**Last Updated**: June 13, 2025  
**API Version**: 2.0 (with Pagination)  
**Contact**: Backend Development Team 